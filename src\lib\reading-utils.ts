import { prisma } from './db';

// Get API key from environment variables
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

if (!OPENAI_API_KEY) {
  throw new Error('OPENAI_API_KEY environment variable is not set');
}

/**
 * Compare user answers with correct answers
 *
 * @param userAnswers - The user's answers
 * @param correctAnswers - The correct answers
 * @returns Object containing score, band, and mistakes
 */
export function compareAnswers(userAnswers: string[], correctAnswers: string[]) {
  // Normalize answers to uppercase for case-insensitive comparison
  const normalizedUserAnswers = userAnswers.map(a => a?.trim().toUpperCase() || '');
  const normalizedCorrectAnswers = correctAnswers.map(a => a?.trim().toUpperCase() || '');

  // Count correct answers
  let correctCount = 0;
  const mistakes = [];

  // Compare each answer
  for (let i = 0; i < normalizedCorrectAnswers.length; i++) {
    const userAnswer = normalizedUserAnswers[i] || '';
    const correctAnswer = normalizedCorrectAnswers[i] || '';

    // Handle special cases for True/False/Not Given
    let isCorrect = false;

    // Check for True/T/TRUE variations
    if ((correctAnswer === 'TRUE' || correctAnswer === 'T') &&
        (userAnswer === 'TRUE' || userAnswer === 'T')) {
      isCorrect = true;
    }
    // Check for False/F/FALSE variations
    else if ((correctAnswer === 'FALSE' || correctAnswer === 'F') &&
             (userAnswer === 'FALSE' || userAnswer === 'F')) {
      isCorrect = true;
    }
    // Check for Not Given/NG/NOT GIVEN variations
    else if ((correctAnswer === 'NOT GIVEN' || correctAnswer === 'NG' || correctAnswer === 'NOTGIVEN') &&
             (userAnswer === 'NOT GIVEN' || userAnswer === 'NG' || userAnswer === 'NOTGIVEN')) {
      isCorrect = true;
    }
    // Standard case-insensitive comparison for other answers
    else if (userAnswer === correctAnswer) {
      isCorrect = true;
    }

    if (isCorrect) {
      correctCount++;
    } else {
      mistakes.push({
        questionNumber: i + 1,
        userAnswer: userAnswers[i] || '',
        correctAnswer: correctAnswers[i] || '',
        explanation: `The correct answer is ${correctAnswers[i]}.`,
        advice: 'Review this question type carefully.'
      });
    }
  }

  // Calculate score and band
  const totalQuestions = normalizedCorrectAnswers.length;
  const score = totalQuestions > 0 ? Math.round((correctCount / totalQuestions) * 40) : 0;

  // Calculate band score based on IELTS reading band score conversion table
  let band = 0;
  if (score >= 39) band = 9.0;
  else if (score >= 37) band = 8.5;
  else if (score >= 35) band = 8.0;
  else if (score >= 33) band = 7.5;
  else if (score >= 30) band = 7.0;
  else if (score >= 27) band = 6.5;
  else if (score >= 23) band = 6.0;
  else if (score >= 20) band = 5.5;
  else if (score >= 16) band = 5.0;
  else if (score >= 13) band = 4.5;
  else if (score >= 10) band = 4.0;
  else if (score >= 8) band = 3.5;
  else if (score >= 6) band = 3.0;
  else if (score >= 4) band = 2.5;
  else if (score >= 2) band = 2.0;
  else band = 1.0;

  return {
    score,
    band,
    correctAnswers: correctCount,
    totalQuestions,
    mistakes
  };
}

/**
 * Process a reading entry and generate feedback
 *
 * @param entry - The reading entry to process
 * @param model - The AI model to use for processing
 * @returns The processed reading entry
 */
export async function processReadingEntry(entry: any, model: string) {
  try {
    console.log('Processing reading entry:', entry.id);

    // Get the user's answers and correct answers
    const userAnswers = entry.answers || [];

    // Get correct answers from the entry directly (global correct answers) or material
    let correctAnswers: string[] = [];

    // First check if entry has correctAnswersText field (global correct answers)
    if (entry.correctAnswersText) {
      console.log('Global correct answers found:', entry.correctAnswersText);

      if (typeof entry.correctAnswersText === 'string') {
        // Handle case where answers are a comma-separated string
        correctAnswers = entry.correctAnswersText.split(',').map((a: string) => a.trim());
      } else if (Array.isArray(entry.correctAnswersText)) {
        correctAnswers = entry.correctAnswersText;
      }

      console.log('Processed global correct answers:', correctAnswers);
    }
    // Fallback to material answers if no global answers are provided
    else if (entry.material && entry.material.answers) {
      console.log('Falling back to reading material answers:', entry.material.answers);

      // If the material has answers, use those
      correctAnswers = Array.isArray(entry.material.answers)
        ? entry.material.answers
        : Object.values(entry.material.answers);

      console.log('Processed material correct answers:', correctAnswers);
    }

    console.log('User answers:', userAnswers);
    console.log('Correct answers:', correctAnswers);

    // Compare answers and get results
    const {
      score,
      band,
      correctAnswers: correctCount,
      totalQuestions,
      mistakes
    } = compareAnswers(userAnswers, correctAnswers);

    // Prepare data for OpenAI analysis
    const userAnswersStr = userAnswers.map((ans: string, i: number) => `${i + 1}. ${ans}`).join('\n');
    const correctAnswersStr = correctAnswers.map((ans: string, i: number) => `${i + 1}. ${ans}`).join('\n');
    const mistakesStr = mistakes.map((m: any) =>
      `Question ${m.questionNumber}: User answered "${m.userAnswer}" but correct answer is "${m.correctAnswer}"`
    ).join('\n');

    // Generate AI-powered analysis
    let aiAnalysis = null;

    try {
      console.log('Generating AI analysis for reading entry...');

      // Create prompt for OpenAI
      const prompt = `
      You are an expert IELTS Reading examiner providing detailed feedback.

      Student's Reading test results:
      - Score: ${score}/40
      - Band: ${band.toFixed(1)}
      - Correct answers: ${correctCount}/${totalQuestions}
      - Percentage correct: ${(correctCount / totalQuestions * 100).toFixed(1)}%

      User's answers:
      ${userAnswersStr}

      Correct answers:
      ${correctAnswersStr}

      Mistakes:
      ${mistakesStr}

      Based on this information, provide a detailed analysis of the student's reading performance.
      Include specific strengths, weaknesses, and improvement suggestions.
      Format your response as a JSON object with the following structure:
      {
        "analysis": "Detailed analysis of the student's performance",
        "strengths": ["Strength 1", "Strength 2", ...],
        "weaknesses": ["Weakness 1", "Weakness 2", ...],
        "improvementSuggestions": ["Suggestion 1", "Suggestion 2", ...],
        "criteriaScores": [
          {"name": "Section 1 (Social/Training)", "score": "X/Y", "feedback": "Feedback for section 1"},
          {"name": "Section 2 (Workplace)", "score": "X/Y", "feedback": "Feedback for section 2"},
          {"name": "Section 3 (Academic)", "score": "X/Y", "feedback": "Feedback for section 3"}
        ]
      }
      `;

      // Call OpenAI API
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENAI_API_KEY}`
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: 'system',
              content: 'You are an expert IELTS Reading examiner providing detailed feedback.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 2000,
          response_format: { type: "json_object" }
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Failed to generate AI analysis');
      }

      const data = await response.json();
      const content = data.choices[0].message.content;

      // Parse the AI response
      aiAnalysis = JSON.parse(content);
      console.log('AI analysis generated successfully');

    } catch (aiError) {
      console.error('Error generating AI analysis:', aiError);

      // Fallback to basic analysis if AI fails
      aiAnalysis = {
        analysis: "Analysis could not be generated with AI. Basic analysis provided instead.",
        strengths: [] as string[],
        weaknesses: [] as string[],
        improvementSuggestions: [] as string[],
        criteriaScores: [] as any[]
      };

      // Calculate percentage correct
      const percentCorrect = totalQuestions > 0 ? (correctCount / totalQuestions) * 100 : 0;

      if (percentCorrect >= 80) {
        aiAnalysis.strengths.push("Excellent understanding of reading passages");
        aiAnalysis.strengths.push("Strong ability to identify key information");
      } else if (percentCorrect >= 60) {
        aiAnalysis.strengths.push("Good understanding of main ideas");
        aiAnalysis.strengths.push("Accurate detail identification");
      } else {
        aiAnalysis.weaknesses.push("Difficulty understanding main ideas");
        aiAnalysis.weaknesses.push("Struggles with identifying key details");
      }

      // Add more specific feedback based on mistakes
      if (mistakes.length > 0) {
        aiAnalysis.weaknesses.push("Difficulty with specific question types");
      }

      aiAnalysis.improvementSuggestions = [
        "Practice reading a variety of text types",
        "Improve skimming and scanning techniques",
        "Focus on understanding the overall structure of passages"
      ];
    }

    // Extract data from AI analysis
    const strengths = aiAnalysis.strengths || [];
    const weaknesses = aiAnalysis.weaknesses || [];
    const improvementSuggestions = aiAnalysis.improvementSuggestions || [];
    const criteriaScores = aiAnalysis.criteriaScores || [];

    // Update the entry in the database
    // Create update data without criteriaScores first
    const updateData = {
      score,
      band,
      correctAnswers: correctCount, // Store as integer, not array
      totalQuestions,
      mistakes: JSON.stringify(mistakes),
      strengths: JSON.stringify(strengths),
      weaknesses: JSON.stringify(weaknesses),
      improvementSuggestions: JSON.stringify(improvementSuggestions)
    };

    // Store criteriaScores in a variable for logging
    const criteriaScoresJson = JSON.stringify(criteriaScores);
    console.log('Generated criteriaScores for reading:', criteriaScoresJson);

    // Update the entry without criteriaScores field
    const updatedEntry = await prisma.readingEntry.update({
      where: { id: entry.id },
      data: updateData
    });

    return updatedEntry;
  } catch (error) {
    console.error('Error processing reading entry:', error);
    throw error;
  }
}

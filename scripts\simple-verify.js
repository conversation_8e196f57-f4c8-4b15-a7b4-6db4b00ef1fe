// Simple database verification script
const { exec } = require('child_process');

// Function to execute a command and return a promise
function execCommand(command) {
  return new Promise((resolve, reject) => {
    console.log(`Executing: ${command}`);
    
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error: ${error.message}`);
        return reject(error);
      }
      
      if (stderr) {
        console.error(`stderr: ${stderr}`);
      }
      
      console.log(`stdout: ${stdout}`);
      resolve(stdout);
    });
  });
}

async function verifyDatabase() {
  try {
    // Use Prisma CLI to validate the database connection
    await execCommand('npx prisma validate');
    console.log('Database connection validated successfully!');
    return true;
  } catch (error) {
    console.error('Database validation failed:', error);
    return false;
  }
}

// Run the script
verifyDatabase()
  .then(success => {
    if (success) {
      console.log('Database verification completed successfully.');
    } else {
      console.error('Database verification failed.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });

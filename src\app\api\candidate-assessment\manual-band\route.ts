import { NextRequest, NextResponse } from 'next/server';
import { createWritingEntryWithManualBand, updateWritingEntryWithManualBand } from '@/lib/writing-utils';
import { createSpeakingEntryWithManualBand, updateSpeakingEntryWithManualBand } from '@/lib/speaking-utils';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { entryId, componentType, studentId, band, taskType, taskQuestion, essayText, imageUrl, audioUrl, transcription, examinerContent } = body;

    console.log('Processing manual band input:', { componentType, entryId, studentId, band });

    if (!componentType) {
      return NextResponse.json({ error: 'Component type is required' }, { status: 400 });
    }

    if (!studentId && !entryId) {
      return NextResponse.json({ error: 'Student ID or Entry ID is required' }, { status: 400 });
    }

    if (band === undefined || band === null) {
      return NextResponse.json({ error: 'Band score is required' }, { status: 400 });
    }

    // Validate band score
    const bandScore = parseFloat(band);
    if (isNaN(bandScore) || bandScore < 0 || bandScore > 9) {
      return NextResponse.json({ error: 'Band score must be between 0 and 9' }, { status: 400 });
    }

    let result;

    // Process based on component type
    if (componentType === 'writing_task1' || componentType === 'writing_task2') {
      // Validate task type for writing
      if (!taskType && !entryId) {
        return NextResponse.json({ error: 'Task type is required for writing entries' }, { status: 400 });
      }

      if (entryId) {
        // Update existing entry
        result = await updateWritingEntryWithManualBand(
          entryId, 
          bandScore,
          taskType || (componentType === 'writing_task1' ? 'task1' : 'task2')
        );
      } else {
        // Create new entry
        result = await createWritingEntryWithManualBand({
          studentId,
          taskType: taskType || (componentType === 'writing_task1' ? 'task1' : 'task2'),
          band: bandScore,
          taskQuestion,
          essayText,
          imageUrl
        });
      }
    } else if (componentType === 'speaking') {
      if (entryId) {
        // Update existing entry
        result = await updateSpeakingEntryWithManualBand(entryId, bandScore);
      } else {
        // Create new entry
        result = await createSpeakingEntryWithManualBand({
          studentId,
          band: bandScore,
          audioUrl,
          transcription,
          examinerContent
        });
      }
    } else {
      return NextResponse.json({ error: 'Invalid component type for manual band input' }, { status: 400 });
    }

    return NextResponse.json({
      id: result.id,
      band: result.band,
      status: 'success'
    });
  } catch (error) {
    console.error('Error processing manual band input:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to process manual band input' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getOrCreateStudent } from '@/lib/student-utils';
import { createListeningEntry, calculateBandScore } from '@/lib/listening-entry-utils';
import { getListeningMaterialById } from '@/lib/listening-material-utils';
import OpenAI from 'openai';

// Define interfaces for type safety
interface AnswerMap {
  [key: string]: string | number | boolean;
}

// Define interface for correct answers
interface CorrectAnswersMap {
  [key: string]: string | number | boolean;
}

interface ListeningEntry {
  id: string;
  studentId: string;
  materialId: string;
  answers: AnswerMap;
  imageUrl?: string;
  metadata?: {
    globalCorrectAnswers?: string[] | string;
    [key: string]: any;
  };
}

// Get API key from environment variables
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

if (!OPENAI_API_KEY) {
  throw new Error('OPENAI_API_KEY environment variable is not set');
}

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: OPENAI_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    // Check if this is a FormData request (for image upload) or a JSON request
    let entries: ListeningEntry[] = [];
    let model = 'gpt-4o';
    let imageData = null;

    if (request.headers.get('content-type')?.includes('multipart/form-data')) {
      // Handle FormData request (image upload)
      const formData = await request.formData();
      const file = formData.get('image') as File;
      const studentId = formData.get('studentId') as string || `anonymous-${Date.now()}`;
      const materialId = formData.get('materialId') as string;

      if (!file) {
        return NextResponse.json({ error: 'No image file provided' }, { status: 400 });
      }

      if (!materialId) {
        return NextResponse.json({ error: 'Material ID is required' }, { status: 400 });
      }

      // Check if file is an image
      if (!file.type.startsWith('image/')) {
        return NextResponse.json({ error: 'File must be an image' }, { status: 400 });
      }

      // Convert file to base64
      const bytes = await file.arrayBuffer();
      const buffer = Buffer.from(bytes);
      const base64Image = buffer.toString('base64');

      // Get the listening material to extract correct answers format
      const material = await getListeningMaterialById(materialId);
      if (!material) {
        return NextResponse.json({ error: `Listening material with ID ${materialId} not found` }, { status: 404 });
      }

      // Extract text from image using OpenAI's vision capabilities
      try {
        const response = await openai.chat.completions.create({
          model: 'gpt-4o',
          messages: [
            {
              role: 'system',
              content: 'You are an expert OCR system specialized in extracting text from IELTS listening answer sheets. Your primary goal is to accurately identify and extract the answers written on the sheet. Format your response as a JSON object with question numbers as keys and the extracted answers as values.'
            },
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: `Extract the answers from this IELTS listening answer sheet image. The answer sheet contains responses to ${material.answers ? Object.keys(material.answers).length : 'multiple'} questions. Format your response as a JSON object with question numbers as keys and the extracted answers as values. For example: {"1": "Paris", "2": "1995", ...}. Only include the answers, not any explanations or other text.`
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: `data:${file.type};base64,${base64Image}`
                  }
                }
              ]
            }
          ],
          max_tokens: 4000,
          temperature: 0.0,
          response_format: { type: "json_object" }
        });

        // Parse the extracted answers
        const extractedContent = response.choices[0].message.content;
        let extractedAnswers: AnswerMap = {};

        try {
          // Make sure extractedContent is a string before parsing
          if (extractedContent) {
            extractedAnswers = JSON.parse(extractedContent) as AnswerMap;
          } else {
            console.error('No content returned from OpenAI');
            return NextResponse.json({ error: 'No content returned from OpenAI' }, { status: 500 });
          }
        } catch (e) {
          console.error('Error parsing extracted answers:', e);
          return NextResponse.json({ error: 'Failed to parse extracted answers' }, { status: 500 });
        }

        // Create an entry with the extracted answers
        entries = [{
          id: `image-${Date.now()}`,
          studentId,
          materialId,
          answers: extractedAnswers,
          imageUrl: `data:${file.type};base64,${base64Image}` // Store the image data
        }];
      } catch (error) {
        console.error('Error extracting text from image:', error);
        return NextResponse.json({
          error: 'Failed to extract text from image',
          details: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 });
      }
    } else {
      // Handle JSON request (regular bulk checking)
      const body = await request.json();
      entries = body.entries;
      model = body.model || 'gpt-4o';
    }

    if (!entries || !Array.isArray(entries) || entries.length === 0) {
      return NextResponse.json({ error: 'Entries array is required' }, { status: 400 });
    }

    if (!model) {
      return NextResponse.json({ error: 'Model is required' }, { status: 400 });
    }

    // Check if studentId and materialId are provided for each entry
    for (let i = 0; i < entries.length; i++) {
      if (!entries[i].studentId) {
        entries[i].studentId = `anonymous-${Date.now()}-${i}`; // Generate a temporary ID if not provided
      }

      if (!entries[i].materialId) {
        return NextResponse.json({
          error: `Material ID is required for entry ${i + 1}`
        }, { status: 400 });
      }
    }

    // Process each entry
    const results = await Promise.all(
      entries.map(async (entry) => {
        try {
          const { studentId, materialId, answers, imageUrl } = entry;

          // Get the listening material (still needed for question text)
          const material = await getListeningMaterialById(materialId);
          if (!material) {
            throw new Error(`Listening material with ID ${materialId} not found`);
          }

          // Check if global correct answers are provided in metadata
          let correctAnswersText = null;
          if (entry.metadata && typeof entry.metadata === 'object' && 'globalCorrectAnswers' in entry.metadata) {
            correctAnswersText = entry.metadata.globalCorrectAnswers;
          }

          // Get or create the student
          await getOrCreateStudent(studentId);

          // Validate answers format
          if (!answers || typeof answers !== 'object') {
            throw new Error('Answers must be provided as an object');
          }

          // Type assertion for answers to avoid TypeScript errors
          const typedAnswers = answers as AnswerMap;

          // Get correct answers from global answers if provided, otherwise from material
          let correctAnswers: CorrectAnswersMap;

          if (correctAnswersText) {
            // Parse the correct answers from the text
            correctAnswers = {};
            if (typeof correctAnswersText === 'string') {
              // Split by commas and create an object with sequential keys
              const answersArray = correctAnswersText.split(',').map(a => a.trim());
              answersArray.forEach((answer, index) => {
                correctAnswers[String(index + 1)] = answer;
              });
              console.log('Using global correct answers:', correctAnswers);
            } else if (Array.isArray(correctAnswersText)) {
              correctAnswersText.forEach((answer, index) => {
                correctAnswers[String(index + 1)] = answer;
              });
              console.log('Using global correct answers array:', correctAnswers);
            }
          } else {
            // Fall back to material answers
            correctAnswers = material.answers as CorrectAnswersMap;
            console.log('Using material correct answers:', correctAnswers);
          }

          if (!correctAnswers || Object.keys(correctAnswers).length === 0) {
            throw new Error('No correct answers found in global settings or material');
          }

          // Calculate score
          let score = 0;
          const mistakes = [];
          const questionIds = Object.keys(correctAnswers);
          const totalQuestions = questionIds.length;

          for (const questionId of questionIds) {
            const userAnswer = typedAnswers[questionId];
            const correctAnswer = correctAnswers[questionId];

            // Skip if user didn't provide an answer
            if (userAnswer === undefined) continue;

            // Compare answers (case-insensitive for text answers)
            let isCorrect = false;

            if (typeof userAnswer === 'string' && typeof correctAnswer === 'string') {
              const userAnswerUpper = userAnswer.toUpperCase().trim();
              const correctAnswerUpper = correctAnswer.toUpperCase().trim();

              // Handle special cases for True/False/Not Given
              // Check for True/T/TRUE variations
              if ((correctAnswerUpper === 'TRUE' || correctAnswerUpper === 'T') &&
                  (userAnswerUpper === 'TRUE' || userAnswerUpper === 'T')) {
                isCorrect = true;
              }
              // Check for False/F/FALSE variations
              else if ((correctAnswerUpper === 'FALSE' || correctAnswerUpper === 'F') &&
                       (userAnswerUpper === 'FALSE' || userAnswerUpper === 'F')) {
                isCorrect = true;
              }
              // Check for Not Given/NG/NOT GIVEN variations
              else if ((correctAnswerUpper === 'NOT GIVEN' || correctAnswerUpper === 'NG' || correctAnswerUpper === 'NOTGIVEN') &&
                       (userAnswerUpper === 'NOT GIVEN' || userAnswerUpper === 'NG' || userAnswerUpper === 'NOTGIVEN')) {
                isCorrect = true;
              }
              // Standard case-insensitive comparison
              else {
                isCorrect = userAnswerUpper === correctAnswerUpper;
              }
            } else {
              // For non-string values, use exact comparison
              isCorrect = userAnswer === correctAnswer;
            }

            if (isCorrect) {
              score++;
            } else {
              // Find the question text
              const questions = material.questions || [];
              const question = Array.isArray(questions) ?
                questions.find((q) => q && typeof q === 'object' && 'id' in q && q.id === questionId) :
                null;
              const questionText = question && typeof question === 'object' && 'text' in question ?
                question.text :
                `Question ${questionId}`;

              mistakes.push({
                questionId,
                questionText,
                userAnswer,
                correctAnswer,
              });
            }
          }

          // Calculate band score
          const band = calculateBandScore(score, totalQuestions);

          // Generate AI analysis for detailed feedback
          const aiPrompt = `
            You are an IELTS Listening examiner providing detailed feedback on a candidate's listening test.

            The candidate scored ${score} out of ${totalQuestions} questions correctly (Band ${band}).

            Here are the mistakes they made:
            ${mistakes.map(m => `- Question: ${m.questionText}\n  User answer: ${m.userAnswer}\n  Correct answer: ${m.correctAnswer}`).join('\n')}

            Based on this information, provide:
            1. An analysis of the candidate's listening skills
            2. Specific strengths (at least 3)
            3. Specific weaknesses (at least 3)
            4. Detailed improvement suggestions (at least 3)
            5. Explanation for each mistake (why the answer was wrong and how to approach it correctly)

            Format your response as a JSON object with the following structure:
            {
              "analysis": "Detailed analysis of the candidate's listening skills",
              "strengths": ["Strength 1", "Strength 2", "Strength 3"],
              "weaknesses": ["Weakness 1", "Weakness 2", "Weakness 3"],
              "improvementSuggestions": ["Suggestion 1", "Suggestion 2", "Suggestion 3"],
              "mistakeExplanations": {
                "questionId1": "Explanation for mistake 1",
                "questionId2": "Explanation for mistake 2"
              }
            }
          `;

          // Only call AI if there are mistakes to analyze or if the score is not perfect
          let aiAnalysis = null;
          if (mistakes.length > 0 || score < totalQuestions) {
            const aiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
              },
              body: JSON.stringify({
                model: model,
                messages: [
                  {
                    role: 'system',
                    content: 'You are an expert IELTS Listening examiner providing detailed feedback.',
                  },
                  {
                    role: 'user',
                    content: aiPrompt,
                  },
                ],
                temperature: 0.7,
              }),
            });

            if (!aiResponse.ok) {
              const errorData = await aiResponse.json();
              throw new Error(errorData.error?.message || 'Failed to generate AI analysis');
            }

            const aiData = await aiResponse.json();
            const aiContent = aiData.choices[0].message.content;

            try {
              aiAnalysis = JSON.parse(aiContent);
            } catch (e) {
              console.error('Error parsing AI response:', e);
              aiAnalysis = {
                analysis: 'Error parsing AI response',
                strengths: [],
                weaknesses: [],
                improvementSuggestions: [],
                mistakeExplanations: {},
              };
            }
          } else {
            // Perfect score
            aiAnalysis = {
              analysis: 'The candidate has demonstrated excellent listening skills, answering all questions correctly.',
              strengths: [
                'Excellent comprehension of spoken English',
                'Strong ability to identify key information',
                'Perfect accuracy in answering all question types',
              ],
              weaknesses: [],
              improvementSuggestions: [
                'Continue practicing with more challenging materials',
                'Maintain current listening strategies',
                'Focus on time management to ensure consistent performance',
              ],
              mistakeExplanations: {},
            };
          }

          // Enhance mistakes with explanations
          const enhancedMistakes = mistakes.map(mistake => ({
            ...mistake,
            explanation: aiAnalysis?.mistakeExplanations?.[mistake.questionId] || 'No explanation available',
          }));

          // Store the result in the database
          const listeningEntry = await createListeningEntry({
            studentId,
            materialId,
            answers,
            imageUrl,
            score,
            band,
            correctAnswers: score,
            totalQuestions,
            mistakes: enhancedMistakes,
            strengths: aiAnalysis?.strengths || [],
            weaknesses: aiAnalysis?.weaknesses || [],
            improvementSuggestions: aiAnalysis?.improvementSuggestions || [],
          });

          // Return the result
          return {
            id: entry.id,
            studentId,
            materialId,
            score,
            band,
            correctAnswers: score,
            totalQuestions,
            mistakes: enhancedMistakes,
            analysis: aiAnalysis?.analysis,
            strengths: aiAnalysis?.strengths || [],
            weaknesses: aiAnalysis?.weaknesses || [],
            improvementSuggestions: aiAnalysis?.improvementSuggestions || [],
            status: 'completed',
          };
        } catch (error) {
          console.error('Error processing entry:', error);
          return {
            id: entry.id,
            error: error instanceof Error ? error.message : 'An unexpected error occurred',
            status: 'error',
          };
        }
      })
    );

    return NextResponse.json({ results });
  } catch (error) {
    console.error('Error in bulk listening checker:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// Verify database connection and functionality
const { PrismaClient } = require('../src/generated/prisma');

async function verifyDatabaseConnection() {
  console.log('Verifying database connection...');
  
  try {
    // Create a new Prisma client instance
    const prisma = new PrismaClient();
    
    // Test the connection by checking if the Student table exists
    try {
      const studentCount = await prisma.student.count();
      console.log(`Connection successful! Found ${studentCount} students in the database.`);
    } catch (error) {
      console.error('Error querying Student table:', error);
      return false;
    }
    
    // Test if we can create and delete a student
    try {
      console.log('Testing student creation...');
      const student = await prisma.student.create({
        data: {
          name: 'Test Student',
          email: `test-${Date.now()}@example.com`,
        }
      });
      console.log('Created test student:', student.id);
      
      // Delete the test student
      await prisma.student.delete({
        where: { id: student.id }
      });
      console.log('Deleted test student');
    } catch (error) {
      console.error('Error testing student creation/deletion:', error);
      return false;
    }
    
    // Disconnect from the database
    await prisma.$disconnect();
    
    console.log('Database connection and functionality verified successfully!');
    return true;
  } catch (error) {
    console.error('Error verifying database connection:', error);
    return false;
  }
}

// Run the script
verifyDatabaseConnection()
  .then(success => {
    if (success) {
      console.log('Database verification completed successfully.');
    } else {
      console.error('Database verification failed.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });

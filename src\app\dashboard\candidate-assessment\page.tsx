'use client';

import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>le,
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  Paper,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { Add as AddIcon, CheckCircle, CloudUpload as CloudUploadIcon, Delete as DeleteIcon, Refresh as RefreshIcon, PictureAsPdf as PdfIcon, Upload as UploadIcon, AudioFile as AudioFileIcon } from '@mui/icons-material';
import { generateCandidateReport } from '@/utils/pdf-generator';
import { fetchAndAnalyzeAnswerSheets } from '@/utils/answer-sheet-utils';
import { useState, useEffect, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import CorrectAnswersUpload from '@/components/CorrectAnswersUpload';
import CorrectAnswersFileUpload from '@/components/CorrectAnswersFileUpload';
import ScoreInput from '@/components/ScoreInput';
import BandScoreInput from '@/components/BandScoreInput';

// Define types for our component
interface TestComponent {
  id: string;
  name: string;
  status: 'pending' | 'uploaded' | 'processed';
  type: 'listening' | 'reading' | 'writing_task1' | 'writing_task2' | 'speaking';
  entryId?: string | null;
  materialId?: string | null; // Added materialId property
  correctAnswers?: string[];
  taskQuestions?: string;
  answerSheetImageUrl?: string;
  answerSheetAnalysis?: any; // Analysis of the answer sheet from GPT-4.1
  essayText?: string;
  audioUrl?: string;
  score?: number;
  feedback?: string;
  band?: number; // Band score for the component
  criteriaScores?: any; // Detailed criteria scores
  strengths?: string[] | string; // Component strengths
  weaknesses?: string[] | string; // Component weaknesses
  improvementSuggestions?: string[] | string; // Improvement suggestions
}

interface Candidate {
  id: string;
  studentId: string;
  name?: string;
  components: TestComponent[];
  reportGenerated: boolean;
  reportId?: string | null;
}

export default function CandidateAssessmentPage() {
  // State for candidates
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // State for adding new candidates
  const [showAddDialog, setShowAddDialog] = useState<boolean>(false);
  const [newStudentIds, setNewStudentIds] = useState<string>('');
  const [candidateCount, setCandidateCount] = useState<string>('');
  const [addMode, setAddMode] = useState<'manual' | 'count'>('manual');

  // State for global correct answers and task questions
  const [globalReadingAnswers, setGlobalReadingAnswers] = useState<string>('');
  const [globalListeningAnswers, setGlobalListeningAnswers] = useState<string>('');

  // State for writing task questions
  const [globalWritingTask1Question, setGlobalWritingTask1Question] = useState<string>('');
  const [globalWritingTask2Question, setGlobalWritingTask2Question] = useState<string>('');

  // State for writing task 1 image (chart, graph, etc.)
  const [globalWritingTask1Image, setGlobalWritingTask1Image] = useState<File | null>(null);

  // State for candidate answer sheets
  const [candidateAnswerSheets, setCandidateAnswerSheets] = useState<{
    [candidateId: string]: {
      [componentType: string]: File | null;
    };
  }>({});

  // State for tabs
  const [tabValue, setTabValue] = useState(0);

  // State for correct answers upload
  const [correctAnswersText, setCorrectAnswersText] = useState<string>('');

  // State for applying to all candidates
  const [applyingToAll, setApplyingToAll] = useState(false);

  // Dropzone for writing task 1 image (chart, graph, etc.)
  const {
    getRootProps: getWritingTask1ImageDropzoneProps,
    getInputProps: getWritingTask1ImageInputProps,
    isDragActive: isWritingTask1ImageDragActive
  } = useDropzone({
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        setGlobalWritingTask1Image(acceptedFiles[0]);
      }
    },
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'application/pdf': ['.pdf']
    },
    maxFiles: 1
  });

  // Dropzone for candidate answer sheets
  const getAnswerSheetDropzone = (candidateId: string, componentType: string) => {
    const { getRootProps, getInputProps, isDragActive } = useDropzone({
      onDrop: (acceptedFiles) => {
        if (acceptedFiles.length > 0) {
          setCandidateAnswerSheets(prev => ({
            ...prev,
            [candidateId]: {
              ...prev[candidateId],
              [componentType]: acceptedFiles[0]
            }
          }));
        }
      },
      accept: {
        'image/jpeg': ['.jpg', '.jpeg'],
        'image/png': ['.png'],
        'application/pdf': ['.pdf']
      },
      maxFiles: 1
    });

    return { getRootProps, getInputProps, isDragActive };
  };

  // State for upload dialog
  const [showUploadDialog, setShowUploadDialog] = useState<boolean>(false);
  const [currentCandidate, setCurrentCandidate] = useState<Candidate | null>(null);
  const [currentComponent, setCurrentComponent] = useState<TestComponent | null>(null);

  // State for pagination
  const [page, setPage] = useState<number>(1);
  const [pageSize] = useState<number>(5); // Reduced to 5 students per page
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalCount, setTotalCount] = useState<number>(0);

  // State for search
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState<string>('');

  // Debounce search query to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500); // 500ms debounce time

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Reset to page 1 when search query changes
  useEffect(() => {
    if (searchQuery !== debouncedSearchQuery && page !== 1) {
      setPage(1);
    }
  }, [searchQuery, debouncedSearchQuery, page]);

  // Fetch candidates on component mount, page change, or search query change
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch candidates with pagination and search
        const searchParam = debouncedSearchQuery ? `&search=${encodeURIComponent(debouncedSearchQuery)}` : '';
        const candidatesResponse = await fetch(`/api/candidate-assessment?page=${page}&pageSize=${pageSize}${searchParam}`);

        if (!candidatesResponse.ok) {
          throw new Error('Failed to fetch candidates');
        }
        const data = await candidatesResponse.json();

        // Update state with candidates and pagination info
        setCandidates(data.candidates);
        setTotalPages(data.pagination.totalPages);
        setTotalCount(data.pagination.totalCount);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
        console.error('Error fetching data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [page, pageSize, debouncedSearchQuery]);

  // Auto-clear success message after 5 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

      // Clean up the timer when component unmounts or successMessage changes
      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  // Handle adding new candidates in bulk
  const handleAddCandidate = async () => {
    let studentIds: string[] = [];

    if (addMode === 'manual') {
      if (!newStudentIds.trim()) {
        setError('Student IDs are required');
        return;
      }

      // Split the input by commas, newlines, or spaces and filter out empty strings
      studentIds = newStudentIds
        .split(/[,\n\s]+/)
        .map(id => id.trim())
        .filter(id => id.length > 0);

      if (studentIds.length === 0) {
        setError('No valid student IDs provided');
        return;
      }

      // Check for duplicates in the input
      const uniqueIds = new Set(studentIds);
      if (uniqueIds.size !== studentIds.length) {
        setError('Duplicate student IDs found in the input');
        return;
      }
    } else {
      // Count mode
      const count = parseInt(candidateCount);
      if (isNaN(count) || count <= 0) {
        setError('Please enter a valid number greater than 0');
        return;
      }

      if (count > 500) {
        setError('For performance reasons, please add 500 or fewer candidates at once');
        return;
      }
    }

    // Check if any student IDs already exist (only for manual mode)
    if (addMode === 'manual') {
      const existingIds = studentIds.filter(id => candidates.some(c => c.studentId === id));
      if (existingIds.length > 0) {
        setError(`The following student IDs already exist: ${existingIds.join(', ')}`);
        return;
      }
    }

    try {
      const response = await fetch('/api/candidate-assessment/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          studentIds: addMode === 'manual' ? studentIds : undefined,
          count: addMode === 'count' ? parseInt(candidateCount) : undefined
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add candidates');
      }

      const newStudents = await response.json();

      // Create new candidates with the returned student data
      const newCandidates = newStudents.map((student: any) => ({
        id: student.id,
        studentId: student.id,
        name: null,
        components: [
          { id: 'listening', name: 'Listening', status: 'pending', type: 'listening' },
          { id: 'reading', name: 'Reading', status: 'pending', type: 'reading' },
          { id: 'writing_task1', name: 'Writing Task 1', status: 'pending', type: 'writing_task1' },
          { id: 'writing_task2', name: 'Writing Task 2', status: 'pending', type: 'writing_task2' },
          { id: 'speaking', name: 'Speaking', status: 'pending', type: 'speaking' },
        ],
        reportGenerated: false
      }));

      setCandidates([...candidates, ...newCandidates]);
      setNewStudentIds('');
      setCandidateCount('');
      setShowAddDialog(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      console.error('Error adding candidates:', err);
    }
  };

  // State for delete confirmation dialog
  const [showDeleteDialog, setShowDeleteDialog] = useState<boolean>(false);
  const [candidateToDelete, setCandidateToDelete] = useState<string | null>(null);
  const [deletingCandidate, setDeletingCandidate] = useState<boolean>(false);

  // Handle removing a candidate
  const handleRemoveCandidate = async (candidateId: string) => {
    try {
      setDeletingCandidate(true);

      // Call the API to delete the candidate
      const response = await fetch(`/api/candidate-assessment/${candidateId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete candidate');
      }

      // Remove the candidate from the UI
      setCandidates(candidates.filter(c => c.id !== candidateId));
      setShowDeleteDialog(false);
      setCandidateToDelete(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      console.error('Error deleting candidate:', err);
    } finally {
      setDeletingCandidate(false);
    }
  };

  // Open delete confirmation dialog
  const openDeleteDialog = (candidateId: string) => {
    setCandidateToDelete(candidateId);
    setShowDeleteDialog(true);
  };

  // Handle archiving all candidates
  const handleArchiveCandidates = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/candidate-assessment/archive', {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to archive candidates');
      }

      // Clear the candidates list
      setCandidates([]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      console.error('Error archiving candidates:', err);
    } finally {
      setLoading(false);
    }
  };

  // Function to handle audio transcription
  const transcribeAudioFile = async (file: File) => {
    if (!file || currentComponent?.type !== 'speaking') return;

    try {
      setTranscriptionLoading(true);
      setTranscriptionComplete(false);
      setError(null); // Clear any previous errors

      console.log('Starting transcription for audio file:', file.name, file.type, file.size);

      // Check file size and type
      if (file.size > 25 * 1024 * 1024) { // 25MB limit for OpenAI
        throw new Error('Audio file is too large. Maximum size is 25MB.');
      }

      // Check file type and normalize if needed
      let fileType = file.type;
      const fileName = file.name;
      const fileExtension = fileName.split('.').pop()?.toLowerCase();

      // Normalize MIME type if needed
      if (!fileType.startsWith('audio/')) {
        // Try to infer MIME type from file extension
        if (fileExtension === 'mp3') fileType = 'audio/mpeg';
        else if (fileExtension === 'm4a') fileType = 'audio/mp4';
        else if (fileExtension === 'wav') fileType = 'audio/wav';
        else if (fileExtension === 'ogg') fileType = 'audio/ogg';
        else if (fileExtension === 'webm') fileType = 'audio/webm';
        else fileType = 'audio/mpeg'; // Default to MP3

        console.log(`Normalized MIME type to ${fileType} based on file extension ${fileExtension}`);
      }

      console.log(`Processing audio file: ${file.name}, ${fileType}, ${file.size} bytes`);

      // Call the audio transcription API with retry logic
      let attempts = 0;
      const maxAttempts = 3; // Increase max attempts to 3
      let transcriptionResult: { transcript?: string } | null = null;

      while (attempts < maxAttempts && !transcriptionResult) {
        attempts++;
        try {
          console.log(`Transcription attempt ${attempts} of ${maxAttempts}...`);

          // Create a controller for this specific request
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 90000); // 90 second timeout (increased)

          let responseData;

          try {
            // Create FormData for the API request
            const formData = new FormData();

            // Create a new file object with the normalized MIME type if needed
            let fileToUpload = file;
            if (file.type !== fileType) {
              // Create a new file with the correct MIME type
              fileToUpload = new File([file], file.name, { type: fileType });
              console.log(`Created new file with corrected MIME type: ${fileToUpload.type}`);
            }

            formData.append('audio', fileToUpload);
            formData.append('model', 'whisper-1');
            formData.append('language', 'en'); // Explicitly set language to English

            // Call the audio transcription API
            const transcriptionResponse = await fetch('/api/audio-transcription', {
              method: 'POST',
              body: formData,
              signal: controller.signal
            });

            // Clear the timeout
            clearTimeout(timeoutId);

            console.log(`Transcription API response status: ${transcriptionResponse.status}`);

            if (!transcriptionResponse.ok) {
              let errorData;
              try {
                errorData = await transcriptionResponse.json();
              } catch (jsonError) {
                const errorText = await transcriptionResponse.text();
                errorData = { error: `Failed to parse error response: ${errorText.substring(0, 100)}...` };
              }

              console.error(`Audio transcription error (attempt ${attempts}):`, errorData);

              // If this is the last attempt, throw the error
              if (attempts >= maxAttempts) {
                throw new Error(errorData.error || `Failed to transcribe audio (HTTP ${transcriptionResponse.status})`);
              }

              // Otherwise, wait a moment before retrying
              console.log(`Waiting before retry attempt ${attempts + 1}...`);
              await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds before retry
              continue;
            }

            // Process the response
            responseData = await transcriptionResponse.json();
            console.log('Audio transcription result:', responseData);
          } catch (fetchError) {
            // Clear the timeout
            clearTimeout(timeoutId);

            if (fetchError instanceof Error && fetchError.name === 'AbortError') {
              console.error('Transcription request timed out after 90 seconds');

              // If this is the last attempt, throw the error
              if (attempts >= maxAttempts) {
                throw new Error('Transcription request timed out');
              }

              // Otherwise, wait a moment before retrying
              console.log(`Waiting before retry attempt ${attempts + 1} after timeout...`);
              await new Promise(resolve => setTimeout(resolve, 3000));
              continue;
            }

            // Re-throw other fetch errors
            throw fetchError;
          }

          // Check if we got a transcript
          if (!responseData || !responseData.transcript) {
            console.error('No transcript in response:', responseData);
            if (attempts >= maxAttempts) {
              throw new Error('No transcript returned from the API');
            }
            transcriptionResult = null; // Reset to trigger retry
            continue;
          }

          // Check if we got a simulated transcript (fallback)
          if (responseData.transcript.includes('SIMULATED TRANSCRIPT') ||
              responseData.transcript.includes('HARDCODED TRANSCRIPT')) {
            console.warn('Received simulated transcript instead of actual transcription');

            // If this is the last attempt, accept the simulated transcript
            if (attempts >= maxAttempts) {
              console.log('Accepting simulated transcript after all attempts failed');
              transcriptionResult = responseData;
            } else {
              // Otherwise, try again
              console.log(`Rejecting simulated transcript, trying again (attempt ${attempts + 1})...`);
              await new Promise(resolve => setTimeout(resolve, 3000));
              continue;
            }
          } else {
            // We got a real transcript
            transcriptionResult = responseData;
            console.log('Successfully received real transcript');
          }

          // Update the transcription field with the extracted text
          setUploadContent((prev: any) => ({
            ...prev,
            transcription: responseData.transcript
          }));

          // Set transcription complete flag
          setTranscriptionComplete(true);

          return responseData.transcript;
        } catch (attemptError) {
          console.error(`Error in transcription attempt ${attempts}:`, attemptError);

          // If this is the last attempt, rethrow the error
          if (attempts >= maxAttempts) {
            throw attemptError;
          }

          // Otherwise, wait a moment before retrying
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }

      // If we get here without a result, something went wrong
      throw new Error('Failed to transcribe audio after multiple attempts');
    } catch (error) {
      console.error('Error transcribing audio:', error);

      // Provide more detailed error messages based on the error type
      let errorMessage = 'Unknown error';

      if (error instanceof Error) {
        errorMessage = error.message;

        // Add more context for specific error types
        if (error.message.includes('timed out')) {
          errorMessage = 'The transcription request timed out. The audio file might be too large or the server is busy. Please try again with a smaller file or try later.';
        } else if (error.message.includes('Failed to process audio') || error.message.includes('format')) {
          errorMessage = 'Failed to process audio. The file format might not be supported or the audio quality is too low. Please try a different file format (MP3, M4A, or WAV).';
        } else if (error.message.includes('No transcript')) {
          errorMessage = 'No transcript could be extracted from the audio. Please check the audio quality and try again.';
        } else if (error.message.includes('too large')) {
          errorMessage = 'The audio file is too large. Maximum size is 25MB. Please compress the file or use a shorter recording.';
        } else if (error.message.includes('Too many requests')) {
          errorMessage = 'Too many requests. The server is busy. Please try again later.';
        }
      }

      console.log('Final error message:', errorMessage);

      setError(`Transcription error: ${errorMessage}`);
      setTranscriptionComplete(false);
      return null;
    } finally {
      setTranscriptionLoading(false);
    }
  };

  // Handle opening upload dialog
  const handleOpenUpload = async (candidate: Candidate, component: TestComponent) => {
    setCurrentCandidate(candidate);
    setCurrentComponent(component);
    setUploadedFile(null);
    setUploadedFiles([]);
    setUploadedImageUrls([]);
    setError(null); // Clear any previous errors
    setTranscriptionLoading(false);
    setTranscriptionComplete(false);

    // Show material warning for reading and listening components
    setShowMaterialWarning(component.type === 'reading' || component.type === 'listening');

    // Initialize content object
    let initialContent: any = {};

    // If the component has an entryId, fetch the existing data
    if (component.entryId) {
      try {
        const response = await fetch(`/api/candidate-assessment/entry?id=${component.entryId}&type=${component.type}`);
        if (response.ok) {
          const entryData = await response.json();
          if (entryData) {
            if (component.type === 'reading' || component.type === 'listening') {
              // Convert answers array to comma-separated string
              const userAnswers = Array.isArray(entryData.answers)
                ? entryData.answers.join(', ')
                : typeof entryData.answers === 'object'
                  ? Object.values(entryData.answers).join(', ')
                  : '';

              initialContent = {
                userAnswers,
                imageUrl: entryData.imageUrl,
                extractedText: entryData.extractedText
              };
            } else if (component.type === 'writing_task1' || component.type === 'writing_task2') {
              // For writing tasks, get the essay text and image URLs
              initialContent = {
                essayText: entryData.essayText || '',
                taskQuestions: entryData.taskQuestions || '',
                imageUrls: entryData.imageUrls || [],
                manualBand: entryData.band ? parseFloat(entryData.band) : null
              };

              // If there are image URLs, set them in the state
              if (entryData.imageUrls && entryData.imageUrls.length > 0) {
                setUploadedImageUrls(entryData.imageUrls);
              }
            } else if (component.type === 'speaking') {
              // For speaking components, get the transcription and examiner content
              console.log('Loading existing speaking entry data:', entryData);

              initialContent = {
                transcription: entryData.transcription || '',
                examinerContent: entryData.examinerContent || '',
                imageUrl: entryData.audioUrl, // Use the audio URL as the image URL for UI purposes
                manualBand: entryData.band ? parseFloat(entryData.band) : null
              };

              // If we have transcription data, set the transcription complete flag
              if (entryData.transcription) {
                setTranscriptionComplete(true);
              }

              // Create a dummy file object if we have an audio URL but no file
              if (entryData.audioUrl && !entryData.audioUrl.startsWith('data:')) {
                // This is just a placeholder to show the UI that a file exists
                console.log('Setting dummy file object for existing audio');
              }
            }
          }
        }
      } catch (error) {
        console.error('Error fetching entry data:', error);
      }
    }

    // Add global answers
    if (component.type === 'reading') {
      initialContent.correctAnswers = globalReadingAnswers;
    } else if (component.type === 'listening') {
      initialContent.correctAnswers = globalListeningAnswers;
    }

    // Add writing task questions if applicable
    if (component.type === 'writing_task1') {
      // Ensure we have default task questions for writing task 1
      const taskQuestions = globalWritingTask1Question || 'Default Task 1 question: Describe the chart or graph.';
      initialContent.taskQuestions = taskQuestions;
    } else if (component.type === 'writing_task2') {
      // Ensure we have default task questions for writing task 2
      const taskQuestions = globalWritingTask2Question || 'Default Task 2 question: Write an essay on the given topic.';
      initialContent.taskQuestions = taskQuestions;
    }

    // Reset the upload content with the initial content
    setUploadContent(() => initialContent);

    setShowUploadDialog(true);
  };

  // State for upload form
  const [uploadContent, setUploadContent] = useState<any>({
    raw_score: '',
    userAnswers: '',
    correctAnswers: '',
    taskQuestions: '',
    essayText: '',
    transcription: '',
    examinerContent: '',
    manualBand: null,
    inflatedBand: null
  });
  const [uploadLoading, setUploadLoading] = useState<boolean>(false);
  const [transcriptionLoading, setTranscriptionLoading] = useState<boolean>(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [transcriptionComplete, setTranscriptionComplete] = useState<boolean>(false);

  // State for multiple uploaded files for writing tasks
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [uploadedImageUrls, setUploadedImageUrls] = useState<string[]>([]);

  // State for the material selection warning
  const [showMaterialWarning, setShowMaterialWarning] = useState<boolean>(false);

  // Handle file drop
  const onDrop = useCallback(async (acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    if (rejectedFiles.length > 0) {
      // Check if the rejection is due to too many files
      const tooManyFilesError = rejectedFiles.some(rejection =>
        rejection.errors.some((e: any) => e.code === 'too-many-files')
      );

      if (tooManyFilesError) {
        setError(`Too many files. You can upload a maximum of 10 images for a writing task.`);
      } else {
        // Handle other types of rejections
        const errors = rejectedFiles.map(rejection => {
          return `File ${rejection.file.name}: ${rejection.errors.map((e: any) => e.message).join(', ')}`;
        }).join('\n');
        setError(`Some files were rejected: ${errors}`);
      }
      return;
    }

    if (acceptedFiles.length > 0) {
      // For writing tasks, handle multiple files
      if (currentComponent?.type === 'writing_task1' || currentComponent?.type === 'writing_task2') {
        console.log('Files accepted for writing task:', acceptedFiles.length);
        // Add new files to the existing array
        const newFiles = [...uploadedFiles, ...acceptedFiles];
        setUploadedFiles(newFiles);

        // Create preview URLs for the images
        const newImageUrls = acceptedFiles.map(file => URL.createObjectURL(file));
        setUploadedImageUrls(prev => [...prev, ...newImageUrls]);

        // Process each new image to extract text for writing tasks
        try {
          setUploadLoading(true);

          // Process each image one by one and combine the text
          for (const file of acceptedFiles) {
            // Create a FormData object for the image processing request
            const formData = new FormData();
            formData.append('image', file);
            formData.append('componentType', currentComponent.type);
            formData.append('entryId', 'temp'); // We don't have an entry ID yet
            formData.append('studentId', currentCandidate?.studentId || 'unknown');

            // Call the image processing API
            const imageProcessResponse = await fetch('/api/candidate-assessment/process-image', {
              method: 'POST',
              body: formData
            });

            if (!imageProcessResponse.ok) {
              const errorData = await imageProcessResponse.json();
              console.error('Image processing error:', errorData);
              // Don't show an error, just log it
              continue; // Skip to the next image
            }

            const imageResult = await imageProcessResponse.json();
            console.log('Writing image processing result:', imageResult);

            // Update the essay text field with the extracted text
            if (imageResult.text) {
              setUploadContent((prev: any) => {
                // If we already have text, append the new text
                const existingText = prev.essayText || '';
                const newText = existingText ? `${existingText}\n\n${imageResult.text}` : imageResult.text;
                return {
                  ...prev,
                  essayText: newText
                };
              });
            }
          }
        } catch (imageError) {
          console.error('Error processing writing image with Claude 3.5:', imageError);
          // Don't show an error, just log it
        } finally {
          setUploadLoading(false);
        }
      } else {
        // For other components, just use the first file
        const file = acceptedFiles[0];
        console.log('File accepted:', file.name, 'type:', file.type, 'size:', file.size);
        setUploadedFile(file);

        // Create a preview URL for the file
        const imageUrl = URL.createObjectURL(file);
        setUploadContent((prev: any) => ({ ...prev, imageUrl }));

      // For reading and listening components, process the image immediately to extract answers
      // For speaking components, process the audio immediately to extract transcription
      if (currentComponent?.type === 'reading' || currentComponent?.type === 'listening' || currentComponent?.type === 'speaking') {
        try {
          setUploadLoading(true);

          // Create a FormData object for the processing request
          const formData = new FormData();

          if (currentComponent.type === 'speaking') {
            // For speaking components, we need to transcribe the audio
            console.log('Processing audio file for transcription:', file.name, file.type, file.size);

            try {
              setUploadLoading(true);
              // Start the transcription process immediately and wait for it to complete
              const transcript = await transcribeAudioFile(file);
              if (transcript) {
                console.log('Transcription completed successfully:', transcript.substring(0, 100) + '...');

                // Check if we got a simulated transcript
                if (transcript.includes('SIMULATED TRANSCRIPT') || transcript.includes('HARDCODED TRANSCRIPT')) {
                  console.warn('Using simulated transcript - this is not the actual audio content');
                  // Show a warning to the user
                  setError('Warning: Using simulated transcript. The audio could not be properly transcribed. You may want to try uploading again or manually enter the transcript.');
                }
              }
            } catch (error) {
              console.error('Transcription failed:', error);
              // We still keep the file even if transcription fails
            } finally {
              setUploadLoading(false);
            }

            // Return early since we're handling the transcription asynchronously
            return;
          } else {
            // For image-based components (reading/listening)
            formData.append('image', file);
            formData.append('componentType', currentComponent.type);
            formData.append('entryId', 'temp'); // We don't have an entry ID yet
            formData.append('studentId', currentCandidate?.studentId || 'unknown');
          }

          // Call the image processing API
          const imageProcessResponse = await fetch('/api/candidate-assessment/process-image', {
            method: 'POST',
            body: formData
          });

          if (!imageProcessResponse.ok) {
            const errorData = await imageProcessResponse.json();
            console.error('Image processing error:', errorData);
            // Don't show an error, just log it
          } else {
            const imageResult = await imageProcessResponse.json();
            console.log('Image processing result:', imageResult);

            // Update the user answers field with the parsed answers
            if (imageResult.parsedAnswers) {
              setUploadContent((prev: any) => ({
                ...prev,
                userAnswers: imageResult.parsedAnswers
              }));
            }
          }
        } catch (imageError) {
          console.error('Error processing image with Claude 3.5:', imageError);
          // Don't show an error, just log it
        } finally {
          setUploadLoading(false);
        }
      }
    }
    }
  }, [currentComponent, currentCandidate, uploadedFiles]);

  // Accept types are defined directly in the useDropzone hook below

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: currentComponent?.type === 'speaking' ? {
      'audio/mpeg': ['.mp3'],
      'audio/mp4': ['.m4a'],
      'audio/wav': ['.wav'],
      'audio/flac': ['.flac'],
      'audio/ogg': ['.ogg', '.oga'],
      'audio/webm': ['.webm']
    } : {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'application/pdf': ['.pdf']
    },
    // Allow multiple files for writing tasks, single file for other components
    maxFiles: (currentComponent?.type === 'writing_task1' || currentComponent?.type === 'writing_task2') ? 10 : 1,
    maxSize: 20 * 1024 * 1024, // 20MB max file size (increased for audio files)
    onError: (err) => {
      console.error('Dropzone error:', err);
      setError(`File upload error: ${err.message}`);
    }
  });

  // Handle upload completion
  const handleUploadComplete = async (retryCount = 0) => {
    if (!currentCandidate || !currentComponent) return;

    // Clear any previous errors
    setError(null);

    // Hide the material warning
    setShowMaterialWarning(false);

    // Get the content from the upload dialog
    const content = { ...uploadContent };

    // For reading and listening components, use the simplified approach
    if (currentComponent.type === 'reading' || currentComponent.type === 'listening') {
      // Validate raw score
      if (!content.raw_score) {
        setError(`Please enter the ${currentComponent.type} raw score`);
        return;
      }

      try {
        setUploadLoading(true);

        // Call the simplified API
        const response = await fetch('/api/candidate-assessment/simplified-process', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            entryId: currentComponent.entryId,
            componentType: currentComponent.type,
            studentId: currentCandidate.studentId,
            materialId: currentComponent.materialId || 'default', // Use a default material ID if not provided
            raw_score: content.raw_score
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to process ${currentComponent.type} component`);
        }

        const result = await response.json();

        // Update the component in the UI
        const updatedCandidates = candidates.map(c => {
          if (c.id === currentCandidate.id) {
            const updatedComponents = c.components.map(comp => {
              if (comp.id === currentComponent.id) {
                return {
                  ...comp,
                  status: 'processed' as const,
                  entryId: result.entryId,
                  band: result.band,
                  strengths: result.strengths,
                  weaknesses: result.weaknesses,
                  improvementSuggestions: result.improvementSuggestions
                };
              }
              return comp;
            });
            return { ...c, components: updatedComponents };
          }
          return c;
        });

        setCandidates(updatedCandidates);
        setShowUploadDialog(false);
        setUploadContent(() => ({}));
        setUploadLoading(false);
        return;
      } catch (error) {
        console.error(`Error processing ${currentComponent.type} component:`, error);
        setError(error instanceof Error ? error.message : 'An unexpected error occurred');
        setUploadLoading(false);
        return;
      }
    }

    // For writing tasks, validate that we have either files, essay text, or manual band score
    if (currentComponent.type === 'writing_task1' || currentComponent.type === 'writing_task2') {
      // Ensure we have task questions
      if (!uploadContent.taskQuestions || uploadContent.taskQuestions.trim() === '') {
        // Set default task questions if empty
        const defaultQuestion = currentComponent.type === 'writing_task1' ?
          'Default Task 1 question: Describe the chart or graph.' :
          'Default Task 2 question: Write an essay on the given topic.';

        setUploadContent((prev: Record<string, any>) => ({ ...prev, taskQuestions: defaultQuestion }));
      }

      // Check if we have a manual band score
      if (uploadContent.manualBand !== undefined && uploadContent.manualBand !== null) {
        console.log('Using manual band score for writing task:', uploadContent.manualBand);

        try {
          setUploadLoading(true);

          // Call the manual band API
          const response = await fetch('/api/candidate-assessment/manual-band', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              entryId: currentComponent.entryId,
              componentType: currentComponent.type,
              studentId: currentCandidate.studentId,
              band: uploadContent.manualBand,
              taskType: currentComponent.type === 'writing_task1' ? 'task1' : 'task2',
              taskQuestion: uploadContent.taskQuestions,
              essayText: uploadContent.essayText
            }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Failed to process ${currentComponent.type} component with manual band`);
          }

          const result = await response.json();

          // Update the component in the UI
          const updatedCandidates = candidates.map(c => {
            if (c.id === currentCandidate.id) {
              const updatedComponents = c.components.map(comp => {
                if (comp.id === currentComponent.id) {
                  return {
                    ...comp,
                    status: 'processed' as const,
                    entryId: result.id,
                    band: result.band
                  };
                }
                return comp;
              });
              return { ...c, components: updatedComponents };
            }
            return c;
          });

          setCandidates(updatedCandidates);
          setShowUploadDialog(false);
          setUploadLoading(false);
          return;
        } catch (error) {
          console.error(`Error processing ${currentComponent.type} component with manual band:`, error);
          setError(error instanceof Error ? error.message : 'An unexpected error occurred');
          setUploadLoading(false);
          return;
        }
      }

      // If no manual band score, validate that we have either files or essay text
      if (uploadedFiles.length === 0 && !uploadContent.essayText) {
        setError('Please upload at least one image of the handwritten essay, enter the essay text, or provide a manual band score');
        return;
      }

      // Log the upload attempt for debugging
      console.log('Attempting to upload writing task with data:', {
        componentType: currentComponent.type,
        studentId: currentCandidate.studentId,
        taskQuestions: uploadContent.taskQuestions,
        numberOfFiles: uploadedFiles.length,
        hasEssayText: !!uploadContent.essayText,
        essayTextLength: uploadContent.essayText ? uploadContent.essayText.length : 0,
        manualBand: uploadContent.manualBand
      });
    }

    // For speaking, check if we have a manual band score
    if (currentComponent.type === 'speaking') {
      if (uploadContent.manualBand !== undefined && uploadContent.manualBand !== null) {
        console.log('Using manual band score for speaking task:', uploadContent.manualBand);

        try {
          setUploadLoading(true);

          // Prepare the request body
          const requestBody: any = {
            entryId: currentComponent.entryId,
            componentType: currentComponent.type,
            studentId: currentCandidate.studentId,
            band: uploadContent.manualBand,
            transcription: uploadContent.transcription,
            examinerContent: uploadContent.examinerContent
          };

          // Call the manual band API
          const response = await fetch('/api/candidate-assessment/manual-band', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Failed to process speaking component with manual band`);
          }

          const result = await response.json();

          // Update the component in the UI
          const updatedCandidates = candidates.map(c => {
            if (c.id === currentCandidate.id) {
              const updatedComponents = c.components.map(comp => {
                if (comp.id === currentComponent.id) {
                  return {
                    ...comp,
                    status: 'processed' as const,
                    entryId: result.id,
                    band: result.band
                  };
                }
                return comp;
              });
              return { ...c, components: updatedComponents };
            }
            return c;
          });

          setCandidates(updatedCandidates);
          setShowUploadDialog(false);
          setUploadLoading(false);
          return;
        } catch (error) {
          console.error(`Error processing speaking component with manual band:`, error);
          setError(error instanceof Error ? error.message : 'An unexpected error occurred');
          setUploadLoading(false);
          return;
        }
      }
    }

    try {
      setUploadLoading(true);

      // Prepare content based on component type
      let content = uploadContent;
      let answerSheetImageUrl = null;

      // If we have an uploaded file, create a data URL for the answer sheet image
      if (uploadedFile) {
        // Create a data URL for the image to store in the component
        answerSheetImageUrl = URL.createObjectURL(uploadedFile);
      }

      // For file uploads, we need to handle them differently
      let response;

      // For writing tasks with multiple files, we need to handle them specially
      if ((currentComponent.type === 'writing_task1' || currentComponent.type === 'writing_task2') && uploadedFiles.length > 0) {
        try {
          // Create a FormData object for file upload
          const formData = new FormData();

          // Add all files to the form data
          uploadedFiles.forEach((file, index) => {
            formData.append(`file${index}`, file);
          });

          // Add metadata
          formData.append('studentId', currentCandidate.studentId);
          formData.append('componentType', currentComponent.type);
          formData.append('fileCount', uploadedFiles.length.toString());

          // If this is an update to an existing entry, include the entry ID
          if (currentComponent.entryId) {
            formData.append('entryId', currentComponent.entryId);
          }

          // Add task questions and type
          let taskQuestions = uploadContent.taskQuestions || '';
          if (!taskQuestions || taskQuestions.trim() === '') {
            taskQuestions = currentComponent.type === 'writing_task1' ?
              'Default Task 1 question: Describe the chart or graph.' :
              'Default Task 2 question: Write an essay on the given topic.';
          }
          formData.append('taskQuestions', taskQuestions);
          formData.append('taskType', currentComponent.type === 'writing_task1' ? 'task1' : 'task2');

          // Add essay text if available
          if (uploadContent.essayText) {
            formData.append('essayText', uploadContent.essayText);
          }

          // Log the form data for debugging
          console.log('Uploading writing task with multiple files:', {
            componentType: currentComponent.type,
            studentId: currentCandidate.studentId,
            taskQuestions: taskQuestions,
            fileCount: uploadedFiles.length,
            hasEssayText: !!uploadContent.essayText
          });

          // Make API call to upload component with files
          response = await fetch('/api/candidate-assessment/upload-file', {
            method: 'POST',
            body: formData,
          });
        } catch (uploadError) {
          console.error('Error preparing file upload:', uploadError);
          throw new Error(`Error preparing file upload: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`);
        }
      }
      // For single file uploads (reading, listening, speaking, writing)
      else if ((uploadedFile || (currentComponent.type === 'speaking' && currentComponent.status === 'uploaded')) &&
          (['reading', 'listening', 'speaking', 'writing_task1', 'writing_task2'].includes(currentComponent.type))) {
        try {
          // Create a FormData object for file upload
          const formData = new FormData();

          // Make sure we have a file to upload or we're updating an existing speaking entry
          if (!uploadedFile && !(currentComponent.type === 'speaking' && currentComponent.status === 'uploaded')) {
            throw new Error('No file selected for upload');
          }

          // Only add the file if we have one
          if (uploadedFile) {
            console.log('Uploading file:', uploadedFile.name, uploadedFile.type, uploadedFile.size);
            formData.append('file', uploadedFile);
          } else {
            console.log('Updating existing speaking entry without a new file');
          }

          formData.append('studentId', currentCandidate.studentId);
          formData.append('componentType', currentComponent.type);

          // If this is an update to an existing entry, include the entry ID
          if (currentComponent.entryId) {
            formData.append('entryId', currentComponent.entryId);
          }

          // Include user answers for reading and listening components
          if ((['reading', 'listening'].includes(currentComponent.type)) && content.userAnswers) {
            formData.append('userAnswers', content.userAnswers);
          }

          // Include correct answers for reading and listening components
          if (['reading', 'listening'].includes(currentComponent.type)) {
            // Use content.correctAnswers if available, otherwise use global answers
            let correctAnswers = content.correctAnswers || '';

            // If no content.correctAnswers, use the appropriate global answers
            if (!correctAnswers) {
              // Use type assertion to help TypeScript understand the type narrowing
              const componentType = currentComponent.type as 'reading' | 'listening';
              if (componentType === 'reading') {
                correctAnswers = globalReadingAnswers;
              } else if (componentType === 'listening') {
                correctAnswers = globalListeningAnswers;
              }
            }

            if (correctAnswers) {
              console.log(`Adding correct answers for ${currentComponent.type}:`, correctAnswers);
              formData.append('correctAnswers', correctAnswers);
            }
          }
          // We don't need material ID anymore as we're using uploaded correct answers directly

          // Additional fields for speaking
          if (currentComponent.type === 'speaking') {
            // Make sure we include the transcription that was extracted earlier
            const transcription = content.transcription || '';
            console.log('Including transcription in upload:', transcription ? `${transcription.substring(0, 50)}...` : 'None');
            formData.append('transcription', transcription);

            // Include examiner content if available
            const examinerContent = content.examinerContent || '';
            console.log('Including examiner content in upload:', examinerContent ? `${examinerContent.substring(0, 50)}...` : 'None');
            formData.append('examinerContent', examinerContent);

            // Check if we have a simulated transcript
            if (transcription.includes('SIMULATED TRANSCRIPT') || transcription.includes('HARDCODED TRANSCRIPT')) {
              console.warn('Uploading with simulated transcript - this is not the actual audio content');
              // Add a flag to indicate this is a simulated transcript
              formData.append('isSimulatedTranscript', 'true');
            }

            // No longer using partNumber as we're processing the entire conversation
          }

          // Additional fields for writing tasks
          if (currentComponent.type === 'writing_task1' || currentComponent.type === 'writing_task2') {
            // Ensure we have task questions
            let taskQuestions = content.taskQuestions || '';
            if (!taskQuestions || taskQuestions.trim() === '') {
              taskQuestions = currentComponent.type === 'writing_task1' ?
                'Default Task 1 question: Describe the chart or graph.' :
                'Default Task 2 question: Write an essay on the given topic.';
            }
            formData.append('taskQuestions', taskQuestions);
            formData.append('taskType', currentComponent.type === 'writing_task1' ? 'task1' : 'task2');

            // Log the form data for debugging
            if (uploadedFile) {
              console.log('Uploading writing task with data:', {
                componentType: currentComponent.type,
                studentId: currentCandidate.studentId,
                taskQuestions: content.taskQuestions || '',
                fileName: uploadedFile.name,
                fileType: uploadedFile.type,
                fileSize: uploadedFile.size
              });
            } else {
              console.log('Uploading writing task with data:', {
                componentType: currentComponent.type,
                studentId: currentCandidate.studentId,
                taskQuestions: content.taskQuestions || '',
                noFile: true
              });
            }
          }

          // Make API call to upload component with file
          response = await fetch('/api/candidate-assessment/upload-file', {
            method: 'POST',
            body: formData,
          });
        } catch (uploadError) {
          console.error('Error preparing file upload:', uploadError);
          throw new Error(`Error preparing file upload: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`);
        }
      } else {
        // Regular JSON upload (fallback)
        response = await fetch('/api/candidate-assessment/upload', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            studentId: currentCandidate.studentId,
            componentType: currentComponent.type,
            content,
          }),
        });
      }

      if (!response.ok) {
        let errorMessage = 'Failed to upload component';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
          // Log the full error response for debugging
          console.error('Server error response:', errorData);
        } catch (e) {
          console.error('Error parsing error response:', e);
        }

        // For writing tasks, add more specific error information
        if (currentComponent.type === 'writing_task1' || currentComponent.type === 'writing_task2') {
          errorMessage = `Failed to upload ${currentComponent.type}: ${errorMessage}`;
        }

        throw new Error(errorMessage);
      }

      const result = await response.json();

      // Update the component status
      const updatedCandidates = candidates.map(candidate => {
        if (candidate.id === currentCandidate.id) {
          const updatedComponents = candidate.components.map(component => {
            if (component.id === currentComponent.id) {
              const updatedComponent = {
                ...component,
                status: 'uploaded' as const,
                entryId: result.id,
                answerSheetImageUrl: answerSheetImageUrl || undefined // Store the answer sheet image URL
              };

              // Add correct answers for reading and listening
              if (['reading', 'listening'].includes(component.type)) {
                updatedComponent.correctAnswers = content.correctAnswers ?
                  content.correctAnswers.split(',').map((a: string) => a.trim()) :
                  [];
              }

              // Add task questions for writing
              if (component.type === 'writing_task1' || component.type === 'writing_task2') {
                updatedComponent.taskQuestions = content.taskQuestions || '';
                // If we have an uploaded file for writing tasks, make sure to store the image URL
                if (answerSheetImageUrl) {
                  updatedComponent.answerSheetImageUrl = answerSheetImageUrl;
                }
              }

              return updatedComponent;
            }
            return component;
          });

          return { ...candidate, components: updatedComponents };
        }
        return candidate;
      });

      setCandidates(updatedCandidates);
      setShowUploadDialog(false);
      // Don't clear the upload content - this allows the user to see the same content if they reopen the dialog
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      console.error('Error uploading component:', err);

      // Special handling for writing tasks
      if (currentComponent.type === 'writing_task1' || currentComponent.type === 'writing_task2') {
        console.error('Writing task upload failed with details:', {
          componentType: currentComponent.type,
          studentId: currentCandidate.studentId,
          taskQuestions: uploadContent.taskQuestions,
          fileName: uploadedFile?.name,
          fileType: uploadedFile?.type,
          fileSize: uploadedFile?.size,
          error: errorMessage
        });

        // Try a different approach for writing tasks
        if (retryCount === 0) {
          console.log('Trying alternative approach for writing task upload...');

          // Create a simpler FormData with minimal required fields
          const simpleFormData = new FormData();

          // If we have files, add the first one
          if (uploadedFiles.length > 0) {
            simpleFormData.append('file', uploadedFiles[0]);
          } else if (uploadedFile) {
            simpleFormData.append('file', uploadedFile);
          }

          simpleFormData.append('studentId', currentCandidate.studentId);
          simpleFormData.append('componentType', currentComponent.type);
          simpleFormData.append('taskType', currentComponent.type === 'writing_task1' ? 'task1' : 'task2');
          simpleFormData.append('taskQuestions', 'Default task question');

          // Add essay text if available
          if (uploadContent.essayText) {
            simpleFormData.append('essayText', uploadContent.essayText);
          }

          try {
            // Make a direct API call with minimal data
            const response = await fetch('/api/candidate-assessment/upload-file', {
              method: 'POST',
              body: simpleFormData,
            });

            if (response.ok) {
              console.log('Alternative approach succeeded!');
              setUploadLoading(false);
              setShowUploadDialog(false);
              setUploadedFile(null);
              setUploadContent({});

              // Update the component status
              const updatedCandidates = candidates.map(c => {
                if (c.id === currentCandidate.id) {
                  const updatedComponents = c.components.map(comp => {
                    if (comp.id === currentComponent.id) {
                      return { ...comp, status: 'uploaded' as const };
                    }
                    return comp;
                  });
                  return { ...c, components: updatedComponents };
                }
                return c;
              });
              setCandidates(updatedCandidates);
              return;
            } else {
              console.error('Alternative approach also failed');
            }
          } catch (altError) {
            console.error('Error in alternative approach:', altError);
          }
        }
      }

      // If it's a network error or server error, offer to retry
      if (retryCount < 2 && (errorMessage.includes('network') || errorMessage.includes('server'))) {
        if (confirm(`Upload failed: ${errorMessage}\n\nWould you like to retry?`)) {
          // Wait a moment before retrying
          setTimeout(() => {
            handleUploadComplete(retryCount + 1);
          }, 1000);
          return;
        }
      } else {
        // Show error message for better visibility
        setError(`Upload failed: ${errorMessage}`);
      }
    } finally {
      setUploadLoading(false);
    }
  };

  // State for processing
  const [processingComponents, setProcessingComponents] = useState<{[key: string]: boolean}>({});

  // Handle processing a component
  const handleProcessComponent = async (candidate: Candidate, component: TestComponent) => {
    if (!component.entryId) {
      setError('No entry ID found for this component');
      return;
    }

    // Set processing state
    setProcessingComponents(prev => ({
      ...prev,
      [`${candidate.id}-${component.id}`]: true
    }));

    try {
      // For writing task 1, check if we need to get the task image from the material
      if (component.type === 'writing_task1') {
        try {
          // Fetch the entry to get the material ID
          const entryResponse = await fetch(`/api/candidate-assessment/entry?id=${component.entryId}&type=${component.type}`);
          if (entryResponse.ok) {
            const entryData = await entryResponse.json();
            if (entryData && entryData.materialId) {
              // Fetch the material to get the task image
              const materialResponse = await fetch(`/api/candidate-assessment/material?id=${entryData.materialId}`);
              if (materialResponse.ok) {
                const materialData = await materialResponse.json();
                if (materialData && materialData.imageUrl) {
                  console.log('Found task 1 image in material:', materialData.id);
                  // Set the task image URL in the component
                  component.answerSheetImageUrl = materialData.imageUrl;
                }
              }
            }
          }
        } catch (error) {
          console.error('Error fetching task 1 image from material:', error);
          // Continue with processing even if we can't get the task image
        }
      }

      // If the component has an image URL and is a writing, reading, or listening component,
      // first process the image with Claude 3.5 to extract text
      if (component.answerSheetImageUrl &&
          (component.type === 'writing_task1' ||
           component.type === 'writing_task2' ||
           component.type === 'reading' ||
           component.type === 'listening')) {

        console.log(`Processing image for ${component.type} with Claude 3.5...`);

        try {
          // Fetch the image from the URL
          const imageResponse = await fetch(component.answerSheetImageUrl);
          const imageBlob = await imageResponse.blob();

          // Create a FormData object for the image processing request
          const formData = new FormData();
          formData.append('image', imageBlob);
          formData.append('componentType', component.type);
          formData.append('entryId', component.entryId);
          formData.append('studentId', candidate.studentId);

          // Call the image processing API
          const imageProcessResponse = await fetch('/api/candidate-assessment/process-image', {
            method: 'POST',
            body: formData
          });

          if (!imageProcessResponse.ok) {
            const errorData = await imageProcessResponse.json();
            console.error('Image processing error:', errorData);
            // We'll continue with the regular processing even if image processing fails
          } else {
            const imageResult = await imageProcessResponse.json();
            console.log('Image processing result:', imageResult);

            // Log success but don't show alert since it happens automatically
            console.log(`Successfully extracted text from ${component.name} image using Claude 3.5 Sonnet.`);
          }
        } catch (imageError) {
          console.error('Error processing image with Claude 3.5:', imageError);
          // Continue with regular processing even if image processing fails
        }
      }

      // Make API call to process component (this will use the extracted text if available)
      const response = await fetch('/api/candidate-assessment/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          entryId: component.entryId,
          componentType: component.type,
          model: 'gpt-4o', // Default model
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process component');
      }

      const result = await response.json();
      console.log('Process result:', result);

      // Update the component status and include the band score
      const updatedCandidates = candidates.map(c => {
        if (c.id === candidate.id) {
          const updatedComponents = c.components.map(comp => {
            if (comp.id === component.id) {
              return {
                ...comp,
                status: 'processed' as const,
                band: result.band || comp.band, // Include band score from API response
                criteriaScores: result.criteriaScores || comp.criteriaScores,
                strengths: result.strengths || comp.strengths,
                weaknesses: result.weaknesses || comp.weaknesses,
                improvementSuggestions: result.improvementSuggestions || comp.improvementSuggestions
              };
            }
            return comp;
          });

          return { ...c, components: updatedComponents };
        }
        return c;
      });

      setCandidates(updatedCandidates);

      // Log success message with band score if available
      if (result.band) {
        console.log(`Successfully processed ${component.name}. Band score: ${result.band.toFixed(1)}`);
      } else if (component.type === 'writing_task1' || component.type === 'writing_task2') {
        console.log(`Successfully processed ${component.name}. The AI has analyzed the handwritten essay and provided feedback.`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      console.error('Error processing component:', err);
    } finally {
      // Clear processing state
      setProcessingComponents(prev => {
        const newState = { ...prev };
        delete newState[`${candidate.id}-${component.id}`];
        return newState;
      });
    }
  };

  // State for report generation
  const [generatingReports, setGeneratingReports] = useState<{[key: string]: boolean}>({});

  // Handle generating a report
  const handleGenerateReport = async (candidate: Candidate) => {
    // Check if all components are processed
    const allProcessed = candidate.components.every(c => c.status === 'processed');

    if (!allProcessed) {
      setError('All components must be processed before generating a report');
      return;
    }

    // Set generating state
    setGeneratingReports(prev => ({
      ...prev,
      [candidate.id]: true
    }));

    try {
      // Clear any previous errors
      setError(null);

      // Generate PDF with answer sheet analysis
      const fileName = await generateDemoPDF(candidate);
      console.log('Generated PDF with filename:', fileName);

      try {
        // Make API call to generate report
        console.log('Sending request to generate report for student:', candidate.studentId);
        const response = await fetch('/api/reports/generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            studentIds: [candidate.studentId],
            fileName: fileName, // Pass the generated PDF filename
          }),
        });

        console.log('Report generation response status:', response.status);

        // Check if the response is ok
        if (!response.ok) {
          const errorText = await response.text();
          let errorData;
          try {
            errorData = JSON.parse(errorText);
          } catch (parseError) {
            console.error('Failed to parse error response:', errorText);
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
          }
          throw new Error(errorData.error || 'Failed to generate report');
        }

        // Parse the response
        const responseText = await response.text();
        let result;
        try {
          result = JSON.parse(responseText);
        } catch (parseError) {
          console.error('Failed to parse response:', responseText);
          throw new Error('Invalid response from server');
        }

        // Update the candidate status
        const updatedCandidates = candidates.map(c => {
          if (c.id === candidate.id) {
            return {
              ...c,
              reportGenerated: true,
              reportId: result.reports[0]?.id
            };
          }
          return c;
        });

        setCandidates(updatedCandidates);
      } catch (apiError) {
        console.error('API error:', apiError);
        throw apiError;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      console.error('Error generating report:', err);
    } finally {
      // Clear generating state
      setGeneratingReports(prev => {
        const newState = { ...prev };
        delete newState[candidate.id];
        return newState;
      });
    }
  };

// Generate a premium professional PDF report for IELTS candidates
const generateDemoPDF = async (candidate: Candidate) => {
  try {
    console.log('Starting PDF generation for candidate:', candidate.studentId);

    // First, analyze any answer sheet images using OpenAI GPT-4.1
    console.log('Analyzing answer sheets with GPT-4.1...');
    const candidateWithAnalysis = await fetchAndAnalyzeAnswerSheets(candidate);
    console.log('Answer sheet analysis completed');

    // Then generate the PDF with the analyzed data
    console.log('Generating PDF with analyzed data...');
    const pdfResult = generateCandidateReport(candidateWithAnalysis);
    console.log('PDF generation completed successfully');

    return pdfResult;
  } catch (error) {
    console.error('Error generating PDF with answer sheet analysis:', error);
    // Fallback to generating PDF without analysis if there's an error
    console.log('Falling back to generating PDF without analysis...');
    const pdfResult = generateCandidateReport(candidate);
    console.log('Fallback PDF generation completed');
    return pdfResult;
  }
};

// Generate a demo PDF without making API calls
const handleDemoPDF = async (candidate: Candidate) => {
  // Log the candidate data for debugging
  console.log('Generating PDF for candidate:', candidate.studentId);
  console.log('Component data:', candidate.components.map(c => ({
    type: c.type,
    status: c.status,
    band: c.band,
    hasAnalysis: !!c.answerSheetAnalysis
  })));
  try {
    // Clear any previous errors
    setError(null);

    // Set loading state
    setGeneratingReports(prev => ({
      ...prev,
      [candidate.id]: true
    }));

    // Generate the PDF directly without making API calls
    const pdfResult = await generateDemoPDF(candidate);

    // Create a URL for the blob
    const url = window.URL.createObjectURL(pdfResult.pdfBlob);

    // Create a temporary link element
    const link = document.createElement('a');
    link.href = url;
    link.download = pdfResult.fileName;
    // Set target to _self to prevent browser confirmation dialog
    link.target = "_self";
    // Add rel attribute to prevent browser confirmation
    link.rel = "noopener noreferrer";

    // Append to the document, click it, and remove it
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the URL object
    window.URL.revokeObjectURL(url);

    // Show success message in the UI instead of a popup
    setSuccessMessage(`PDF report for ${candidate.studentId} downloaded successfully!`);
  } catch (err) {
    setError(`Error generating PDF: ${err instanceof Error ? err.message : 'Unknown error'}`);
    console.error('Error generating preview PDF:', err);
  } finally {
    // Clear generating state
    setGeneratingReports(prev => {
      const newState = { ...prev };
      delete newState[candidate.id];
      return newState;
    });
  }
};

// Handle direct download of PDF without requiring a report to be generated first
const handleDirectDownload = async (candidate: Candidate) => {
  try {
    // Clear any previous errors
    setError(null);

    // Set loading state
    setGeneratingReports(prev => ({
      ...prev,
      [candidate.id]: true
    }));

    console.log('Starting direct download for candidate:', candidate.studentId);

    // Use fetch to call the direct download API
    const response = await fetch('/api/reports/direct-download', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        studentId: candidate.studentId,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to download PDF: ${response.status} ${response.statusText}`);
    }

    // Get the blob from the response
    const blob = await response.blob();

    // Create a URL for the blob
    const url = window.URL.createObjectURL(blob);

    // Create a temporary link element
    const link = document.createElement('a');
    link.href = url;
    link.download = `IELTS_Report_${candidate.studentId}_${new Date().toISOString().slice(0, 10)}.pdf`;
    // Set target to _self to prevent browser confirmation dialog
    link.target = "_self";
    // Add rel attribute to prevent browser confirmation
    link.rel = "noopener noreferrer";

    // Append to the document, click it, and remove it
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the URL object
    window.URL.revokeObjectURL(url);

    // Show success message in the UI instead of a popup
    console.log('Direct PDF download completed');
    setSuccessMessage(`PDF report for ${candidate.studentId} downloaded successfully!`);
  } catch (err) {
    setError(`Error downloading report: ${err instanceof Error ? err.message : 'Unknown error'}`);
    console.error('Error with direct download:', err);
  } finally {
    // Clear generating state
    setGeneratingReports(prev => {
      const newState = { ...prev };
      delete newState[candidate.id];
      return newState;
    });
  }
};

// Handle downloading the actual report PDF
const handleDownloadReport = async (candidate: Candidate) => {
  if (!candidate.reportId) {
    setError('No report ID found. Please generate a report first.');
    return;
  }

  try {
    // Clear any previous errors
    setError(null);

    // Set loading state
    setGeneratingReports(prev => ({
      ...prev,
      [candidate.id]: true
    }));

    console.log('Downloading report for candidate:', candidate.studentId);

    // Create a direct download link to the PDF
    const downloadUrl = `/api/reports/download?id=${candidate.reportId}`;

    // Use fetch to get the PDF data directly
    const response = await fetch(downloadUrl);

    if (!response.ok) {
      throw new Error(`Failed to download PDF: ${response.status} ${response.statusText}`);
    }

    // Get the blob from the response
    const blob = await response.blob();

    // Create a URL for the blob
    const url = window.URL.createObjectURL(blob);

    // Create a temporary link element
    const link = document.createElement('a');
    link.href = url;
    link.download = `IELTS_Report_${candidate.studentId}.pdf`; // Suggest a filename
    // Set target to _self to prevent browser confirmation dialog
    link.target = "_self";
    // Add rel attribute to prevent browser confirmation
    link.rel = "noopener noreferrer";

    // Append to the document, click it, and remove it
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the URL object
    window.URL.revokeObjectURL(url);

    // Show success message in the UI instead of a popup
    console.log('PDF report download completed');
    setSuccessMessage(`PDF report for ${candidate.studentId} downloaded successfully!`);
  } catch (err) {
    setError(`Error downloading report: ${err instanceof Error ? err.message : 'Unknown error'}`);
    console.error('Error downloading report:', err);
  } finally {
    // Clear generating state
    setGeneratingReports(prev => {
      const newState = { ...prev };
      delete newState[candidate.id];
      return newState;
    });
  }
};

// Handle applying global answers and task questions to all candidates
const handleApplyToAllCandidates = async (specificComponent?: 'reading' | 'listening' | 'writing_task1' | 'writing_task2') => {
  try {
    setApplyingToAll(true);
    setError(null);

    // Create a copy of the candidates array
    const updatedCandidates = [...candidates];

    // Hide the material selection warning if it was shown
    setShowMaterialWarning(false);

    // First, update the UI state
    for (const candidate of updatedCandidates) {
      // Only update components that are specified or all if none specified
      // Update reading component
      if (!specificComponent || specificComponent === 'reading') {
        const readingComponent = candidate.components.find(c => c.type === 'reading');
        if (readingComponent && readingComponent.status !== 'processed') {
          // Split the comma-separated string into an array
          readingComponent.correctAnswers = globalReadingAnswers.split(',').map(a => a.trim());
        }
      }

      // Update listening component
      if (!specificComponent || specificComponent === 'listening') {
        const listeningComponent = candidate.components.find(c => c.type === 'listening');
        if (listeningComponent && listeningComponent.status !== 'processed') {
          // Split the comma-separated string into an array
          listeningComponent.correctAnswers = globalListeningAnswers.split(',').map(a => a.trim());
        }
      }

      // Update writing task 1 component
      if (!specificComponent || specificComponent === 'writing_task1') {
        const writingTask1Component = candidate.components.find(c => c.type === 'writing_task1');
        if (writingTask1Component && writingTask1Component.status !== 'processed') {
          writingTask1Component.taskQuestions = globalWritingTask1Question;

          // If there's an image for task 1, create a URL for it
          if (globalWritingTask1Image) {
            const imageUrl = URL.createObjectURL(globalWritingTask1Image);
            writingTask1Component.answerSheetImageUrl = imageUrl || undefined;
            console.log(`Applied task 1 image to candidate ${candidate.studentId}`);
          }
        }
      }

      // Update writing task 2 component
      if (!specificComponent || specificComponent === 'writing_task2') {
        const writingTask2Component = candidate.components.find(c => c.type === 'writing_task2');
        if (writingTask2Component && writingTask2Component.status !== 'processed') {
          writingTask2Component.taskQuestions = globalWritingTask2Question;
        }
      }
    }

    // Update the candidates state
    setCandidates(updatedCandidates);

    // Now, call the API to apply global answers to all entries in the database
    if (specificComponent === 'reading' || !specificComponent) {
      if (globalReadingAnswers.trim()) {
        try {
          const response = await fetch('/api/candidate-assessment/apply-global-answers', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              componentType: 'reading',
              correctAnswers: globalReadingAnswers
            }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            console.error('Error applying global reading answers:', errorData);
            throw new Error(errorData.error || 'Failed to apply global reading answers');
          }

          const result = await response.json();
          console.log('Global reading answers applied to database entries:', result);
        } catch (apiError) {
          console.error('API error applying global reading answers:', apiError);
          // Continue with the process even if the API call fails
        }
      }
    }

    if (specificComponent === 'listening' || !specificComponent) {
      if (globalListeningAnswers.trim()) {
        try {
          const response = await fetch('/api/candidate-assessment/apply-global-answers', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              componentType: 'listening',
              correctAnswers: globalListeningAnswers
            }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            console.error('Error applying global listening answers:', errorData);
            throw new Error(errorData.error || 'Failed to apply global listening answers');
          }

          const result = await response.json();
          console.log('Global listening answers applied to database entries:', result);
        } catch (apiError) {
          console.error('API error applying global listening answers:', apiError);
          // Continue with the process even if the API call fails
        }
      }
    }

    // Apply writing task 1 image to all candidates if available
    if ((specificComponent === 'writing_task1' || !specificComponent) && globalWritingTask1Image) {
      try {
        console.log('Uploading writing task 1 image to apply to all candidates...');

        // Create a FormData object for the file upload
        const formData = new FormData();
        formData.append('file', globalWritingTask1Image);
        formData.append('componentType', 'writing_task1');
        formData.append('taskQuestions', globalWritingTask1Question || 'Default Task 1 question: Describe the chart or graph.');
        formData.append('applyToAll', 'true');

        // Call the API to upload the task 1 image
        const response = await fetch('/api/candidate-assessment/apply-task-image', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('Error applying writing task 1 image:', errorData);
          throw new Error(errorData.error || 'Failed to apply writing task 1 image');
        }

        const result = await response.json();
        console.log('Writing task 1 image applied to all candidates:', result);
      } catch (apiError) {
        console.error('API error applying writing task 1 image:', apiError);
        // Continue with the process even if the API call fails
      }
    }

    // Log success message
    const message = specificComponent ?
      `${specificComponent.charAt(0).toUpperCase() + specificComponent.slice(1)} settings applied to all candidates successfully!` :
      'Global answers and task questions applied to all candidates successfully!';
    console.log(message);
    // Show success message in the UI
    setError(null); // Clear any previous errors
    setSuccessMessage(message);

    // Automatically clear the success message after 5 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 5000);
  } catch (err) {
    setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    console.error('Error applying global answers and task questions:', err);
  } finally {
    setApplyingToAll(false);
  }
};

/* Legacy PDF generator function - kept for reference only
   This function is intentionally unused but kept as a reference for the PDF generation logic.
   The actual PDF generation is now handled by the utils/pdf-generator.ts module.
   The IDE will show this as unused, which is expected.
*/
// @ts-ignore - Intentionally unused, kept for reference
const _generateDemoPDF = (candidate: Candidate) => {
  // Import jsPDF for legacy function
  const { jsPDF } = require('jspdf');
  // ========== DESIGN SYSTEM ==========
  // Premium color system with primary, secondary, and tertiary palettes
  const BRAND = {
    primary: {
      main: [16, 61, 136],      // Deep blue
      light: [72, 133, 237],    // Lighter blue
      dark: [10, 39, 89],       // Darker blue
      accent: [41, 98, 255],    // Bright blue accent
      contrast: [255, 255, 255] // White text on primary
    },
    secondary: {
      success: [30, 94, 31],      // Deep green
      successLight: [200, 230, 201], // Light green bg
      warning: [198, 93, 0],      // Deep orange
      warningLight: [255, 224, 178], // Light orange bg
      error: [176, 0, 32],        // Deep red
      errorLight: [255, 205, 210]  // Light red bg
    },
    neutral: {
      text: {
        primary: [33, 33, 33],       // Near black for body text
        secondary: [97, 97, 97],     // Dark gray for secondary text
        disabled: [158, 158, 158]    // Light gray for disabled text
      },
      background: {
        paper: [255, 255, 255],      // White
        default: [250, 250, 250],    // Off-white
        light: [245, 245, 245],      // Light gray
        medium: [224, 224, 224],     // Medium gray
        dark: [189, 189, 189]        // Dark gray
      },
      divider: [224, 224, 224]       // Line gray
    },
    charts: {
      blue: [72, 133, 237],
      green: [30, 142, 62],
      orange: [230, 124, 33],
      purple: [123, 31, 162],
      teal: [0, 131, 143],
      indigo: [48, 63, 159],
      pink: [216, 27, 96]
    }
  };

  // Professional typographic scale
  const TYPOGRAPHY = {
    h1: { size: 24, weight: 'bold', letterSpacing: 0.5 },
    h2: { size: 18, weight: 'bold', letterSpacing: 0.25 },
    h3: { size: 16, weight: 'bold', letterSpacing: 0 },
    h4: { size: 14, weight: 'bold', letterSpacing: 0.15 },
    h5: { size: 12, weight: 'bold', letterSpacing: 0.1 },
    subtitle1: { size: 11, weight: 'bold', letterSpacing: 0.15 },
    subtitle2: { size: 10, weight: 'medium', letterSpacing: 0.1 },
    body1: { size: 10, weight: 'normal', letterSpacing: 0.5 },
    body2: { size: 9, weight: 'normal', letterSpacing: 0.25 },
    caption: { size: 8, weight: 'normal', letterSpacing: 0.4 },
    button: { size: 10, weight: 'medium', letterSpacing: 1.25 }
  };

  // Professional spacing system (in mm)
  const SPACING = {
    xs: 2,
    sm: 4,
    md: 8,
    lg: 16,
    xl: 24,
    xxl: 32,
    xxxl: 48,
    // Page margins
    margin: {
      top: 20,
      right: 20,
      bottom: 20,
      left: 20
    }
  };

  // Shadow definitions for depth
  const SHADOWS = {
    sm: { offsetX: 0.5, offsetY: 0.5, blur: 1, color: [0, 0, 0, 0.1] },
    md: { offsetX: 1, offsetY: 1, blur: 2, color: [0, 0, 0, 0.15] },
    lg: { offsetX: 2, offsetY: 2, blur: 4, color: [0, 0, 0, 0.2] }
  };

  // Border radius settings
  const BORDER_RADIUS = {
    sm: 1,
    md: 2,
    lg: 4,
    xl: 8,
    circle: 999
  };

  // Line weights
  const LINE_WEIGHT = {
    thin: 0.1,
    regular: 0.25,
    medium: 0.5,
    thick: 1,
    border: 1.5
  };

  // ========== PDF DOCUMENT SETUP ==========
  // Create PDF document
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4',
  });

  // Page dimensions
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  const contentWidth = pageWidth - (SPACING.margin.left + SPACING.margin.right);

  // Initialize tracking variables
  let currentY = SPACING.margin.top;
  let pageCount = 1;

  // ========== UTILITY FUNCTIONS ==========
  // Apply typography styles
  const applyTypography = (style: keyof typeof TYPOGRAPHY, color: any = BRAND.neutral.text.primary) => {
    const typeSetting = TYPOGRAPHY[style];
    doc.setFontSize(typeSetting.size);
    doc.setFont('helvetica', typeSetting.weight);
    doc.setTextColor(...color);
    // Return the line height appropriate for this text style
    return typeSetting.size * 0.35 + 2;
  };

  // Apply shadow to an element
  const applyShadow = (shadow: keyof typeof SHADOWS, x: number, y: number, width: number, height: number) => {
    const shadowDef = SHADOWS[shadow];
    doc.setFillColor(...shadowDef.color);
    doc.rect(
      x + shadowDef.offsetX,
      y + shadowDef.offsetY,
      width,
      height,
      'F'
    );
  };

  // Define the border radius type
  type BorderRadiusObject = { tl: number; tr: number; bl: number; br: number };

  // Draw a rounded rectangle with optional shadow
  const drawRoundedRect = (
    x: number,
    y: number,
    width: number,
    height: number,
    radius: number | BorderRadiusObject = BORDER_RADIUS.md,
    fillColor: number[] = BRAND.neutral.background.paper,
    strokeColor?: number[],
    withShadow: keyof typeof SHADOWS | null = null
  ) => {
    // Add shadow if specified
    if (withShadow) {
      applyShadow(withShadow, x, y, width, height);
    }

    // Draw filled rectangle
    doc.setFillColor(...fillColor);

    // Handle different radius types
    if (typeof radius === 'object') {
      // For object radius, we need to use a number for the actual roundedRect call
      // jsPDF doesn't actually support different radii for each corner in its API
      // So we'll use the top-left radius as a fallback
      doc.roundedRect(x, y, width, height, radius.tl, radius.tl, 'F');
    } else {
      // Use the same radius for all corners
      doc.roundedRect(x, y, width, height, radius, radius, 'F');
    }

    // Add stroke if specified
    if (strokeColor) {
      doc.setDrawColor(...strokeColor);
      doc.setLineWidth(LINE_WEIGHT.regular);

      // Handle different radius types for stroke
      if (typeof radius === 'object') {
        // For object radius, use the top-left radius as a fallback
        doc.roundedRect(x, y, width, height, radius.tl, radius.tl, 'S');
      } else {
        doc.roundedRect(x, y, width, height, radius, radius, 'S');
      }
    }
  };

  // Check and add a new page if needed
  const checkAndAddPage = (height: number) => {
    if (currentY + height > pageHeight - SPACING.margin.bottom) {
      addFooter();
      doc.addPage();
      pageCount++;
      addHeaderToPage();
      currentY = SPACING.margin.top + 25; // Start below the header
      return true;
    }
    return false;
  };

  // Add premium footer to current page (modernized design)
  const addFooter = () => {
    const footerY = pageHeight - SPACING.margin.bottom;

    // Add footer bar with clean design
    const gradientHeight = 1; // Thin line instead of thick bar
    doc.setFillColor(...BRAND.primary.main);
    doc.rect(SPACING.margin.left, footerY - gradientHeight, contentWidth, gradientHeight, 'F');

    // Add page information
    applyTypography('caption', BRAND.neutral.text.secondary);
    doc.text(`Page ${pageCount}`, pageWidth / 2, footerY + 4, { align: 'center' });

    // Add confidentiality statement
    applyTypography('caption', BRAND.neutral.text.secondary);
    doc.text('CONFIDENTIAL - For internal assessment purposes only', pageWidth / 2, footerY + 8, { align: 'center' });

    // Add small logo in bottom right (rectangular instead of circular)
    drawRoundedRect(pageWidth - SPACING.margin.right - 15, footerY, 15, 6, BORDER_RADIUS.sm, BRAND.primary.main);
    applyTypography('caption', BRAND.primary.contrast);
    doc.text('IELTS', pageWidth - SPACING.margin.right - 7.5, footerY + 4, { align: 'center' });
  };

  // Add premium header to current page (modernized design)
  const addHeaderToPage = () => {
    // Draw clean header bar
    doc.setFillColor(...BRAND.primary.main);
    doc.rect(0, 0, pageWidth, 20, 'F');

    // Add logo (rectangular instead of circular)
    drawRoundedRect(SPACING.margin.left, 5, 30, 10, BORDER_RADIUS.sm, BRAND.primary.contrast);
    applyTypography('h4', BRAND.primary.main);
    doc.text('IELTS', SPACING.margin.left + 15, 11, { align: 'center' });

    // Add title
    applyTypography('h2', BRAND.primary.contrast);
    doc.text('IELTS ASSESSMENT REPORT', pageWidth / 2, 13, { align: 'center' });

    // Add decorative element (line under header)
    doc.setFillColor(...BRAND.primary.light);
    doc.rect(SPACING.margin.left, 22, contentWidth, 1, 'F');
  };



  // Draw badge with label
  const drawBadge = (
    x: number,
    y: number,
    text: string,
    color: number[] = BRAND.primary.main,
    textColor: number[] = BRAND.primary.contrast
  ) => {
    const textWidth = doc.getTextWidth(text);
    const padding = SPACING.xs;
    const width = textWidth + padding * 4;

    // Draw badge background
    drawRoundedRect(x, y, width, 6, BORDER_RADIUS.md, color);

    // Add text
    applyTypography('caption', textColor);
    doc.text(text, x + width/2, y + 4, { align: 'center' });

    return width; // Return width for positioning subsequent elements
  };

  // Draw section header
  const drawSectionHeader = (
    y: number,
    title: string,
    withLine = true,
    withIcon = false,
    color: number[] = BRAND.primary.main
  ) => {
    // Draw decorative line
    if (withLine) {
      doc.setDrawColor(...color);
      doc.setLineWidth(LINE_WEIGHT.medium);
      doc.line(SPACING.margin.left, y, pageWidth - SPACING.margin.right, y);
    }

    // Draw icon if requested
    if (withIcon) {
      doc.setFillColor(...color);
      doc.circle(SPACING.margin.left + 3, y - 5, 1.5, 'F');
    }

    // Add title text
    applyTypography('h3', color);
    doc.text(title, withIcon ? SPACING.margin.left + 8 : SPACING.margin.left, y - 5);

    return y + SPACING.md; // Return new Y position
  };

  // Draw colored section header
  const drawColoredSectionHeader = (
    y: number,
    title: string,
    color: number[] = BRAND.primary.main,
    contrastColor: number[] = BRAND.primary.contrast
  ) => {
    // Draw header background
    drawRoundedRect(
      SPACING.margin.left,
      y,
      contentWidth,
      10,
      BORDER_RADIUS.md,
      color,
      undefined,
      'sm'
    );

    // Add title text
    applyTypography('h4', contrastColor);
    doc.text(title, SPACING.margin.left + SPACING.md, y + 6.5);

    return y + 10 + SPACING.sm; // Return new Y position
  };

  // Draw bullet point (using small squares instead of circles)
  const drawBulletPoint = (
    x: number,
    y: number,
    text: string,
    color: number[] = BRAND.primary.main
  ) => {
    // Draw square bullet
    doc.setFillColor(...color);
    doc.rect(x - 1, y + 0.5, 2, 2, 'F');

    // Add text with wrapping
    applyTypography('body1');
    const lines = doc.splitTextToSize(text, contentWidth - (x - SPACING.margin.left) - SPACING.md);
    doc.text(lines, x + 4, y + 2);

    return y + (lines.length * 4) + 2; // Return new Y position
  };

  // Draw table
  const drawTable = (
    y: number,
    headers: string[],
    rows: string[][],
    columnWidths: number[],
    headerColor: number[] = BRAND.primary.main,
    alternateRows = true,
    highlightColumn = -1
  ) => {
    const rowHeight = 8;
    const tableWidth = columnWidths.reduce((a, b) => a + b, 0);

    // Draw header row
    drawRoundedRect(
      SPACING.margin.left,
      y,
      tableWidth,
      rowHeight,
      { tl: BORDER_RADIUS.md, tr: BORDER_RADIUS.md, bl: 0, br: 0 },
      headerColor
    );

    // Add header text
    applyTypography('subtitle2', BRAND.primary.contrast);
    let xOffset = SPACING.margin.left;
    headers.forEach((header, i) => {
      doc.text(header, xOffset + columnWidths[i]/2, y + rowHeight - 2.5, { align: 'center' });
      xOffset += columnWidths[i];
    });

    // Draw data rows
    let currentRowY = y + rowHeight;

    rows.forEach((row, rowIndex) => {
      // Apply alternating row background
      if (alternateRows && rowIndex % 2 === 1) {
        drawRoundedRect(
          SPACING.margin.left,
          currentRowY,
          tableWidth,
          rowHeight,
          0,
          BRAND.neutral.background.light
        );
      }

      // Add row data
      let xOffset = SPACING.margin.left;
      row.forEach((cell, cellIndex) => {
        // Use different styling for highlighted column
        if (cellIndex === highlightColumn) {
          applyTypography('subtitle2', BRAND.primary.main);
        } else {
          applyTypography('body1');
        }

        // Align cell content based on position
        const align = cellIndex === 0 ? 'left' : 'center';
        const xPos = align === 'left'
          ? xOffset + SPACING.xs
          : xOffset + columnWidths[cellIndex]/2;

        doc.text(cell, xPos, currentRowY + rowHeight - 2.5,
          align === 'left' ? undefined : { align: 'center' });

        xOffset += columnWidths[cellIndex];
      });

      currentRowY += rowHeight;
    });

    // Draw bottom border
    if (rows.length > 0) {
      drawRoundedRect(
        SPACING.margin.left,
        currentRowY,
        tableWidth,
        0.5,
        { tl: 0, tr: 0, bl: BORDER_RADIUS.md, br: BORDER_RADIUS.md },
        BRAND.neutral.divider
      );
      currentRowY += 0.5;
    }

    return currentRowY + SPACING.md; // Return new Y position
  };

  // Draw bar chart
  const drawBarChart = (
    y: number,
    data: { label: string, value: number, color: number[] }[],
    maxValue: number,
    targetLine?: number
  ) => {
    const chartWidth = contentWidth;
    const chartHeight = 60;
    const barSpacing = 5;
    const barWidth = (chartWidth - ((data.length + 1) * barSpacing)) / data.length;

    // Draw chart background with grid lines
    drawRoundedRect(
      SPACING.margin.left,
      y,
      chartWidth,
      chartHeight,
      BORDER_RADIUS.md,
      BRAND.neutral.background.light,
      BRAND.neutral.divider,
      'sm'
    );

    // Draw horizontal grid lines
    doc.setDrawColor(...BRAND.neutral.divider);
    doc.setLineWidth(LINE_WEIGHT.thin);

    // Draw score labels and grid lines
    applyTypography('caption', BRAND.neutral.text.secondary);
    for (let i = 0; i <= maxValue; i++) {
      if (i % 1 === 0) { // Only whole numbers
        const lineY = y + chartHeight - ((i / maxValue) * chartHeight);

        // Draw grid line
        doc.setLineDashPattern([1, 1], 0);
        doc.line(SPACING.margin.left + SPACING.xs, lineY, SPACING.margin.left + chartWidth - SPACING.xs, lineY);
        doc.setLineDashPattern([], 0);

        // Draw label
        doc.text(i.toString(), SPACING.margin.left - 3, lineY, { align: 'right' });
      }
    }

    // Draw target line if provided
    if (targetLine !== undefined) {
      const targetY = y + chartHeight - ((targetLine / maxValue) * chartHeight);
      doc.setDrawColor(...BRAND.neutral.text.secondary);
      doc.setLineWidth(LINE_WEIGHT.regular);
      doc.setLineDashPattern([3, 2], 0);
      doc.line(SPACING.margin.left + SPACING.xs, targetY, SPACING.margin.left + chartWidth - SPACING.xs, targetY);
      doc.setLineDashPattern([], 0);

      // Add target label
      applyTypography('caption', BRAND.neutral.text.secondary);
      doc.text('Target', SPACING.margin.left + chartWidth + 3, targetY, { align: 'left' });
    }

    // Draw bars
    data.forEach((item, index) => {
      const barHeight = (item.value / maxValue) * (chartHeight - SPACING.sm);
      const barX = SPACING.margin.left + (barSpacing * (index + 1)) + (barWidth * index);
      const barY = y + chartHeight - barHeight - SPACING.xs;

      // Draw bar shadow
      applyShadow('sm', barX, barY, barWidth, barHeight);

      // Draw bar with gradient effect
      doc.setFillColor(...item.color);
      // Use our custom drawRoundedRect instead of direct call
      drawRoundedRect(
        barX, barY, barWidth, barHeight,
        { tl: BORDER_RADIUS.md, tr: BORDER_RADIUS.md, bl: 0, br: 0 },
        item.color
      );

      // Add subtle 3D effect with darker bottom
      const darkerColor = item.color.map(c => Math.max(0, c - 40));
      doc.setFillColor(...darkerColor);
      // Use our custom drawRoundedRect instead of direct call
      drawRoundedRect(
        barX, barY + barHeight - 2, barWidth, 2,
        { tl: 0, tr: 0, bl: BORDER_RADIUS.md, br: BORDER_RADIUS.md },
        darkerColor
      );

      // Add value in circle above bar
      drawRoundedRect(barX + barWidth/2 - 5, barY - 10, 10, 10, BORDER_RADIUS.circle,
        BRAND.neutral.background.paper, item.color, 'sm');
      applyTypography('subtitle2', item.color);
      doc.text(item.value.toString(), barX + barWidth/2, barY - 4, { align: 'center' });

      // Add label below bar
      applyTypography('caption');
      doc.text(item.label, barX + barWidth/2, y + chartHeight + 5, { align: 'center' });
    });

    return y + chartHeight + SPACING.lg; // Return new Y position
  };



  // Draw component analysis section
  const drawComponentAnalysis = (y: number, component: any) => {
    let currentY = y;

    checkAndAddPage(100);

    // Draw component header
    drawRoundedRect(
      SPACING.margin.left,
      currentY,
      contentWidth,
      12,
      BORDER_RADIUS.md,
      BRAND.primary.main,
      undefined,
      'sm'
    );

    // Add component title
    applyTypography('h4', BRAND.primary.contrast);
    doc.text(`${component.component} Analysis`, SPACING.margin.left + SPACING.md, currentY + 8);

    // Determine score color
    let scoreColor = BRAND.secondary.warning;
    if (component.score >= 8) scoreColor = BRAND.secondary.success;
    else if (component.score >= 7) scoreColor = BRAND.secondary.success;

    // Add score badge
    const badgeWidth = 16;
    drawRoundedRect(
      SPACING.margin.left + contentWidth - badgeWidth - SPACING.xs,
      currentY + 2,
      badgeWidth,
      8,
      BORDER_RADIUS.md,
      BRAND.neutral.background.paper,
      scoreColor,
      'sm'
    );

    applyTypography('h5', scoreColor);
    doc.text(
      component.score.toString(),
      SPACING.margin.left + contentWidth - badgeWidth/2 - SPACING.xs,
      currentY + 7,
      { align: 'center' }
    );

    currentY += 12 + SPACING.md;

    // Draw breakdown by section
    applyTypography('subtitle1');
    doc.text('Breakdown by Section:', SPACING.margin.left, currentY);
    currentY += SPACING.md;

    // Format criteria data for table
    const criteriaHeaders = ['Section', 'Score'];
    const criteriaRows = component.criteriaScores.map((criteria: any) => [
      criteria.name, criteria.score
    ]);

    // Draw table with fixed column widths
    currentY = drawTable(
      currentY,
      criteriaHeaders,
      criteriaRows,
      [contentWidth * 0.7, contentWidth * 0.3],
      BRAND.primary.main,
      true,
      1 // Highlight score column
    );

    // Add mistakes by question types section
    applyTypography('subtitle1', BRAND.secondary.warning);
    doc.text('Mistakes by Question Types:', SPACING.margin.left, currentY);
    currentY += SPACING.xs;

    // Add weaknesses content in a box
    drawRoundedRect(
      SPACING.margin.left,
      currentY,
      contentWidth,
      14,
      BORDER_RADIUS.md,
      BRAND.secondary.warningLight,
      BRAND.secondary.warning
    );

    applyTypography('body1');
    const weaknessLines = doc.splitTextToSize(component.weaknesses, contentWidth - SPACING.md);
    doc.text(weaknessLines, SPACING.margin.left + SPACING.xs, currentY + SPACING.xs);
    currentY += 14 + SPACING.md;

    // Add strengths section
    applyTypography('subtitle1', BRAND.secondary.success);
    doc.text('Strengths by Question Type:', SPACING.margin.left, currentY);
    currentY += SPACING.xs;

    // Add strengths content in a box
    drawRoundedRect(
      SPACING.margin.left,
      currentY,
      contentWidth,
      14,
      BORDER_RADIUS.md,
      BRAND.secondary.successLight,
      BRAND.secondary.success
    );

    applyTypography('body1');
    const strengthLines = doc.splitTextToSize(component.strengths, contentWidth - SPACING.md);
    doc.text(strengthLines, SPACING.margin.left + SPACING.xs, currentY + SPACING.xs);
    currentY += 14 + SPACING.md;

    return currentY + SPACING.lg; // Return the new Y position
  };



  // Draw recommendation section
  const drawRecommendation = (y: number, rec: any) => {
    let currentY = y;

    checkAndAddPage(80);

    // Area heading with timeframe
    const headingText = `${rec.area} (Timeframe: ${rec.timeframe})`;
    applyTypography('h4', BRAND.primary.main);
    doc.text(headingText, SPACING.margin.left, currentY);

    // Add underline
    doc.setDrawColor(...BRAND.primary.main);
    doc.setLineWidth(LINE_WEIGHT.thin);
    doc.line(SPACING.margin.left, currentY + 2, SPACING.margin.left + doc.getTextWidth(headingText), currentY + 2);

    currentY += SPACING.md;

    // Add recommendation in a box
    drawRoundedRect(
      SPACING.margin.left,
      currentY,
      contentWidth,
      14,
      BORDER_RADIUS.md,
      BRAND.neutral.background.light
    );

    applyTypography('body1');
    const recLines = doc.splitTextToSize(rec.recommendation, contentWidth - SPACING.md);
    doc.text(recLines, SPACING.margin.left + SPACING.xs, currentY + SPACING.xs);
    currentY += 14 + SPACING.md;

    // Add exercises section
    applyTypography('subtitle1', BRAND.primary.main);
    doc.text('Suggested Exercises:', SPACING.margin.left, currentY);
    currentY += SPACING.md;

    // Draw exercises in a box with numbered bullets
    drawRoundedRect(
      SPACING.margin.left,
      currentY,
      contentWidth,
      rec.exercises.length * 8 + SPACING.md,
      BORDER_RADIUS.md,
      BRAND.neutral.background.paper,
      BRAND.neutral.divider,
      'sm'
    );

    let exerciseY = currentY + SPACING.xs;

    rec.exercises.forEach((exercise: string, index: number) => {
      // Create numbered bullet
      drawRoundedRect(
        SPACING.margin.left + SPACING.sm,
        exerciseY,
        6,
        6,
        BORDER_RADIUS.circle,
        BRAND.primary.main
      );

      applyTypography('caption', BRAND.primary.contrast);
      doc.text((index + 1).toString(), SPACING.margin.left + SPACING.sm + 3, exerciseY + 4, { align: 'center' });

      // Add exercise text
      applyTypography('body1');
      const exerciseLines = doc.splitTextToSize(exercise, contentWidth - SPACING.lg - 6);
      doc.text(exerciseLines, SPACING.margin.left + SPACING.lg, exerciseY + 4);
      exerciseY += 8;
    });

    currentY += rec.exercises.length * 8 + SPACING.lg;

    // Add resources section
    applyTypography('subtitle1', BRAND.secondary.success);
    doc.text('Recommended Resources:', SPACING.margin.left, currentY);
    currentY += SPACING.md;

    // Draw resources in a box with icon bullets
    drawRoundedRect(
      SPACING.margin.left,
      currentY,
      contentWidth,
      rec.resources.length * 8 + SPACING.md,
      BORDER_RADIUS.md,
      BRAND.secondary.successLight,
      BRAND.secondary.success,
      'sm'
    );

    let resourceY = currentY + SPACING.xs;

    rec.resources.forEach((resource: string) => {
      // Draw resource icon (book symbol)
      doc.setFillColor(...BRAND.secondary.success);
      doc.rect(SPACING.margin.left + SPACING.sm, resourceY + 1, 4, 3, 'F');
      doc.setDrawColor(...BRAND.secondary.success);
      doc.setLineWidth(LINE_WEIGHT.thin);
      doc.line(SPACING.margin.left + SPACING.sm, resourceY + 1, SPACING.margin.left + SPACING.sm, resourceY + 5);

      // Add resource text
      applyTypography('body1');
      const resourceLines = doc.splitTextToSize(resource, contentWidth - SPACING.lg - 5);
      doc.text(resourceLines, SPACING.margin.left + SPACING.lg, resourceY + 4);
      resourceY += 8;
    });

    return currentY + rec.resources.length * 8 + SPACING.lg; // Return new Y position
  };

  // ========== BEGIN PDF GENERATION ==========
  // Add header to first page
  addHeaderToPage();

  // ========== CANDIDATE INFORMATION SECTION ==========
  // Add premium info card with shadow effect
  drawRoundedRect(
    SPACING.margin.left,
    currentY,
    contentWidth,
    45,
    BORDER_RADIUS.md,
    BRAND.neutral.background.paper,
    BRAND.neutral.divider,
    'md'
  );

  // Add candidate info header
  drawRoundedRect(
    SPACING.margin.left,
    currentY,
    contentWidth,
    10,
    { tl: BORDER_RADIUS.md, tr: BORDER_RADIUS.md, bl: 0, br: 0 },
    BRAND.primary.main
  );

  applyTypography('h4', BRAND.primary.contrast);
  doc.text('CANDIDATE ASSESSMENT SUMMARY', SPACING.margin.left + SPACING.md, currentY + 6.5);

  // Add candidate information
  currentY += 10 + SPACING.xs;

  applyTypography('subtitle1');
  doc.text('Candidate ID:', SPACING.margin.left + SPACING.md, currentY);

  applyTypography('body1', BRAND.primary.main);
  doc.text(candidate.studentId, SPACING.margin.left + 30, currentY);

  currentY += SPACING.md;

  applyTypography('subtitle1');
  doc.text('Test Date:', SPACING.margin.left + SPACING.md, currentY);

  applyTypography('body1');
  doc.text(new Date().toLocaleDateString('en-GB'), SPACING.margin.left + 30, currentY);

  currentY += SPACING.md;

  // Add "Official Assessment" badge if needed
  drawBadge(
    SPACING.margin.left + contentWidth - 40,
    currentY - 10,
    'OFFICIAL ASSESSMENT',
    BRAND.primary.accent
  );

  currentY += SPACING.md;

  // ========== OVERALL SCORE SECTION ==========
  const overallScore = 7.5; // This should be calculated from component scores

  // Draw clean background element
  drawRoundedRect(
    SPACING.margin.left,
    currentY,
    contentWidth,
    40,
    BORDER_RADIUS.md,
    [245, 250, 255], // Very light blue background
    BRAND.primary.main
  );

  // Add section title
  applyTypography('h3', BRAND.primary.main);
  doc.text('OVERALL ASSESSMENT', SPACING.margin.left + SPACING.md, currentY + 12);

  // Draw band score box on the right side
  drawRoundedRect(
    pageWidth - SPACING.margin.right - 80,
    currentY + 5,
    70,
    30,
    BORDER_RADIUS.md,
    BRAND.neutral.background.paper,
    BRAND.primary.main,
    'sm'
  );

  // Add BAND SCORE label
  applyTypography('subtitle1', BRAND.primary.main);
  doc.text('BAND SCORE', pageWidth - SPACING.margin.right - 45, currentY + 12, { align: 'center' });

  // Add score value
  applyTypography('h1', BRAND.primary.main);
  doc.text(overallScore.toString(), pageWidth - SPACING.margin.right - 45, currentY + 25, { align: 'center' });

  // Add 'Out of 9.0' text
  applyTypography('caption', BRAND.neutral.text.secondary);
  doc.text('Out of 9.0', pageWidth - SPACING.margin.right - 45, currentY + 32, { align: 'center' });

  currentY += 50;

  // ========== TEST RESULTS SECTION ==========
  currentY = drawSectionHeader(currentY, 'TEST RESULTS');

  // Draw a horizontal line below the section header
  doc.setDrawColor(...BRAND.primary.main);
  doc.setLineWidth(LINE_WEIGHT.medium);
  doc.line(SPACING.margin.left, currentY - SPACING.md, pageWidth - SPACING.margin.right, currentY - SPACING.md);

  // Create a premium table for test results
  const tableData = [
    ['Listening', '7.5', 'Good'],
    ['Reading', '8.0', 'Very Good'],
    ['Writing', '6.5', 'Competent'],
    ['Speaking', '7.0', 'Good'],
  ];

  currentY = drawTable(
    currentY,
    ['Component', 'Band Score', 'Performance Level'],
    tableData,
    [contentWidth * 0.4, contentWidth * 0.3, contentWidth * 0.3],
    BRAND.primary.main,
    true,
    1 // Highlight the score column
  );

  // ========== DETAILED COMPONENT ANALYSIS SECTION ==========
  checkAndAddPage(40);
  currentY = drawSectionHeader(currentY, 'DETAILED COMPONENT ANALYSIS');

  // Define component-specific performance data
  const componentAnalysis = [
    {
      component: 'Listening',
      score: 7.5,
      criteriaScores: [
        { name: 'Section 1 (Social)', score: '8/10' },
        { name: 'Section 2 (Training)', score: '7/10' },
        { name: 'Section 3 (Academic)', score: '8/10' },
        { name: 'Section 4 (Academic Lecture)', score: '7/10' }
      ],
      strengths: 'Strong ability to identify specific factual information in short answer questions. Good comprehension of main ideas in academic contexts. Effective at following directions and understanding spatial information. Accurate note-taking for key details in lectures.',
      weaknesses: 'Difficulty with multiple choice questions requiring inference. Challenges with form completion tasks requiring precise spelling. Problems distinguishing similar-sounding words in Section 3. Occasional misunderstanding of speaker intentions in academic discussions.'
    },
    {
      component: 'Reading',
      score: 8.0,
      criteriaScores: [
        { name: 'Section 1 (Social/Training)', score: '9/10' },
        { name: 'Section 2 (Workplace)', score: '8/10' },
        { name: 'Section 3 (Academic)', score: '7/10' }
      ],
      strengths: "Excellent skimming and scanning skills in identifying specific information. Strong ability to match headings to paragraphs. Good understanding of academic texts and author's main arguments. Effective at identifying true/false/not given statements.",
      weaknesses: "Difficulty with inference questions requiring deeper analysis. Challenges with understanding complex vocabulary in academic contexts. Occasional misinterpretation of author's purpose in argumentative texts. Problems with sentence completion tasks requiring precise wording."
    },
    {
      component: 'Writing',
      score: 6.5,
      criteriaScores: [
        { name: 'Task 1', score: '7' },
        { name: 'Task 2', score: '6' },
        { name: 'Task Achievement', score: '6' },
        { name: 'Coherence & Cohesion', score: '7' },
        { name: 'Lexical Resource', score: '6' },
        { name: 'Grammatical Range & Accuracy', score: '7' }
      ],
      strengths: 'Good organization of ideas with clear paragraphing structure. Appropriate use of cohesive devices to connect ideas. Attempts to use less common vocabulary in Task 2. Effective data description and comparison in Task 1. Good sentence variety and complex structures when discussing familiar topics.',
      weaknesses: 'Limited development of ideas in Task 2 essay. Overreliance on simple sentence structures in complex arguments. Limited range of academic vocabulary in Task 2. Imprecise use of data vocabulary in Task 1. Occasional grammatical errors when attempting complex structures.'
    },
    {
      component: 'Speaking',
      score: 7.0,
      criteriaScores: [
        { name: 'Part 1 (Introduction)', score: '7.5' },
        { name: 'Part 2 (Long Turn)', score: '6.5' },
        { name: 'Part 3 (Discussion)', score: '7.0' },
        { name: 'Fluency & Coherence', score: '7' },
        { name: 'Lexical Resource', score: '7' },
        { name: 'Grammatical Range & Accuracy', score: '6' },
        { name: 'Pronunciation', score: '8' }
      ],
      strengths: 'Good fluency with minimal hesitation in familiar topics. Clear pronunciation with effective intonation patterns. Able to develop responses with appropriate detail in Part 2. Good use of discourse markers to organize ideas. Effective at expressing opinions on abstract topics in Part 3.',
      weaknesses: 'Occasional hesitation and use of filler words in Part 2. Some repetition of vocabulary when discussing complex topics. Limited use of less common expressions and idiomatic language. Grammatical errors in complex structures, particularly in Part 3 discussion. Difficulty elaborating ideas under time pressure.'
    }
  ];

  // Add each component analysis
  componentAnalysis.forEach(component => {
    currentY = drawComponentAnalysis(currentY, component);
  });

  // ========== PERFORMANCE ANALYSIS SECTION ==========
  checkAndAddPage(40);
  currentY = drawColoredSectionHeader(currentY, 'PERFORMANCE ANALYSIS', BRAND.primary.main);

  // Add strengths section
  currentY = drawColoredSectionHeader(currentY, 'STRENGTHS', BRAND.secondary.success);

  const strengths = [
    'Strong vocabulary usage in writing tasks with appropriate academic terms',
    'Excellent reading comprehension skills, particularly in identifying main ideas',
    'Good fluency and pronunciation in speaking with minimal hesitation',
    'Effective note-taking during listening tasks with good attention to detail'
  ];

  // Add strengths with premium bullets
  strengths.forEach(strength => {
    currentY = drawBulletPoint(
      SPACING.margin.left + SPACING.xs,
      currentY,
      strength,
      BRAND.secondary.success
    );
  });

  currentY += SPACING.lg;



  // ========== AREAS FOR IMPROVEMENT SECTION ==========
  checkAndAddPage(40);
  currentY = drawColoredSectionHeader(currentY, 'AREAS FOR IMPROVEMENT', BRAND.secondary.warning);

  const weaknesses = [
    'Grammar accuracy in complex sentences needs refinement',
    'Task achievement in Writing Task 2 requires better argument development',
    'Understanding of academic vocabulary in reading could be enhanced',
    'Note-taking efficiency during listening tasks needs improvement'
  ];

  // Add weaknesses with premium bullets
  weaknesses.forEach(weakness => {
    currentY = drawBulletPoint(
      SPACING.margin.left + SPACING.xs,
      currentY,
      weakness,
      BRAND.secondary.warning
    );
  });

  currentY += SPACING.lg;

  // ========== DETAILED IMPROVEMENT PLAN SECTION ==========
  checkAndAddPage(40);
  currentY = drawColoredSectionHeader(currentY, 'DETAILED IMPROVEMENT PLAN', BRAND.primary.main);

  // Define detailed recommendations
  const detailedRecommendations = [
    {
      area: 'Grammar',
      recommendation: 'Practice writing complex sentences with proper verb tense consistency',
      exercises: [
        'Complete 10 sentence transformation exercises focusing on past/present tense shifts',
        'Rewrite 5 paragraphs from your essay correcting all verb tense inconsistencies',
        'Study and apply the rules for sequence of tenses in complex sentences'
      ],
      resources: [
        'Cambridge Grammar for IELTS (Unit 5-7)',
        'Grammar in Use Intermediate - Units on tense consistency',
        'Online: British Council Learn English - Verb Tenses section'
      ],
      timeframe: '2-3 weeks'
    },
    {
      area: 'Academic Vocabulary',
      recommendation: 'Expand range of formal expressions and academic collocations',
      exercises: [
        'Create a personal academic word list with 50 new terms and use each in a sentence',
        'Identify and replace informal expressions in your Writing Task 2 essay',
        'Practice using academic collocations in paragraph writing'
      ],
      resources: [
        'Academic Word List by Averil Coxhead',
        'Oxford Academic Vocabulary Practice books',
        'IELTS Vocabulary: Academic Words (Collins)'
      ],
      timeframe: '4 weeks'
    },
    {
      area: 'Coherence & Cohesion',
      recommendation: 'Improve paragraph transitions and linking devices',
      exercises: [
        'Analyze model essays to identify all transition signals and linking phrases',
        'Rewrite your essays using a wider range of cohesive devices',
        'Practice organizing ideas using logical connectors'
      ],
      resources: [
        'IELTS Writing Task 2: Linking Words and Phrases Guide',
        'Writing for IELTS (Collins) - Chapter on Coherence',
        'Online: IELTS Advantage - Cohesion Tutorial'
      ],
      timeframe: '3 weeks'
    }
  ];

  // Add each recommendation section
  detailedRecommendations.forEach(rec => {
    currentY = drawRecommendation(currentY, rec);
  });

  // ========== BAND SCORE VISUALIZATION SECTION ==========
  checkAndAddPage(120);
  currentY = drawSectionHeader(currentY, 'BAND SCORE VISUALIZATION');

  // Add heading for chart
  applyTypography('subtitle1', BRAND.primary.main);
  doc.text('Component Performance Comparison', SPACING.margin.left, currentY);
  currentY += SPACING.md;

  // Draw a horizontal line below the section header
  doc.setDrawColor(...BRAND.primary.main);
  doc.setLineWidth(LINE_WEIGHT.thin);
  doc.line(SPACING.margin.left, currentY - SPACING.sm, pageWidth - SPACING.margin.right, currentY - SPACING.sm);

  // Prepare premium chart data
  const chartData = [
    { label: 'Listening', value: 7.5, color: BRAND.charts.blue },
    { label: 'Reading', value: 8.0, color: BRAND.charts.green },
    { label: 'Writing', value: 6.5, color: BRAND.charts.orange },
    { label: 'Speaking', value: 7.0, color: BRAND.charts.purple },
    { label: 'Overall', value: 7.5, color: BRAND.primary.main }
  ];

  // Draw premium bar chart
  currentY = drawBarChart(currentY, chartData, 9, 7.0);

  // Add band descriptors section
  applyTypography('subtitle1', BRAND.primary.main);
  doc.text('IELTS Band Score Descriptors', SPACING.margin.left, currentY);
  currentY += SPACING.md;

  // Create a table for band descriptors
  const descriptorData = [
    { band: '9', descriptor: 'Expert User', description: 'Complete operational command of the language: appropriate, accurate and fluent with complete understanding.' },
    { band: '8', descriptor: 'Very Good User', description: 'Fully operational command of the language with only occasional unsystematic inaccuracies and inappropriacies.' },
    { band: '7', descriptor: 'Good User', description: 'Operational command of the language, though with occasional inaccuracies, inappropriacies and misunderstandings.' },
    { band: '6', descriptor: 'Competent User', description: 'Generally effective command of the language despite some inaccuracies, inappropriacies and misunderstandings.' },
    { band: '5', descriptor: 'Modest User', description: 'Partial command of the language, coping with overall meaning in most situations, though likely to make many mistakes.' }
  ];

  // Format data for premium table
  const formattedDescriptorData = descriptorData.map(item => [
    item.band, item.descriptor, item.description
  ]);

  // Draw premium table
  currentY = drawTable(
    currentY,
    ['Band', 'Level', 'Description'],
    formattedDescriptorData,
    [contentWidth * 0.1, contentWidth * 0.25, contentWidth * 0.65],
    BRAND.primary.main,
    true
  );

  // Add note about half bands with elegant style
  currentY += SPACING.xs;

  drawRoundedRect(
    SPACING.margin.left,
    currentY,
    contentWidth,
    10,
    BORDER_RADIUS.md,
    [245, 245, 250],
    [220, 220, 240]
  );

  doc.setFillColor(...BRAND.primary.main);
  doc.circle(SPACING.margin.left + SPACING.md, currentY + 5, 1, 'F');

  applyTypography('caption', BRAND.neutral.text.secondary);
  doc.text(
    'Note: Half band scores (e.g., 6.5) indicate performance between the two band descriptors.',
    SPACING.margin.left + SPACING.lg,
    currentY + 6
  );

  currentY += 10 + SPACING.md;

  // ========== FINAL PDF PREPARATION ==========
  // Add footer to the last page
  addFooter();

  // Replace placeholder with actual total pages
  const totalPages = doc.getNumberOfPages();
  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);
    applyTypography('caption', BRAND.neutral.text.secondary);
    doc.text(`Page ${i} of ${totalPages}`, pageWidth / 2, pageHeight - SPACING.margin.bottom + 4, { align: 'center' });
  }

  // Save the PDF with a professional filename
  const formattedDate = new Date().toISOString().slice(0, 10);
  const fileName = `IELTS_Assessment_Report_${candidate.studentId}_${formattedDate}.pdf`;
  doc.save(fileName);
};

  // Render upload button
  const renderUploadButton = (candidate: Candidate, component: TestComponent) => {
    return (
      <Button
        variant="outlined"
        size="small"
        startIcon={<CloudUploadIcon />}
        onClick={() => handleOpenUpload(candidate, component)}
      >
        Upload
      </Button>
    );
  };

  // Render status chip
  const renderStatusChip = (component: TestComponent) => {
    switch (component.status) {
      case 'pending':
        return <Chip label="Pending" color="default" size="small" />;
      case 'uploaded':
        return <Chip label="Uploaded" color="primary" size="small" />;
      case 'processed':
        return (
          <Chip
            label={component.band ? `Band ${component.band.toFixed(1)}` : "Processed"}
            color="success"
            size="small"
            icon={<CheckCircle />}
          />
        );
      default:
        return <Chip label={component.status} size="small" />;
    }
  };

  // Render process button
  const renderProcessButton = (candidate: Candidate, component: TestComponent) => {
    if (component.status !== 'uploaded') return null;

    const isProcessing = processingComponents[`${candidate.id}-${component.id}`];

    return (
      <Button
        variant="contained"
        size="small"
        color="primary"
        onClick={() => handleProcessComponent(candidate, component)}
        disabled={isProcessing}
        startIcon={isProcessing ? <CircularProgress size={16} color="inherit" /> : null}
      >
        {isProcessing ? 'Processing...' : 'Process'}
      </Button>
    );
  };

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card sx={{ mb: 3, border: '1px solid #4caf50', boxShadow: '0 4px 8px rgba(76, 175, 80, 0.2)' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom color="success.main">
              IELTS MOCK Test Center - Updated Process
            </Typography>
            <Typography variant="body2" sx={{ mb: 2 }}>
              <strong>Important Update:</strong> You no longer need to select materials for Reading and Listening tests.
              Simply upload the correct answers at the top of this page, and then upload candidate answer sheets directly.
            </Typography>
          </CardContent>
        </Card>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h4">Candidate Assessment</Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => {
                setShowAddDialog(true);
                setAddMode('manual');
                setNewStudentIds('');
                setCandidateCount('');
              }}
            >
              Add Candidates (Bulk)
            </Button>
            <Button
              variant="contained"
              color="secondary"
              startIcon={<RefreshIcon />}
              onClick={() => {
                if (confirm('Are you sure you want to archive all candidates? This will clear the current list.')) {
                  handleArchiveCandidates();
                }
              }}
            >
              Archive All
            </Button>
          </Box>
        </Box>

        {/* Global Answers and Task Questions */}
        <Card sx={{ mb: 3, border: '1px solid #4a5af9', boxShadow: '0 4px 8px rgba(74, 90, 249, 0.2)' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom color="primary">
              IELTS MOCK Test Center - Correct Answers Upload
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Upload correct answers for the test before processing candidate answer sheets. This will be used to automatically check all candidates' answers.
            </Typography>

            {showMaterialWarning && (
              <Box sx={{ mb: 2, p: 2, bgcolor: 'warning.light', borderRadius: 1 }}>
                <Typography variant="body2" color="warning.dark">
                  <strong>Note:</strong> You no longer need to select materials. Simply upload the correct answers directly.
                </Typography>
              </Box>
            )}

            <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)} sx={{ mb: 2 }}>
              <Tab label="Reading" />
              <Tab label="Listening" />
              <Tab label="Writing" />
            </Tabs>

            {/* Reading Tab */}
            {tabValue === 0 && (
              <Box>
                <Typography variant="subtitle1" gutterBottom>Reading Correct Answers</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <CorrectAnswersFileUpload
                      title="Reading Correct Answers"
                      value={globalReadingAnswers}
                      onChange={setGlobalReadingAnswers}
                      onApplyToAll={() => handleApplyToAllCandidates('reading')}
                    />
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* Listening Tab */}
            {tabValue === 1 && (
              <Box>
                <Typography variant="subtitle1" gutterBottom>Listening Correct Answers</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <CorrectAnswersFileUpload
                      title="Listening Correct Answers"
                      value={globalListeningAnswers}
                      onChange={setGlobalListeningAnswers}
                      onApplyToAll={() => handleApplyToAllCandidates('listening')}
                    />
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* Writing Tab */}
            {tabValue === 2 && (
              <Box>
                <Typography variant="subtitle1" gutterBottom>Writing Task Questions</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Task 1 Questions"
                      multiline
                      rows={4}
                      value={globalWritingTask1Question}
                      onChange={(e) => setGlobalWritingTask1Question(e.target.value)}
                      helperText="Enter the writing task 1 questions or prompt"
                      margin="normal"
                    />
                    <Paper
                      {...getWritingTask1ImageDropzoneProps}
                      variant="outlined"
                      sx={{
                        p: 3,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        minHeight: 100,
                        cursor: 'pointer',
                        borderStyle: 'dashed',
                        borderColor: isWritingTask1ImageDragActive ? 'primary.main' : 'divider',
                        bgcolor: isWritingTask1ImageDragActive ? 'action.hover' : 'background.paper',
                        mt: 2
                      }}
                    >
                      <input {...getWritingTask1ImageInputProps} />
                      {globalWritingTask1Image ? (
                        <>
                          <CloudUploadIcon sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
                          <Typography variant="body1" align="center">
                            File selected: {globalWritingTask1Image.name}
                          </Typography>
                          <Typography variant="body2" color="textSecondary" align="center">
                            {(globalWritingTask1Image.size / 1024 / 1024).toFixed(2)} MB
                          </Typography>
                        </>
                      ) : (
                        <>
                          <CloudUploadIcon sx={{ fontSize: 32, color: 'text.secondary', mb: 1 }} />
                          <Typography variant="body1" align="center">
                            Upload task 1 image (chart, graph, etc.)
                          </Typography>
                        </>
                      )}
                    </Paper>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Task 2 Questions"
                      multiline
                      rows={4}
                      value={globalWritingTask2Question}
                      onChange={(e) => setGlobalWritingTask2Question(e.target.value)}
                      helperText="Enter the writing task 2 questions or prompt"
                      margin="normal"
                    />
                  </Grid>
                </Grid>
              </Box>
            )}

            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                color="primary"
                onClick={() => handleApplyToAllCandidates()}
                disabled={applyingToAll}
                startIcon={applyingToAll ? <CircularProgress size={16} color="inherit" /> : null}
              >
                {applyingToAll ? 'Applying...' : 'Apply to All Candidates'}
              </Button>
            </Box>
          </CardContent>
        </Card>

        {error && (
          <Paper
            sx={{
              p: 2,
              mb: 2,
              bgcolor: 'error.light',
              color: 'error.contrastText',
              display: 'flex',
              alignItems: 'center',
              gap: 2
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', p: 1 }}>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
            </Box>
            <Box>
              <Typography variant="subtitle1" fontWeight="bold">Error</Typography>
              <Typography>{error}</Typography>
            </Box>
          </Paper>
        )}

        {successMessage && (
          <Paper
            sx={{
              p: 2,
              mb: 2,
              bgcolor: 'success.light',
              color: 'success.contrastText',
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              position: 'relative',
              zIndex: 1000 // Ensure it's above other elements
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', p: 1 }}>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
            </Box>
            <Box>
              <Typography variant="subtitle1" fontWeight="bold">Success</Typography>
              <Typography>{successMessage}</Typography>
            </Box>
            <Box sx={{ ml: 'auto' }}>
              <IconButton size="small" onClick={() => setSuccessMessage(null)}>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </IconButton>
            </Box>
          </Paper>
        )}

        <Card>
          <CardContent>
            {/* Search by Candidate ID */}
            <Box sx={{ mb: 3 }}>
              <TextField
                fullWidth
                label="Search by Candidate ID"
                variant="outlined"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Enter candidate ID to search..."
                InputProps={{
                  startAdornment: (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      style={{ marginRight: '8px', color: '#666' }}
                    >
                      <circle cx="11" cy="11" r="8"></circle>
                      <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                    </svg>
                  ),
                  endAdornment: searchQuery ? (
                    <IconButton
                      size="small"
                      onClick={() => setSearchQuery('')}
                      sx={{ color: 'text.secondary' }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </IconButton>
                  ) : null
                }}
              />
            </Box>

            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : candidates.length === 0 ? (
              <Typography align="center" color="textSecondary">
                {searchQuery ? (
                  <>No candidates found matching "{searchQuery}". <Button size="small" onClick={() => setSearchQuery('')}>Clear search</Button></>
                ) : (
                  <>No candidates found. Add a candidate to get started.</>
                )}
              </Typography>
            ) : (
              <>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Candidate</TableCell>
                        <TableCell>Listening</TableCell>
                        <TableCell>Reading</TableCell>
                        <TableCell>Writing Task 1</TableCell>
                        <TableCell>Writing Task 2</TableCell>
                        <TableCell>Speaking</TableCell>
                        <TableCell>Report</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {candidates.map((candidate) => (
                      <TableRow key={candidate.id}>
                        <TableCell>
                          <Typography variant="subtitle2">{candidate.studentId}</Typography>
                          {candidate.name && (
                            <Typography variant="body2" color="textSecondary">
                              {candidate.name}
                            </Typography>
                          )}
                        </TableCell>

                        {/* Listening */}
                        <TableCell>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            {renderStatusChip(candidate.components[0])}
                            {candidate.components[0].status === 'processed' ? (
                              <Tooltip title="View Details">
                                <Button size="small" variant="text">
                                  View
                                </Button>
                              </Tooltip>
                            ) : (
                              renderUploadButton(candidate, candidate.components[0])
                            )}
                            {renderProcessButton(candidate, candidate.components[0])}
                          </Box>
                        </TableCell>

                        {/* Reading */}
                        <TableCell>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            {renderStatusChip(candidate.components[1])}
                            {candidate.components[1].status === 'processed' ? (
                              <Tooltip title="View Details">
                                <Button size="small" variant="text">
                                  View
                                </Button>
                              </Tooltip>
                            ) : (
                              renderUploadButton(candidate, candidate.components[1])
                            )}
                            {renderProcessButton(candidate, candidate.components[1])}
                          </Box>
                        </TableCell>

                        {/* Writing Task 1 */}
                        <TableCell>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            {renderStatusChip(candidate.components[2])}
                            {candidate.components[2].status === 'processed' ? (
                              <Tooltip title="View Details">
                                <Button size="small" variant="text">
                                  View
                                </Button>
                              </Tooltip>
                            ) : (
                              renderUploadButton(candidate, candidate.components[2])
                            )}
                            {renderProcessButton(candidate, candidate.components[2])}
                          </Box>
                        </TableCell>

                        {/* Writing Task 2 */}
                        <TableCell>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            {renderStatusChip(candidate.components[3])}
                            {candidate.components[3].status === 'processed' ? (
                              <Tooltip title="View Details">
                                <Button size="small" variant="text">
                                  View
                                </Button>
                              </Tooltip>
                            ) : (
                              renderUploadButton(candidate, candidate.components[3])
                            )}
                            {renderProcessButton(candidate, candidate.components[3])}
                          </Box>
                        </TableCell>

                        {/* Speaking */}
                        <TableCell>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            {renderStatusChip(candidate.components[4])}
                            {candidate.components[4].status === 'processed' ? (
                              <Tooltip title="View Details">
                                <Button size="small" variant="text">
                                  View
                                </Button>
                              </Tooltip>
                            ) : (
                              renderUploadButton(candidate, candidate.components[4])
                            )}
                            {renderProcessButton(candidate, candidate.components[4])}
                          </Box>
                        </TableCell>

                        {/* Report */}
                        <TableCell>
                          {candidate.reportGenerated ? (
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                              <Chip
                                label="Generated"
                                color="success"
                                icon={<CheckCircle />}
                              />
                              <Box sx={{ display: 'flex', gap: 1 }}>
                                {candidate.reportId && (
                                  <Button
                                    variant="text"
                                    size="small"
                                    href={`/dashboard/reports?id=${candidate.reportId}`}
                                    target="_blank"
                                  >
                                    View
                                  </Button>
                                )}
                                <Button
                                  variant="contained"
                                  size="small"
                                  color="primary"
                                  startIcon={<PdfIcon />}
                                  onClick={() => handleDownloadReport(candidate)}
                                >
                                  Download PDF
                                </Button>
                              </Box>
                            </Box>
                          ) : (
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                              <Button
                                variant="contained"
                                color="primary"
                                size="small"
                                onClick={() => handleGenerateReport(candidate)}
                                disabled={!candidate.components.every(c => c.status === 'processed') || generatingReports[candidate.id]}
                                startIcon={generatingReports[candidate.id] ? <CircularProgress size={16} color="inherit" /> : null}
                              >
                                {generatingReports[candidate.id] ? 'Generating...' : 'Generate'}
                              </Button>
                              <Box sx={{ display: 'flex', gap: 1 }}>
                                <Button
                                  variant="outlined"
                                  size="small"
                                  startIcon={<PdfIcon />}
                                  onClick={() => handleDemoPDF(candidate)}
                                >
                                  Download Preview PDF
                                </Button>
                                <Button
                                  variant="outlined"
                                  size="small"
                                  color="secondary"
                                  startIcon={<PdfIcon />}
                                  onClick={() => handleDirectDownload(candidate)}
                                >
                                  Direct Download
                                </Button>
                              </Box>
                            </Box>
                          )}
                        </TableCell>

                        {/* Actions */}
                        <TableCell>
                          <Tooltip title="Remove Candidate">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => openDeleteDialog(candidate.id)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination Controls */}
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3, mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Button
                    variant="outlined"
                    disabled={page <= 1}
                    onClick={() => setPage(page - 1)}
                  >
                    Previous
                  </Button>

                  <Typography variant="body1">
                    {searchQuery ? (
                      <span>
                        Search results for "{searchQuery}" - Page {page} of {totalPages}
                      </span>
                    ) : (
                      <span>Page {page} of {totalPages}</span>
                    )}
                  </Typography>

                  <Button
                    variant="outlined"
                    disabled={page >= totalPages}
                    onClick={() => setPage(page + 1)}
                  >
                    Next
                  </Button>
                </Box>
              </Box>
              </>
            )}
          </CardContent>
        </Card>
      </Grid>

      {/* Add Candidates Dialog */}
      <Dialog
        open={showAddDialog}
        onClose={() => setShowAddDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Add Candidates (Bulk)</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <Button
                variant={addMode === 'manual' ? 'contained' : 'outlined'}
                onClick={() => setAddMode('manual')}
              >
                Manual Entry
              </Button>
              <Button
                variant={addMode === 'count' ? 'contained' : 'outlined'}
                onClick={() => setAddMode('count')}
              >
                Generate by Count
              </Button>
            </Box>

            {addMode === 'manual' ? (
              <>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Enter student IDs separated by commas, spaces, or new lines. Each ID will be added as a separate candidate.
                </Typography>
                <TextField
                  autoFocus
                  margin="dense"
                  label="Student IDs"
                  fullWidth
                  multiline
                  rows={6}
                  value={newStudentIds}
                  onChange={(e) => setNewStudentIds(e.target.value)}
                  required
                  placeholder="e.g. S001, S002, S003
S004 S005
S006"
                  sx={{ mb: 2 }}
                />
              </>
            ) : (
              <>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Enter the number of candidates you want to add. The system will generate sequential IDs automatically.
                </Typography>
                <TextField
                  autoFocus
                  margin="dense"
                  label="Number of Candidates"
                  fullWidth
                  type="number"
                  value={candidateCount}
                  onChange={(e) => setCandidateCount(e.target.value)}
                  required
                  placeholder="e.g. 185"
                  inputProps={{ min: 1, max: 500 }}
                  sx={{ mb: 2 }}
                />
              </>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAddDialog(false)}>Cancel</Button>
          <Button
            onClick={handleAddCandidate}
            variant="contained"
            disabled={addMode === 'manual' ? !newStudentIds.trim() : !candidateCount}
          >
            Add Candidates
          </Button>
        </DialogActions>
      </Dialog>

      {/* Upload Dialog */}
      <Dialog
        open={showUploadDialog}
        onClose={() => {
          if (!uploadLoading) {
            setShowUploadDialog(false);
            // Clear any error messages
            setError(null);
            // Don't clear the state when closing the dialog
            // This allows the user to come back to the same state
          }
        }}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {currentComponent?.status === 'uploaded' ? 'Update' : 'Upload'} {currentComponent?.name} for Student {currentCandidate?.studentId}
          {currentComponent?.status === 'uploaded' && (
            <Chip
              size="small"
              color="success"
              label="Previously Uploaded"
              sx={{ ml: 1 }}
              icon={<CheckCircle fontSize="small" />}
            />
          )}
        </DialogTitle>
        <DialogContent>
          {error && (
            <Alert
              severity="error"
              sx={{ mb: 2 }}
              action={
                currentComponent?.type === 'speaking' && uploadedFile ? (
                  <Button
                    color="inherit"
                    size="small"
                    onClick={() => {
                      setError(null);
                      transcribeAudioFile(uploadedFile);
                    }}
                  >
                    Retry Transcription
                  </Button>
                ) : undefined
              }
            >
              <AlertTitle>Error</AlertTitle>
              {error}
            </Alert>
          )}
          <Box sx={{ pt: 1 }}>
            {/* Common upload area for image-based components */}
            {(currentComponent?.type === 'reading' || currentComponent?.type === 'listening') && (
              <>
                <Box sx={{ mb: 2, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
                  <Typography variant="body2" color="info.dark">
                    <strong>Note:</strong> You no longer need to select materials. The correct answers from the global settings will be used.
                  </Typography>
                </Box>
                {uploadContent.imageUrl ? (
                  <Box sx={{ position: 'relative', mb: 2 }}>
                    <Paper
                      variant="outlined"
                      sx={{
                        p: 3,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        bgcolor: 'background.paper',
                        borderRadius: 1,
                        minHeight: 200,
                        position: 'relative',
                        overflow: 'hidden'
                      }}
                    >
                      <img
                        src={uploadContent.imageUrl}
                        alt="Preview"
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'contain',
                        }}
                      />
                    </Paper>
                    <Button
                      variant="outlined"
                      color="primary"
                      startIcon={<CloudUploadIcon />}
                      onClick={() => {
                        // Clear the current image and allow re-upload
                        setUploadedFile(null);
                        setUploadContent((prev: any) => ({ ...prev, imageUrl: null }));
                      }}
                      sx={{ mt: 1 }}
                    >
                      Re-upload
                    </Button>
                  </Box>
                ) : (
                  <Paper
                    {...getRootProps()}
                    variant="outlined"
                    sx={{
                      p: 3,
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      minHeight: 200,
                      cursor: 'pointer',
                      borderStyle: 'dashed',
                      borderColor: isDragActive ? 'primary.main' : 'divider',
                      bgcolor: isDragActive ? 'action.hover' : 'background.paper',
                    }}
                  >
                    <input {...getInputProps()} />
                    {uploadLoading ? (
                      <CircularProgress />
                    ) : (
                      <>
                        <CloudUploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                        <Typography variant="body1" align="center">
                          Drag and drop answer sheet here, or click to select file
                        </Typography>
                        <Typography variant="body2" color="textSecondary" align="center" sx={{ mt: 1 }}>
                          Supported formats: PDF, JPG, PNG
                        </Typography>
                      </>
                    )}
                  </Paper>
                )}

                {/* Display simplified score input for reading/listening */}
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    {currentComponent.type === 'reading' ? 'Reading' : 'Listening'} Score
                  </Typography>

                  <ScoreInput
                    type={currentComponent.type as 'reading' | 'listening'}
                    initialValue={uploadContent.raw_score || ''}
                    onChange={(value, band) => {
                      // Use a functional update to avoid dependency on the current state
                      setUploadContent((prevContent: Record<string, any>) => ({
                        ...prevContent,
                        raw_score: value,
                        band: band
                      }));
                    }}
                    label={`${currentComponent.type === 'reading' ? 'Reading' : 'Listening'} Raw Score`}
                    helperText="Enter score as '30/40' or just the number of correct answers"
                  />

                  {/* Keep the old fields for backward compatibility but hide them */}
                  <Box sx={{ display: 'none' }}>
                    <TextField
                      margin="dense"
                      label="User Answers (comma-separated)"
                      fullWidth
                      value={uploadContent.userAnswers || ''}
                      onChange={(e) => setUploadContent((prev: Record<string, any>) => ({...prev, userAnswers: e.target.value}))}
                    />
                    <TextField
                      margin="dense"
                      label="Correct Answers (comma-separated)"
                      fullWidth
                      value={uploadContent.correctAnswers || (currentComponent?.type === 'reading' ? globalReadingAnswers : globalListeningAnswers)}
                      onChange={(e) => setUploadContent((prev: Record<string, any>) => ({...prev, correctAnswers: e.target.value}))}
                    />
                  </Box>
                </Box>
              </>
            )}

            {/* Writing specific fields */}
            {(currentComponent?.type === 'writing_task1' || currentComponent?.type === 'writing_task2') && (
              <>
                {/* Display writing task type */}
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Writing {currentComponent.type === 'writing_task1' ? 'Task 1' : 'Task 2'}
                  </Typography>
                </Box>

                <Box sx={{ mb: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
                  <Typography variant="body2" color="info.dark" sx={{ mb: 1 }}>
                    <strong>Note:</strong> Upload images of the handwritten essay. Claude 3.5 Sonnet will automatically extract the text and analyze the essay.
                  </Typography>
                  <Typography variant="body2" color="info.dark" sx={{ mb: 1 }}>
                    You can upload up to 10 images for the same essay. The extracted text will appear in the field below and can be edited if needed.
                  </Typography>
                  <Typography variant="body2" color="info.dark">
                    <strong>For multi-page essays:</strong> Upload all pages at once or add them one by one. Text from all images will be combined in order.
                  </Typography>
                </Box>

                <TextField
                  margin="dense"
                  label="Task Questions"
                  fullWidth
                  multiline
                  rows={3}
                  value={uploadContent.taskQuestions || ''}
                  onChange={(e) => setUploadContent((prev: Record<string, any>) => ({...prev, taskQuestions: e.target.value}))}
                  helperText="Enter the writing task questions or prompt"
                />

                <TextField
                  margin="dense"
                  label="Candidate Essay Text"
                  fullWidth
                  multiline
                  rows={6}
                  value={uploadContent.essayText || ''}
                  onChange={(e) => setUploadContent((prev: Record<string, any>) => ({...prev, essayText: e.target.value}))}
                  helperText="Extracted text from the handwritten essay (automatically processed with Claude 3.5 Sonnet)"
                  sx={{ mt: 2, mb: 2 }}
                  InputProps={{
                    endAdornment: uploadLoading ? (
                      <CircularProgress size={20} />
                    ) : null
                  }}
                />

                {/* Manual band score input for writing */}
                <BandScoreInput
                  type={currentComponent.type as 'writing_task1' | 'writing_task2'}
                  initialValue={uploadContent.manualBand || null}
                  onChange={(value, inflatedValue) => {
                    setUploadContent((prev: Record<string, any>) => ({
                      ...prev,
                      manualBand: value,
                      inflatedBand: inflatedValue
                    }));
                  }}
                  label={`Manual Band Score for ${currentComponent.type === 'writing_task1' ? 'Writing Task 1' : 'Writing Task 2'}`}
                  helperText="Enter band score manually (0-9) or upload essay for AI analysis"
                />

                {/* Multiple image upload display */}
                {uploadedImageUrls.length > 0 ? (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Uploaded Images ({uploadedImageUrls.length})
                      {currentComponent?.status === 'uploaded' && (
                        <Chip
                          size="small"
                          color="success"
                          label="Saved"
                          sx={{ ml: 1 }}
                          icon={<CheckCircle fontSize="small" />}
                        />
                      )}
                    </Typography>
                    <Grid container spacing={2}>
                      {uploadedImageUrls.map((url, index) => (
                        <Grid item xs={12} sm={6} md={4} key={index}>
                          <Paper
                            variant="outlined"
                            sx={{
                              p: 1,
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: 'center',
                              justifyContent: 'center',
                              bgcolor: 'background.paper',
                              borderRadius: 1,
                              position: 'relative',
                              height: 200,
                              overflow: 'hidden'
                            }}
                          >
                            <img
                              src={url}
                              alt={`Preview ${index + 1}`}
                              style={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'contain',
                              }}
                            />
                            <IconButton
                              size="small"
                              color="error"
                              sx={{ position: 'absolute', top: 5, right: 5, bgcolor: 'rgba(255,255,255,0.7)' }}
                              onClick={() => {
                                // Remove this image
                                const newUrls = [...uploadedImageUrls];
                                const newFiles = [...uploadedFiles];
                                newUrls.splice(index, 1);
                                newFiles.splice(index, 1);
                                setUploadedImageUrls(newUrls);
                                setUploadedFiles(newFiles);

                                // If we removed the last image, clear the essay text
                                if (newUrls.length === 0) {
                                  setUploadContent((prev: any) => ({ ...prev, essayText: '' }));
                                }
                              }}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Paper>
                        </Grid>
                      ))}
                    </Grid>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                      <Button
                        variant="outlined"
                        color="primary"
                        startIcon={<CloudUploadIcon />}
                        onClick={() => {
                          // Open file dialog to add more images
                          const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
                          if (fileInput) {
                            fileInput.click();
                          }
                        }}
                      >
                        Add More Images
                      </Button>
                      {uploadLoading && (
                        <Box sx={{ display: 'flex', alignItems: 'center', ml: 2 }}>
                          <CircularProgress size={20} sx={{ mr: 1 }} />
                          <Typography variant="body2" color="text.secondary">
                            Processing image with Claude 3.5...
                          </Typography>
                        </Box>
                      )}
                      <Button
                        variant="outlined"
                        color="error"
                        startIcon={<DeleteIcon />}
                        onClick={() => {
                          // Clear all images
                          setUploadedFiles([]);
                          setUploadedImageUrls([]);
                          // Clear the essay text as well
                          setUploadContent((prev: any) => ({ ...prev, essayText: '' }));
                        }}
                      >
                        Clear All Images
                      </Button>
                    </Box>
                  </Box>
                ) : (
                  <Paper
                    {...getRootProps()}
                    variant="outlined"
                    sx={{
                      p: 3,
                      mt: 2,
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      minHeight: 200,
                      cursor: 'pointer',
                      borderStyle: 'dashed',
                      borderColor: isDragActive ? 'primary.main' : 'divider',
                      bgcolor: isDragActive ? 'action.hover' : 'background.paper',
                    }}
                  >
                    <input {...getInputProps()} />
                    {uploadLoading ? (
                      <CircularProgress />
                    ) : (
                      <>
                        <CloudUploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                        <Typography variant="body1" align="center">
                          Drag and drop handwritten essay images here, or click to select files
                        </Typography>
                        <Typography variant="body2" color="textSecondary" align="center" sx={{ mt: 1 }}>
                          Supported formats: PDF, JPG, PNG
                        </Typography>
                        <Typography variant="body2" color="primary" align="center" sx={{ mt: 1 }}>
                          You can upload up to 10 images for the same essay
                        </Typography>
                        <Typography variant="body2" color="info.main" align="center" sx={{ mt: 1 }}>
                          Each image will be processed to extract text and combined into one essay
                        </Typography>
                      </>
                    )}
                  </Paper>
                )}
              </>
            )}

            {/* Speaking specific fields */}
            {currentComponent?.type === 'speaking' && (
              <>
                <Box sx={{ mb: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
                  <Typography variant="body2" color="info.dark" sx={{ mb: 1 }}>
                    <strong>Note:</strong> Upload an audio recording of the complete speaking test conversation (candidate + examiner).
                  </Typography>
                  <Typography variant="body2" color="info.dark" sx={{ mb: 1 }}>
                    <strong>Recommended format:</strong> MP3 (most reliable). Also supported: WAV, FLAC, OGG.
                  </Typography>
                  <Typography variant="body2" color="info.dark" sx={{ mb: 1 }}>
                    <strong>Important:</strong> If you have an M4A file and encounter errors, please convert it to MP3 format first.
                  </Typography>
                  <Typography variant="body2" color="info.dark" sx={{ mb: 1 }}>
                    The system will automatically transcribe the audio and extract both the examiner's questions and candidate's responses.
                  </Typography>
                  <Typography variant="body2" color="info.dark">
                    When processed, the AI will analyze the entire conversation and provide detailed feedback on all parts of the speaking test.
                  </Typography>
                </Box>

                {/* Show the file if it's already uploaded or if we have a new file */}
                {(uploadedFile || (currentComponent?.status === 'uploaded' && currentComponent?.entryId)) ? (
                  <Box sx={{ position: 'relative', mb: 2 }}>
                    <Paper
                      variant="outlined"
                      sx={{
                        p: 3,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        bgcolor: 'background.paper',
                        borderRadius: 1,
                        minHeight: 100,
                        position: 'relative',
                        overflow: 'hidden'
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <AudioFileIcon sx={{ fontSize: 32, color: 'success.main', mr: 1 }} />
                        <Box>
                          <Typography variant="body1">
                            {uploadedFile ? `File selected: ${uploadedFile.name}` : 'Audio file already uploaded'}
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            {uploadedFile ? `${(uploadedFile.size / 1024 / 1024).toFixed(2)} MB` : ''}
                            {!uploadedFile && currentComponent?.status === 'uploaded' && (
                              <Chip size="small" color="success" label="Uploaded" sx={{ ml: 1 }} />
                            )}
                          </Typography>
                        </Box>
                      </Box>
                      {transcriptionLoading && (
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <CircularProgress size={20} sx={{ mr: 1 }} />
                          <Typography variant="body2" color="primary">
                            Transcribing audio... This may take a moment.
                          </Typography>
                        </Box>
                      )}
                      {transcriptionComplete && !transcriptionLoading && (
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <CheckCircle color="success" sx={{ mr: 1, fontSize: 20 }} />
                          <Typography variant="body2" color="success.main">
                            Transcription complete! Check the transcription field below.
                          </Typography>
                        </Box>
                      )}
                    </Paper>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                      <Button
                        variant="outlined"
                        color="primary"
                        startIcon={<CloudUploadIcon />}
                        onClick={() => {
                          // Open file dialog to re-upload
                          const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
                          if (fileInput) {
                            fileInput.click();
                          }
                        }}
                      >
                        Change Audio File
                      </Button>
                      <Button
                        variant="outlined"
                        color="error"
                        startIcon={<DeleteIcon />}
                        onClick={() => {
                          // Clear the current file and allow re-upload
                          setUploadedFile(null);
                          setUploadContent((prev: any) => ({
                            ...prev,
                            imageUrl: null,
                            // Don't clear transcription or examiner content
                          }));
                        }}
                      >
                        Clear File
                      </Button>
                    </Box>
                  </Box>
                ) : (
                  <Paper
                    {...getRootProps()}
                    variant="outlined"
                    sx={{
                      p: 3,
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      minHeight: 100,
                      cursor: 'pointer',
                      borderStyle: 'dashed',
                      borderColor: isDragActive ? 'primary.main' : 'divider',
                      bgcolor: isDragActive ? 'action.hover' : 'background.paper',
                      mb: 2
                    }}
                  >
                    <input {...getInputProps()} />
                    {transcriptionLoading ? (
                      <>
                        <CircularProgress sx={{ mb: 2 }} />
                        <Typography variant="body1" align="center">
                          Transcribing audio...
                        </Typography>
                        <Typography variant="body2" color="info.main" align="center" sx={{ mt: 1 }}>
                          This may take a moment. The transcription will appear below when ready.
                        </Typography>
                      </>
                    ) : (
                      <>
                        <CloudUploadIcon sx={{ fontSize: 32, color: 'text.secondary', mb: 1 }} />
                        <Typography variant="body1" align="center">
                          Upload audio recording
                        </Typography>
                        <Typography variant="body2" color="textSecondary" align="center" sx={{ mt: 1 }}>
                          Recommended format: MP3 (most reliable)
                        </Typography>
                        <Typography variant="body2" color="textSecondary" align="center">
                          Also supported: WAV, FLAC, OGG
                        </Typography>
                        <Typography variant="body2" color="error.main" align="center" sx={{ mt: 1 }}>
                          If using M4A files, convert to MP3 if you encounter errors
                        </Typography>
                      </>
                    )}
                  </Paper>
                )}

                <TextField
                  margin="dense"
                  label={<>
                    Transcription
                    {uploadContent.transcription?.includes('SIMULATED TRANSCRIPT') && (
                      <Chip
                        label="Simulated"
                        color="warning"
                        size="small"
                        sx={{ ml: 1 }}
                        title="This is a simulated transcript, not the actual audio content"
                      />
                    )}
                  </>}
                  fullWidth
                  multiline
                  rows={4}
                  value={uploadContent.transcription || ''}
                  onChange={(e) => setUploadContent((prev: Record<string, any>) => ({...prev, transcription: e.target.value}))}
                  helperText={
                    transcriptionLoading ? "Transcription in progress..." :
                    uploadContent.transcription?.includes('SIMULATED TRANSCRIPT') ?
                      "Warning: This is a simulated transcript, not the actual audio content. The AI couldn't transcribe the audio properly. You may want to try uploading again or manually enter the transcript." :
                    transcriptionComplete ? "Transcription complete! You can edit if needed." :
                    "Will be automatically extracted from the audio file. You can also enter it manually if needed."
                  }
                  InputProps={{
                    endAdornment: transcriptionLoading ? (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <CircularProgress size={20} sx={{ mr: 1 }} />
                        <Typography variant="caption" color="primary">Extracting...</Typography>
                      </Box>
                    ) : transcriptionComplete ? (
                      uploadContent.transcription?.includes('SIMULATED TRANSCRIPT') ? (
                        <Tooltip title="Simulated transcript - not actual audio content">
                          <span><CheckCircle color="warning" sx={{ fontSize: 20 }} /></span>
                        </Tooltip>
                      ) : (
                        <CheckCircle color="success" sx={{ fontSize: 20 }} />
                      )
                    ) : null,
                    readOnly: transcriptionLoading
                  }}
                  error={uploadContent.transcription?.includes('SIMULATED TRANSCRIPT')}
                />

                <TextField
                  margin="dense"
                  label="Examiner Content"
                  fullWidth
                  multiline
                  rows={2}
                  sx={{ mt: 2 }}
                  value={uploadContent.examinerContent || ''}
                  onChange={(e) => setUploadContent((prev: Record<string, any>) => ({...prev, examinerContent: e.target.value}))}
                  helperText={
                    transcriptionLoading ? "Extraction in progress..." :
                    transcriptionComplete ? "Extraction complete! You can edit if needed." :
                    "Will be automatically extracted from the audio. You can provide additional context if needed."
                  }
                  InputProps={{
                    endAdornment: transcriptionLoading ? (
                      <CircularProgress size={20} />
                    ) : transcriptionComplete ? (
                      <CheckCircle color="success" sx={{ fontSize: 20 }} />
                    ) : null,
                    readOnly: transcriptionLoading
                  }}
                />

                {/* Manual band score input for speaking */}
                <BandScoreInput
                  type="speaking"
                  initialValue={uploadContent.manualBand || null}
                  onChange={(value, inflatedValue) => {
                    setUploadContent((prev: Record<string, any>) => ({
                      ...prev,
                      manualBand: value,
                      inflatedBand: inflatedValue
                    }));
                  }}
                  label="Manual Band Score for Speaking"
                  helperText="Enter band score manually (0-9) or upload audio for AI analysis"
                />
              </>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              if (!uploadLoading) {
                setShowUploadDialog(false);
                // Clear any error messages
                setError(null);
                // Don't clear the state when closing the dialog
              }
            }}
            disabled={uploadLoading || transcriptionLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={() => handleUploadComplete()}
            variant="contained"
            disabled={uploadLoading || transcriptionLoading ||
              (currentComponent?.type === 'speaking' &&
               !uploadedFile &&
               currentComponent?.status !== 'uploaded' &&
               (uploadContent.manualBand === undefined || uploadContent.manualBand === null))}
            startIcon={uploadLoading ? <CircularProgress size={16} color="inherit" /> : null}
          >
            {uploadLoading ? 'Processing...' : (currentComponent?.status === 'uploaded' ? 'Update' : 'Upload')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={showDeleteDialog}
        onClose={() => !deletingCandidate && setShowDeleteDialog(false)}
      >
        <DialogTitle>Delete Candidate</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this candidate? This action cannot be undone.
          </Typography>
          <Typography variant="body2" color="error" sx={{ mt: 2 }}>
            All associated data (writing, reading, listening, speaking entries and reports) will be permanently deleted.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setShowDeleteDialog(false)}
            disabled={deletingCandidate}
          >
            Cancel
          </Button>
          <Button
            onClick={() => candidateToDelete && handleRemoveCandidate(candidateToDelete)}
            color="error"
            variant="contained"
            disabled={deletingCandidate}
            startIcon={deletingCandidate ? <CircularProgress size={16} color="inherit" /> : null}
          >
            {deletingCandidate ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Grid>
  );
}


Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.6.0
 * Query Engine version: f676762280b54cd07c770017ed3711ddde35f37a
 */
Prisma.prismaVersion = {
  client: "6.6.0",
  engine: "f676762280b54cd07c770017ed3711ddde35f37a"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.StudentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WritingMaterialScalarFieldEnum = {
  id: 'id',
  title: 'title',
  taskType: 'taskType',
  taskQuestion: 'taskQuestion',
  taskImage: 'taskImage',
  sampleAnswer: 'sampleAnswer',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WritingEntryScalarFieldEnum = {
  id: 'id',
  studentId: 'studentId',
  materialId: 'materialId',
  taskType: 'taskType',
  essayText: 'essayText',
  taskQuestion: 'taskQuestion',
  imageUrl: 'imageUrl',
  extractedWithClaude: 'extractedWithClaude',
  band: 'band',
  criteriaScores: 'criteriaScores',
  feedback: 'feedback',
  strengths: 'strengths',
  weaknesses: 'weaknesses',
  improvementSuggestions: 'improvementSuggestions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SpeakingEntryScalarFieldEnum = {
  id: 'id',
  studentId: 'studentId',
  audioUrl: 'audioUrl',
  transcription: 'transcription',
  partNumber: 'partNumber',
  examinerContent: 'examinerContent',
  isSimulated: 'isSimulated',
  band: 'band',
  criteriaScores: 'criteriaScores',
  feedback: 'feedback',
  strengths: 'strengths',
  weaknesses: 'weaknesses',
  improvementSuggestions: 'improvementSuggestions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReadingMaterialScalarFieldEnum = {
  id: 'id',
  title: 'title',
  passage: 'passage',
  questions: 'questions',
  answers: 'answers',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReadingEntryScalarFieldEnum = {
  id: 'id',
  studentId: 'studentId',
  materialId: 'materialId',
  answers: 'answers',
  imageUrl: 'imageUrl',
  extractedText: 'extractedText',
  extractedWithClaude: 'extractedWithClaude',
  score: 'score',
  band: 'band',
  correctAnswers: 'correctAnswers',
  totalQuestions: 'totalQuestions',
  mistakes: 'mistakes',
  strengths: 'strengths',
  weaknesses: 'weaknesses',
  improvementSuggestions: 'improvementSuggestions',
  metadata: 'metadata',
  raw_score: 'raw_score',
  materialTitle: 'materialTitle',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ListeningMaterialScalarFieldEnum = {
  id: 'id',
  title: 'title',
  audioUrl: 'audioUrl',
  transcript: 'transcript',
  section: 'section',
  questions: 'questions',
  answers: 'answers',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ListeningEntryScalarFieldEnum = {
  id: 'id',
  studentId: 'studentId',
  materialId: 'materialId',
  answers: 'answers',
  imageUrl: 'imageUrl',
  extractedText: 'extractedText',
  extractedWithClaude: 'extractedWithClaude',
  score: 'score',
  band: 'band',
  correctAnswers: 'correctAnswers',
  totalQuestions: 'totalQuestions',
  mistakes: 'mistakes',
  strengths: 'strengths',
  weaknesses: 'weaknesses',
  improvementSuggestions: 'improvementSuggestions',
  criteriaScores: 'criteriaScores',
  metadata: 'metadata',
  raw_score: 'raw_score',
  materialTitle: 'materialTitle',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReportScalarFieldEnum = {
  id: 'id',
  studentId: 'studentId',
  candidateName: 'candidateName',
  testDate: 'testDate',
  readingResult: 'readingResult',
  listeningResult: 'listeningResult',
  writingResult: 'writingResult',
  speakingResult: 'speakingResult',
  overallBand: 'overallBand',
  overallStrengths: 'overallStrengths',
  overallWeaknesses: 'overallWeaknesses',
  overallImprovements: 'overallImprovements',
  printStatus: 'printStatus',
  folderAssignment: 'folderAssignment',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};


exports.Prisma.ModelName = {
  Student: 'Student',
  WritingMaterial: 'WritingMaterial',
  WritingEntry: 'WritingEntry',
  SpeakingEntry: 'SpeakingEntry',
  ReadingMaterial: 'ReadingMaterial',
  ReadingEntry: 'ReadingEntry',
  ListeningMaterial: 'ListeningMaterial',
  ListeningEntry: 'ListeningEntry',
  Report: 'Report'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)

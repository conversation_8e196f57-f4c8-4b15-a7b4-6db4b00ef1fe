// Test script for database functionality
const { PrismaClient } = require('../src/generated/prisma');
const prisma = new PrismaClient();

async function main() {
  console.log('Starting database test...');

  try {
    // Test connection
    console.log('Testing database connection...');
    await prisma.$connect();
    console.log('✅ Database connection successful');

    // Test student creation
    console.log('\nTesting student creation...');
    const testStudentId = `test-student-${Date.now()}`;
    const student = await prisma.student.create({
      data: {
        id: testStudentId,
        name: 'Test Student',
        email: `test-${Date.now()}@example.com`,
      },
    });
    console.log(`✅ Student created with ID: ${student.id}`);

    // Test writing material creation
    console.log('\nTesting writing material creation...');
    const writingMaterial = await prisma.writingMaterial.create({
      data: {
        title: 'Test Writing Task',
        taskType: 'task2',
        taskQuestion: 'Some people believe that technology has made life more complicated. To what extent do you agree or disagree?',
        sampleAnswer: 'This is a sample answer for the test writing task.',
      },
    });
    console.log(`✅ Writing material created with ID: ${writingMaterial.id}`);

    // Test writing entry creation
    console.log('\nTesting writing entry creation...');
    const writingEntry = await prisma.writingEntry.create({
      data: {
        studentId: student.id,
        materialId: writingMaterial.id,
        taskType: 'task2',
        essayText: 'This is a test essay for the database test.',
        band: 7.0,
        criteriaScores: {
          taskAchievement: 7.0,
          coherenceCohesion: 7.0,
          lexicalResource: 7.0,
          grammaticalRangeAccuracy: 7.0,
        },
        feedback: 'This is test feedback.',
        strengths: ['Good vocabulary', 'Clear structure'],
        weaknesses: ['Some grammar errors'],
        improvementSuggestions: ['Practice more complex sentences'],
      },
    });
    console.log(`✅ Writing entry created with ID: ${writingEntry.id}`);

    // Test speaking entry creation
    console.log('\nTesting speaking entry creation...');
    const speakingEntry = await prisma.speakingEntry.create({
      data: {
        studentId: student.id,
        audioUrl: 'https://example.com/test-audio.m4a',
        transcription: 'This is a test transcription for the database test.',
        partNumber: 2,
        examinerContent: 'Tell me about your hometown.',
        band: 7.0,
        criteriaScores: {
          fluencyCoherence: 7.0,
          lexicalResource: 7.0,
          grammaticalRangeAccuracy: 7.0,
          pronunciation: 7.0,
        },
        feedback: 'This is test feedback for speaking.',
        strengths: ['Good fluency', 'Clear pronunciation'],
        weaknesses: ['Limited vocabulary'],
        improvementSuggestions: ['Practice more complex vocabulary'],
      },
    });
    console.log(`✅ Speaking entry created with ID: ${speakingEntry.id}`);

    // Test data retrieval
    console.log('\nTesting data retrieval...');
    const retrievedStudent = await prisma.student.findUnique({
      where: { id: student.id },
      include: {
        writingEntries: true,
        speakingEntries: true,
      },
    });
    console.log(`✅ Retrieved student with ID: ${retrievedStudent.id}`);
    console.log(`✅ Student has ${retrievedStudent.writingEntries.length} writing entries`);
    console.log(`✅ Student has ${retrievedStudent.speakingEntries.length} speaking entries`);

    // Test data cleanup
    console.log('\nCleaning up test data...');
    await prisma.writingEntry.delete({ where: { id: writingEntry.id } });
    await prisma.speakingEntry.delete({ where: { id: speakingEntry.id } });
    await prisma.writingMaterial.delete({ where: { id: writingMaterial.id } });
    await prisma.student.delete({ where: { id: student.id } });
    console.log('✅ Test data cleaned up successfully');

    console.log('\n🎉 All database tests passed successfully!');
  } catch (error) {
    console.error('❌ Database test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });

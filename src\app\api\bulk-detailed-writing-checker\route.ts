import { NextRequest, NextResponse } from 'next/server';
import { inflateBandScore } from '@/utils/band-score-inflation';

// Get API key from environment variables
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

if (!OPENAI_API_KEY) {
  throw new Error('OPENAI_API_KEY environment variable is not set');
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { essays, model } = body;

    if (!essays || !Array.isArray(essays) || essays.length === 0) {
      return NextResponse.json({ error: 'Essays array is required' }, { status: 400 });
    }

    if (!model) {
      return NextResponse.json({ error: 'Model is required' }, { status: 400 });
    }

    // Validate each essay has text and taskQuestion
    for (let i = 0; i < essays.length; i++) {
      if (!essays[i].text || essays[i].text.trim() === '') {
        return NextResponse.json({ error: `Essay ${i + 1} text is required` }, { status: 400 });
      }
      if (!essays[i].taskQuestion || essays[i].taskQuestion.trim() === '') {
        return NextResponse.json({ error: `Task question for essay ${i + 1} is required` }, { status: 400 });
      }
    }

    // Process essays in batches to avoid overwhelming the API
    const batchSize = 5;
    const results = [];

    for (let i = 0; i < essays.length; i += batchSize) {
      const batch = essays.slice(i, i + batchSize);

      // Process batch in parallel
      const batchPromises = batch.map(async (essay) => {
        try {
          const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${OPENAI_API_KEY}`,
              'OpenAI-Beta': 'assistants=v1'
            },
            body: JSON.stringify({
              model,
              messages: [
                {
                  role: 'system',
                  content: `You are an expert IELTS writing examiner and grammar specialist. Evaluate the following text according to the official IELTS scoring criteria and provide comprehensive, detailed feedback in a structured JSON format with the following fields:

Task Question: ${essay.taskQuestion}

Essay Type: ${essay.text.length > 500 ? "Task 2" : "Task 1"}

{
  "overallScore": number,
  "criteria": [
    {
      "name": "Task Achievement/Response",
      "score": number,
      "feedback": "detailed explanation",
      "strengths": ["strength1", "strength2"],
      "weaknesses": ["weakness1", "weakness2"],
      "improvements": ["improvement1", "improvement2"],
      "taskFulfillment": "detailed assessment of how well the essay addresses the specific task question"
    },
    // repeat for other 3 criteria: Coherence and Cohesion, Lexical Resource, Grammatical Range and Accuracy
  ],
  "vocabularyAnalysis": {
    "overusedWords": [{"word": "basic word", "count": number, "alternatives": ["better alternative1", "better alternative2", "better alternative3"]}],
    "collocations": [{"incorrect": "incorrect collocation", "correct": "correct collocation", "context": "usage example"}],
    "academicPhrases": ["phrase1", "phrase2"],
    "lexicalDiversity": {
      "uniqueWords": number,
      "totalWords": number,
      "diversityScore": number
    },
    "vocabularyLevel": {
      "basic": ["word1", "word2"],
      "intermediate": ["word1", "word2"],
      "advanced": ["word1", "word2"],
      "distribution": {
        "basic": "percentage",
        "intermediate": "percentage",
        "advanced": "percentage"
      }
    },
    "topicRelevantVocabulary": ["word1", "word2", "word3"],
    "transitionWords": {
      "used": ["word1", "word2", "word3"],
      "suggestions": ["additional1", "additional2"]
    },
    "idiomsAndPhrases": {
      "used": ["phrase1", "phrase2"],
      "suggestions": ["suggestion1", "suggestion2"]
    }
  },
  "sentenceAnalysis": {
    "improvements": [
      {
        "original": "original problematic sentence",
        "improved": "improved version",
        "explanation": "why this is better"
      },
      {
        "original": "another problematic sentence",
        "improved": "improved version",
        "explanation": "why this is better"
      },
      {
        "original": "third problematic sentence",
        "improved": "improved version",
        "explanation": "why this is better"
      }
    ],
    "complexSentences": number,
    "simpleSentences": number,
    "averageSentenceLength": number,
    "sentenceLengthVariety": number,
    "sentenceBeginnings": {
      "variety": number,
      "repetitivePatterns": ["pattern1", "pattern2"]
    }
  },
  "paragraphAnalysis": {
    "introduction": {
      "content": "Introduction paragraph text...",
      "purpose": "Introduces the topic and states the thesis",
      "coherence": number,
      "topicSentence": {
        "text": "The topic sentence text",
        "strength": number,
        "improvement": "optional improvement suggestion"
      },
      "development": {
        "quality": number,
        "missingElements": ["element1", "element2"],
        "improvements": ["improvement1", "improvement2"]
      },
      "transitionQuality": number
    },
    "bodyParagraphs": [
      {
        "content": "Body paragraph 1 text...",
        "purpose": "Presents the first main argument",
        "coherence": number,
        "topicSentence": {
          "text": "The topic sentence text",
          "strength": number,
          "improvement": "optional improvement suggestion"
        },
        "development": {
          "quality": number,
          "missingElements": ["element1", "element2"],
          "improvements": ["improvement1", "improvement2"]
        },
        "transitionQuality": number
      },
      // Additional body paragraphs follow this same structure
    ],
    "conclusion": {
      "content": "Conclusion paragraph text...",
      "purpose": "Summarizes main points and restates thesis",
      "coherence": number,
      "topicSentence": {
        "text": "The topic sentence text",
        "strength": number,
        "improvement": "optional improvement suggestion"
      },
      "development": {
        "quality": number,
        "missingElements": ["element1", "element2"],
        "improvements": ["improvement1", "improvement2"]
      },
      "transitionQuality": number
    },
    "overallCohesion": number
  },
  "grammarAnalysis": {
    "errorsByType": {
      "tenseErrors": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation", "severity": "minor or major"}],
      "subjectVerbAgreement": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation", "severity": "minor or major"}],
      "articles": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation", "severity": "minor or major"}],
      "prepositions": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation", "severity": "minor or major"}],
      "sentenceStructure": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation", "severity": "minor or major"}],
      "fragments": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation", "severity": "minor or major"}],
      "punctuation": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation", "severity": "minor or major"}],
      "wordOrder": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation", "severity": "minor or major"}]
    },
    "grammarStrengths": ["complex structure 1 used correctly", "complex structure 2 used correctly"],
    "totalErrors": number,
    "errorFrequency": "errors per 100 words",
    "mostFrequentErrorType": "the category with most errors",
    "errorDistribution": {
      "tenseErrors": percentage,
      "subjectVerbAgreement": percentage,
      "articles": percentage,
      "prepositions": percentage
    }
  },
  "comparativeAnalysis": {
    "structuralDifferences": ["difference1", "difference2"],
    "argumentationImprovements": ["improvement1", "improvement2"],
    "bandExample": "short example of what makes this a band X essay vs band Y"
  },
  "detailedImprovementPlan": {
    "priorityAreas": ["area1", "area2", "area3"],
    "specificExercises": ["exercise1", "exercise2", "exercise3"],
    "resourceSuggestions": ["resource1", "resource2", "resource3"]
  }
}

The scores should range from 0-9, where 9 is the highest score. Be thorough and detailed in your feedback. Pay special attention to grammar errors, sentence structure, paragraph analysis, and provide specific improvements.`
                },
                {
                  role: 'user',
                  content: essay.text
                }
              ],
              temperature: 0.7,
              max_tokens: 4000,
              response_format: { type: "json_object" }
            })
          });

          if (!response.ok) {
            const errorData = await response.json();
            return {
              id: essay.id,
              error: errorData.error?.message || 'Failed to check writing',
              status: 'error'
            };
          }

          const data = await response.json();
          const content = data.choices[0].message.content;

          try {
            const parsedFeedback = JSON.parse(content);

            return {
              id: essay.id,
              feedback: parsedFeedback,
              status: 'success'
            };
          } catch (e) {
            return {
              id: essay.id,
              error: 'Failed to parse feedback',
              status: 'error'
            };
          }
        } catch (error) {
          return {
            id: essay.id,
            error: error instanceof Error ? error.message : 'An unexpected error occurred',
            status: 'error'
          };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return NextResponse.json(results);
  } catch (error) {
    console.error('Error in bulk detailed writing checker API:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}
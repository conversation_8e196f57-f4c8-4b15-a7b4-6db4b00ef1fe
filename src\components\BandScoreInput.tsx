'use client';

import React, { useState, useEffect } from 'react';
import { Box, TextField, Typography, Slider, Paper } from '@mui/material';
import { inflateBandScore } from '@/utils/band-score-inflation';

interface BandScoreInputProps {
  type: 'writing_task1' | 'writing_task2' | 'speaking';
  initialValue?: number | null;
  onChange: (value: number, inflatedValue: number) => void;
  label?: string;
  helperText?: string;
}

const BandScoreInput: React.FC<BandScoreInputProps> = ({
  type,
  initialValue = null,
  onChange,
  label = 'Band Score',
  helperText = 'Enter band score between 0 and 9'
}) => {
  const [bandScore, setBandScore] = useState<number | null>(initialValue);
  const [inflatedBandScore, setInflatedBandScore] = useState<number | null>(null);
  const isInitialMount = React.useRef(true);
  const prevBandScore = React.useRef<number | null>(null);

  // Calculate inflated band score when band score changes
  useEffect(() => {
    if (bandScore !== null) {
      const inflated = inflateBandScore(bandScore, type) as number;
      setInflatedBandScore(inflated);
    } else {
      setInflatedBandScore(null);
    }
  }, [bandScore, type]);

  // Separate effect to call onChange only when bandScore changes
  useEffect(() => {
    // Skip the first render
    if (isInitialMount.current) {
      isInitialMount.current = false;
      prevBandScore.current = bandScore;
      return;
    }

    // Only call onChange if the band score actually changed
    if (bandScore !== null && inflatedBandScore !== null && bandScore !== prevBandScore.current) {
      prevBandScore.current = bandScore;
      onChange(bandScore, inflatedBandScore);
    }
  }, [bandScore, inflatedBandScore, onChange]);

  const handleScoreChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === '') {
      setBandScore(null);
      return;
    }
    
    const numValue = parseFloat(value);
    if (!isNaN(numValue) && numValue >= 0 && numValue <= 9) {
      setBandScore(numValue);
    }
  };

  const handleSliderChange = (_event: Event, newValue: number | number[]) => {
    const value = newValue as number;
    setBandScore(value);
  };

  return (
    <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
      <Typography variant="subtitle1" gutterBottom>
        {label}
      </Typography>
      
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <TextField
          value={bandScore === null ? '' : bandScore}
          onChange={handleScoreChange}
          variant="outlined"
          size="small"
          type="number"
          inputProps={{ 
            min: 0, 
            max: 9, 
            step: 0.5,
            style: { textAlign: 'center' }
          }}
          sx={{ width: '80px', mr: 2 }}
        />
        <Slider
          value={bandScore === null ? 0 : bandScore}
          onChange={handleSliderChange}
          min={0}
          max={9}
          step={0.5}
          marks
          valueLabelDisplay="auto"
          sx={{ flexGrow: 1 }}
        />
      </Box>
      
      <Typography variant="body2" color="textSecondary">
        {helperText}
      </Typography>
      
      {inflatedBandScore !== null && inflatedBandScore !== bandScore && (
        <Box sx={{ mt: 1, p: 1, bgcolor: 'info.light', borderRadius: 1 }}>
          <Typography variant="body2">
            <strong>Inflated Band Score:</strong> {inflatedBandScore.toFixed(1)}
          </Typography>
          <Typography variant="caption" color="textSecondary">
            {type === 'speaking' ? 'Speaking' : type === 'writing_task1' ? 'Writing Task 1' : 'Writing Task 2'} scores are automatically inflated according to IELTS MOCK policy.
          </Typography>
        </Box>
      )}
    </Paper>
  );
};

export default BandScoreInput;

import { prisma } from '@/lib/db';
import { convertReadingRawScoreToBand, generateStandardizedFeedback } from './score-conversion';

/**
 * Get all reading entries for a student
 *
 * @param studentId - The student ID
 * @returns Array of reading entries
 */
export async function getStudentReadingEntries(studentId: string) {
  return prisma.readingEntry.findMany({
    where: { studentId },
    orderBy: { createdAt: 'desc' }
  });
}

/**
 * Get a reading entry by ID
 *
 * @param id - The reading entry ID
 * @returns The reading entry or null if not found
 */
export async function getReadingEntryById(id: string) {
  return prisma.readingEntry.findUnique({
    where: { id }
  });
}

/**
 * Create a new reading entry
 *
 * @param data - The reading entry data
 * @returns The created reading entry
 */
export async function createReadingEntry(data: {
  studentId: string;
  materialId: string;
  answers: any;
  imageUrl?: string;
  score?: number;
  band?: number;
  correctAnswers?: number;
  totalQuestions?: number;
  mistakes?: any;
  strengths?: any;
  weaknesses?: any;
  improvementSuggestions?: any;
  raw_score?: string;
}) {
  return prisma.readingEntry.create({
    data
  });
}

/**
 * Create a new reading entry with simplified approach using raw score
 *
 * @param data - The reading entry data with raw score
 * @returns The created reading entry
 */
export async function createReadingEntryWithRawScore(data: {
  studentId: string;
  materialId?: string;
  materialTitle?: string;
  raw_score: string;
  imageUrl?: string;
}) {
  // Calculate band score from raw score
  const band = convertReadingRawScoreToBand(data.raw_score);

  // Generate standardized feedback
  const feedback = generateStandardizedFeedback(band, 'reading');

  // Extract score values
  let score = 0;
  let totalQuestions = 40;

  if (data.raw_score.includes('/')) {
    const [correct, total] = data.raw_score.split('/').map(s => parseInt(s.trim(), 10));
    score = correct;
    totalQuestions = total || 40;
  } else {
    score = parseInt(data.raw_score.trim(), 10);
  }

  // Create entry with calculated values
  return prisma.readingEntry.create({
    data: {
      studentId: data.studentId,
      materialId: data.materialId,
      materialTitle: data.materialTitle || 'Global Reading Test',
      imageUrl: data.imageUrl,
      raw_score: data.raw_score,
      score,
      band,
      correctAnswers: score,
      totalQuestions,
      answers: [], // Empty array as we're not using answer checking
      strengths: feedback.strengths,
      weaknesses: feedback.weaknesses,
      improvementSuggestions: feedback.improvements
    }
  });
}

/**
 * Update a reading entry
 *
 * @param id - The reading entry ID
 * @param data - The updated reading entry data
 * @returns The updated reading entry
 */
export async function updateReadingEntry(
  id: string,
  data: {
    answers?: any;
    imageUrl?: string;
    score?: number;
    band?: number;
    correctAnswers?: number;
    totalQuestions?: number;
    mistakes?: any;
    strengths?: any;
    weaknesses?: any;
    improvementSuggestions?: any;
    raw_score?: string;
  }
) {
  return prisma.readingEntry.update({
    where: { id },
    data
  });
}

/**
 * Update a reading entry with raw score
 *
 * @param id - The reading entry ID
 * @param raw_score - The raw score in format "30/40"
 * @returns The updated reading entry
 */
export async function updateReadingEntryWithRawScore(
  id: string,
  raw_score: string
) {
  // Calculate band score from raw score
  const band = convertReadingRawScoreToBand(raw_score);

  // Generate standardized feedback
  const feedback = generateStandardizedFeedback(band, 'reading');

  // Extract score values
  let score = 0;
  let totalQuestions = 40;

  if (raw_score.includes('/')) {
    const [correct, total] = raw_score.split('/').map(s => parseInt(s.trim(), 10));
    score = correct;
    totalQuestions = total || 40;
  } else {
    score = parseInt(raw_score.trim(), 10);
  }

  // Update entry with calculated values
  return prisma.readingEntry.update({
    where: { id },
    data: {
      raw_score,
      score,
      band,
      correctAnswers: score,
      totalQuestions,
      strengths: feedback.strengths,
      weaknesses: feedback.weaknesses,
      improvementSuggestions: feedback.improvements
    }
  });
}

/**
 * Delete a reading entry
 *
 * @param id - The reading entry ID
 * @returns The deleted reading entry
 */
export async function deleteReadingEntry(id: string) {
  return prisma.readingEntry.delete({
    where: { id },
  });
}

/**
 * Calculate band score based on raw score and total questions
 *
 * @param score - The raw score (number of correct answers)
 * @param totalQuestions - The total number of questions
 * @returns The band score (0-9)
 * @deprecated Use convertReadingRawScoreToBand from score-conversion.ts instead
 */
export function calculateBandScore(score: number, totalQuestions: number): number {
  const percentage = (score / totalQuestions) * 100;

  // IELTS Reading band score conversion (approximate)
  if (percentage >= 90) return 9.0;
  if (percentage >= 85) return 8.5;
  if (percentage >= 80) return 8.0;
  if (percentage >= 75) return 7.5;
  if (percentage >= 70) return 7.0;
  if (percentage >= 65) return 6.5;
  if (percentage >= 60) return 6.0;
  if (percentage >= 55) return 5.5;
  if (percentage >= 50) return 5.0;
  if (percentage >= 45) return 4.5;
  if (percentage >= 40) return 4.0;
  if (percentage >= 35) return 3.5;
  if (percentage >= 30) return 3.0;
  if (percentage >= 25) return 2.5;
  if (percentage >= 20) return 2.0;
  if (percentage >= 15) return 1.5;
  if (percentage >= 10) return 1.0;
  if (percentage >= 5) return 0.5;
  return 0.0;
}

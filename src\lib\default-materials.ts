import { prisma } from '@/lib/db';

/**
 * Get or create a default listening material for use when no specific material is selected
 * This is used to satisfy the foreign key constraint in the ListeningEntry model
 * 
 * @returns The default listening material
 */
export async function getOrCreateDefaultListeningMaterial() {
  // Check if default material already exists
  const defaultTitle = 'Default Listening Material';
  let defaultMaterial = await prisma.listeningMaterial.findFirst({
    where: { title: defaultTitle }
  });

  // If it doesn't exist, create it
  if (!defaultMaterial) {
    defaultMaterial = await prisma.listeningMaterial.create({
      data: {
        title: defaultTitle,
        audioUrl: 'https://example.com/default-audio.mp3', // Dummy URL
        questions: [], // Empty array
        answers: {}, // Empty object
        section: 1, // Default section
        transcript: 'Default transcript' // Default transcript
      }
    });
    console.log('Created default listening material:', defaultMaterial.id);
  }

  return defaultMaterial;
}

/**
 * Get or create a default reading material for use when no specific material is selected
 * This is used to satisfy the foreign key constraint in the ReadingEntry model
 * 
 * @returns The default reading material
 */
export async function getOrCreateDefaultReadingMaterial() {
  // Check if default material already exists
  const defaultTitle = 'Default Reading Material';
  let defaultMaterial = await prisma.readingMaterial.findFirst({
    where: { title: defaultTitle }
  });

  // If it doesn't exist, create it
  if (!defaultMaterial) {
    defaultMaterial = await prisma.readingMaterial.create({
      data: {
        title: defaultTitle,
        passage: 'Default passage', // Dummy passage
        questions: [], // Empty array
        answers: {} // Empty object
      }
    });
    console.log('Created default reading material:', defaultMaterial.id);
  }

  return defaultMaterial;
}

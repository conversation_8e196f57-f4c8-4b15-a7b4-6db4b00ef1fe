# Changelog

## v4.0.0

###### Mar 8, 2024

- Add `TypeScript`
- Refactor code
- Replace `date-fns` with `dayjs`
- Replace `Formik` with `React Hook Form`
- Replace `Hero Icons` with `Phosphor Icons`
- Replace `pages` router with `app router`
- Replace `Yup` with `Zod`
- Update `eslint` rules
- Update auth implementation
- Update dependencies
- Update design system

## v3.0.0

###### Feb 24, 2023

- Refactor components
- Replace authentication
- Update dependencies
- Update design system

## v2.1.0

###### Sep 15, 2022

- Integrate Zalter Authentication
- Update dependencies

## v2.0.0

###### Nov 8, 2021

- Migrate to Next.js
- Update design system

# Change Log

## v1.0.0

###### Aug 7, 2020

- Add `eslint`
- Add `Feather Icons`
- Add `Formik` for login/register pages
- Implement `react-router` v6 routing method
- Remove `node-sass` dependency
- Remove extra views
- Update all components to match the PRO version style
- Update dependencies
- Update folder structure to remove folder depth
- Update theme configuration

## v0.4.0

###### Jul 24, 2019

- Adjust theme colors
- Implement `useStyle` hook instead of `withStyles` HOC
- Implement a custom Route component to wrap views in layouts
- Remove `services` and `data` folders, each component has its own data
- Remove unused `.scss` files from `assets` folder
- Replace `.jsx` with `.js`
- Replace Class Components with Function Components
- Replace custom components (Portlet) with Material-UI built-in components
- Replace dependency `classnames` with `clsx`
- Update dependencies
- Update the layout to match the PRO version

## v0.3.0

###### May 13, 2019

- Implement `jsconfig.json` file and removed `.env` to match React v16.8.6 absolute paths
- Update chart styles and options
- Update Dashboard view top widgets styles and structure
- Update few icons to match @material-ui v4 updates
- Update React version to 16.8.6 to support React Hooks
- Update to @material-ui to 4.0.0-beta

## v0.2.0

###### May 11, 2019

- Add docs for IE11 polyfill
- Fix `DisplayMode` component size, when used as a flex child it could grow/shrink
- Fix `ProductCard` component description height
- Fix `Typography` view responsiveness for small devices
- Fix charts responsiveness
- Remove "status" from `ProductCard` component since it was not part of released design
- Remove `auth` service folder since it won't be implemented for this version
- Remove `authGuard` since it won't be used in this version
- Remove unused components from shared components
- Remove unused scss from assets
- Update README.md

## v0.1.0

###### May 2, 2019

### Initial commit

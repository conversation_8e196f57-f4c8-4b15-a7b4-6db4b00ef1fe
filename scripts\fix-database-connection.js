// Fix database connection and criteriaScores issues
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

async function runCommand(command) {
  console.log(`Running command: ${command}`);
  try {
    const { stdout, stderr } = await execAsync(command);
    if (stderr) console.error(`stderr: ${stderr}`);
    console.log(`stdout: ${stdout}`);
    return true;
  } catch (error) {
    console.error(`Error executing command: ${error.message}`);
    return false;
  }
}

async function fixDatabaseConnection() {
  console.log('Starting database connection fix...');
  
  // 1. Update the .env file with the new connection string
  try {
    const envPath = path.join(__dirname, '..', '.env');
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    // Replace the old connection string with the new one
    envContent = envContent.replace(
      /DATABASE_URL=.*/,
      'DATABASE_URL=postgresql://report-generator_owner:<EMAIL>/report-generator?sslmode=require'
    );
    
    fs.writeFileSync(envPath, envContent);
    console.log('Updated .env file with new connection string');
  } catch (error) {
    console.error('Error updating .env file:', error);
    return false;
  }
  
  // 2. Create a temporary schema file for database initialization
  try {
    const schemaPath = path.join(__dirname, '..', 'prisma', 'schema.prisma');
    const tempSchemaPath = path.join(__dirname, '..', 'prisma', 'temp-schema.prisma');
    
    // Read the original schema
    const originalSchema = fs.readFileSync(schemaPath, 'utf8');
    
    // Write it to a temporary file
    fs.writeFileSync(tempSchemaPath, originalSchema);
    console.log('Created temporary schema file');
    
    // 3. Initialize the database with the schema
    const dbPushSuccess = await runCommand('npx prisma db push --schema=./prisma/temp-schema.prisma --force-reset');
    
    // Clean up the temporary file
    fs.unlinkSync(tempSchemaPath);
    console.log('Removed temporary schema file');
    
    if (!dbPushSuccess) {
      console.error('Failed to initialize database');
      return false;
    }
    
    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Error initializing database:', error);
    return false;
  }
  
  // 4. Create a migration file for criteriaScores
  try {
    const migrationDir = path.join(__dirname, '..', 'prisma', 'migrations', '20250501000000_add_criteria_scores_to_listening_entry');
    const migrationFilePath = path.join(migrationDir, 'migration.sql');
    
    // Create the directory if it doesn't exist
    if (!fs.existsSync(migrationDir)) {
      fs.mkdirSync(migrationDir, { recursive: true });
    }
    
    // Create the migration file
    fs.writeFileSync(migrationFilePath, 'ALTER TABLE "ListeningEntry" ADD COLUMN IF NOT EXISTS "criteriaScores" JSONB;');
    console.log('Created migration file for criteriaScores');
    
    // 5. Apply the migration
    const migrationSuccess = await runCommand('npx prisma migrate deploy');
    
    if (!migrationSuccess) {
      console.error('Failed to apply migration');
      // Continue anyway, as we have a workaround in the code
    } else {
      console.log('Migration applied successfully');
    }
  } catch (error) {
    console.error('Error creating or applying migration:', error);
    // Continue anyway, as we have a workaround in the code
  }
  
  console.log('Database connection fix completed');
  return true;
}

// Run the script
fixDatabaseConnection()
  .then(success => {
    if (success) {
      console.log('Database connection fixed successfully!');
    } else {
      console.error('Failed to fix database connection.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getOrCreateStudent } from '@/lib/student-utils';
import { generateReport } from '@/lib/report-utils';

// POST /api/reports/generate
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { studentIds, fileName } = body;

    console.log('Generating reports for students:', studentIds, 'with fileName:', fileName);

    if (!studentIds || !Array.isArray(studentIds) || studentIds.length === 0) {
      return NextResponse.json(
        { error: 'Student IDs array is required' },
        { status: 400 }
      );
    }

    const reports = [];

    // Generate reports for each student
    for (const studentId of studentIds) {
      try {
        // Get or create student
        const student = await getOrCreateStudent(studentId);
        console.log('Found/created student:', student.id);

        // Generate report for the student
        const report = await generateReport(studentId);
        console.log('Generated report with ID:', report.id);

        reports.push(report);
      } catch (error) {
        const studentError = error as any; // Type assertion to handle unknown error type
        console.error(`Error generating report for student ${studentId}:`, studentError);
        throw new Error(`Failed to generate report for student ${studentId}: ${studentError.message || 'Unknown error'}`);
      }
    }

    return NextResponse.json({ reports }, { status: 201 });
  } catch (error) {
    console.error('Error generating reports:', error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    const errorStack = error instanceof Error ? error.stack : undefined;

    return NextResponse.json(
      {
        error: errorMessage,
        stack: errorStack,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

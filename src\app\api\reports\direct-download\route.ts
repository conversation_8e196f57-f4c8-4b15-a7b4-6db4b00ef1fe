import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { generateCandidateReport } from '@/utils/pdf-generator';
import { fetchAndAnalyzeAnswerSheets } from '@/utils/answer-sheet-utils';

// POST /api/reports/direct-download
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { studentId } = body;

    if (!studentId) {
      return NextResponse.json(
        { error: 'Student ID is required' },
        { status: 400 }
      );
    }

    console.log('Direct download requested for student:', studentId);

    // Get all the components for this student
    const listeningEntry = await prisma.listeningEntry.findFirst({
      where: { studentId },
      orderBy: { createdAt: 'desc' },
    });

    const readingEntry = await prisma.readingEntry.findFirst({
      where: { studentId },
      orderBy: { createdAt: 'desc' },
    });

    const writingTask1Entry = await prisma.writingEntry.findFirst({
      where: {
        studentId,
        taskType: 'task1'
      },
      orderBy: { createdAt: 'desc' },
    });

    const writingTask2Entry = await prisma.writingEntry.findFirst({
      where: {
        studentId,
        taskType: 'task2'
      },
      orderBy: { createdAt: 'desc' },
    });

    const speakingEntry = await prisma.speakingEntry.findFirst({
      where: { studentId },
      orderBy: { createdAt: 'desc' },
    });

    // Get student info
    const student = await prisma.student.findUnique({
      where: { id: studentId },
    });

    if (!student) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 });
    }

    // Create a candidate object for the PDF generator
    // Use type assertions to handle properties that might not exist in the database models
    const listeningEntryAny = listeningEntry as any;
    const readingEntryAny = readingEntry as any;
    const writingTask1EntryAny = writingTask1Entry as any;
    const writingTask2EntryAny = writingTask2Entry as any;
    const speakingEntryAny = speakingEntry as any;

    const candidate = {
      id: studentId,
      studentId,
      name: student.name || studentId,
      components: [
        {
          id: listeningEntry?.id || 'listening',
          name: 'Listening',
          status: listeningEntry ? 'processed' : 'pending',
          type: 'listening',
          entryId: listeningEntry?.id || null,
          band: listeningEntry?.band || null,
          criteriaScores: listeningEntryAny?.criteriaScores || null,
          strengths: listeningEntry?.strengths || null,
          weaknesses: listeningEntry?.weaknesses || null,
          improvementSuggestions: listeningEntry?.improvementSuggestions || null,
          answerSheetImageUrl: listeningEntry?.imageUrl || null, // Use imageUrl instead of answerSheetImageUrl
        },
        {
          id: readingEntry?.id || 'reading',
          name: 'Reading',
          status: readingEntry ? 'processed' : 'pending',
          type: 'reading',
          entryId: readingEntry?.id || null,
          band: readingEntry?.band || null,
          criteriaScores: readingEntryAny?.criteriaScores || null,
          strengths: readingEntry?.strengths || null,
          weaknesses: readingEntry?.weaknesses || null,
          improvementSuggestions: readingEntry?.improvementSuggestions || null,
          answerSheetImageUrl: readingEntry?.imageUrl || null, // Use imageUrl instead of answerSheetImageUrl
        },
        {
          id: writingTask1Entry?.id || 'writing_task1',
          name: 'Writing Task 1',
          status: writingTask1Entry ? 'processed' : 'pending',
          type: 'writing_task1',
          entryId: writingTask1Entry?.id || null,
          band: writingTask1Entry?.band || null,
          criteriaScores: writingTask1EntryAny?.criteriaScores || null,
          strengths: writingTask1Entry?.strengths || null,
          weaknesses: writingTask1Entry?.weaknesses || null,
          improvementSuggestions: writingTask1Entry?.improvementSuggestions || null,
          answerSheetImageUrl: writingTask1Entry?.imageUrl || null, // Use imageUrl instead of answerSheetImageUrl
          taskQuestions: writingTask1Entry?.taskQuestion || null, // Use taskQuestion instead of taskQuestions
        },
        {
          id: writingTask2Entry?.id || 'writing_task2',
          name: 'Writing Task 2',
          status: writingTask2Entry ? 'processed' : 'pending',
          type: 'writing_task2',
          entryId: writingTask2Entry?.id || null,
          band: writingTask2Entry?.band || null,
          criteriaScores: writingTask2EntryAny?.criteriaScores || null,
          strengths: writingTask2Entry?.strengths || null,
          weaknesses: writingTask2Entry?.weaknesses || null,
          improvementSuggestions: writingTask2Entry?.improvementSuggestions || null,
          answerSheetImageUrl: writingTask2Entry?.imageUrl || null, // Use imageUrl instead of answerSheetImageUrl
          taskQuestions: writingTask2Entry?.taskQuestion || null, // Use taskQuestion instead of taskQuestions
        },
        {
          id: speakingEntry?.id || 'speaking',
          name: 'Speaking',
          status: speakingEntry ? 'processed' : 'pending',
          type: 'speaking',
          entryId: speakingEntry?.id || null,
          band: speakingEntry?.band || null,
          criteriaScores: speakingEntryAny?.criteriaScores || null,
          strengths: speakingEntry?.strengths || null,
          weaknesses: speakingEntry?.weaknesses || null,
          improvementSuggestions: speakingEntry?.improvementSuggestions || null,
          audioUrl: speakingEntry?.audioUrl || null,
        }
      ],
      reportGenerated: false,
      reportId: null,
    };

    try {
      // First, analyze any answer sheet images using OpenAI GPT-4.1
      console.log('Analyzing answer sheets with GPT-4.1...');
      const candidateWithAnalysis = await fetchAndAnalyzeAnswerSheets(candidate);
      console.log('Answer sheet analysis completed');

      // Define a type for the component to avoid implicit any
      interface ComponentWithAnalysis {
        type: string;
        answerSheetAnalysis?: any;
        band?: number;
      }

      // Log the analysis results for debugging
      console.log('Analysis results:', {
        listeningAnalysis: candidateWithAnalysis.components.find((c: ComponentWithAnalysis) => c.type === 'listening')?.answerSheetAnalysis ? 'Present' : 'Not present',
        readingAnalysis: candidateWithAnalysis.components.find((c: ComponentWithAnalysis) => c.type === 'reading')?.answerSheetAnalysis ? 'Present' : 'Not present',
        writingTask1Analysis: candidateWithAnalysis.components.find((c: ComponentWithAnalysis) => c.type === 'writing_task1')?.answerSheetAnalysis ? 'Present' : 'Not present',
        writingTask2Analysis: candidateWithAnalysis.components.find((c: ComponentWithAnalysis) => c.type === 'writing_task2')?.answerSheetAnalysis ? 'Present' : 'Not present',
        speakingAnalysis: candidateWithAnalysis.components.find((c: ComponentWithAnalysis) => c.type === 'speaking')?.answerSheetAnalysis ? 'Present' : 'Not present'
      });

      // Extract band scores from analysis if available
      candidateWithAnalysis.components.forEach((comp: ComponentWithAnalysis) => {
        if (comp.answerSheetAnalysis && comp.answerSheetAnalysis.score && comp.answerSheetAnalysis.score.overall) {
          // Set the band score from the analysis
          comp.band = parseFloat(comp.answerSheetAnalysis.score.overall);
          console.log(`Set ${comp.type} band score to ${comp.band} from analysis`);
        }
      });

      // Generate the PDF
      console.log('Generating PDF with analyzed data...');
      const pdfResult = generateCandidateReport(candidateWithAnalysis);
      console.log('PDF generation completed successfully');

      // Extract the base64 data from the data URI
      const base64Data = pdfResult.pdfBase64.split(',')[1];
      const binaryData = Buffer.from(base64Data, 'base64');

      // Return the PDF as a downloadable file
      return new NextResponse(binaryData, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${pdfResult.fileName}"`,
        },
      });
    } catch (analysisError) {
      console.error('Error with analysis, falling back to direct PDF generation:', analysisError);

      // Fallback to generating PDF without analysis
      // Use type assertion to match the expected Candidate type
      const pdfResult = generateCandidateReport(candidate as any);

      // Extract the base64 data from the data URI
      const base64Data = pdfResult.pdfBase64.split(',')[1];
      const binaryData = Buffer.from(base64Data, 'base64');

      // Return the PDF as a downloadable file
      return new NextResponse(binaryData, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${pdfResult.fileName}"`,
        },
      });
    }
  } catch (error) {
    console.error('Error generating direct PDF report:', error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    const errorStack = error instanceof Error ? error.stack : undefined;

    // Log detailed error information
    console.error('PDF generation error details:', {
      message: errorMessage,
      stack: errorStack,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json(
      {
        error: 'An unexpected error occurred while generating the PDF report',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

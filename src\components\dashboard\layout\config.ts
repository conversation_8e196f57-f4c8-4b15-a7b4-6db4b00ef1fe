import type { NavItemConfig } from '@/types/nav';
import { paths } from '@/paths';

export const navItems = [
  // { key: 'overview', title: 'Overview', href: paths.dashboard.overview, icon: 'chart-pie' },
  // { key: 'customers', title: 'Customers', href: paths.dashboard.customers, icon: 'users' },
  // { key: 'integrations', title: 'Integrations', href: paths.dashboard.integrations, icon: 'plugs-connected' },
  { key: 'candidate-assessment', title: 'Candidate Assessment', href: paths.dashboard.candidateAssessment, icon: 'users' },
  { key: 'bulk-writing-checker', title: 'Bulk Writing Check', href: paths.dashboard.bulkWritingChecker, icon: 'stack' },
  { key: 'bulk-detailed-writing-checker', title: 'Bulk Detailed Check', href: paths.dashboard.bulkDetailedWritingChecker, icon: 'files' },
  { key: 'handwritten-scanner', title: 'Handwritten Scanner', href: paths.dashboard.handwrittenScanner, icon: 'scan' },
  { key: 'bulk-speaking-checker', title: 'Bulk Speaking Check', href: paths.dashboard.bulkSpeakingChecker, icon: 'speaker-high' },
  { key: 'bulk-detailed-speaking-checker', title: 'Bulk Detailed Speaking', href: paths.dashboard.bulkDetailedSpeakingChecker, icon: 'files' },
  { key: 'bulk-reading-checker', title: 'Bulk Reading Check', href: paths.dashboard.bulkReadingChecker, icon: 'magnifying-glass' },
  { key: 'simplified-reading-checker', title: 'Simplified Reading', href: paths.dashboard.simplifiedReadingChecker, icon: 'magnifying-glass' },
  { key: 'bulk-listening-checker', title: 'Bulk Listening Check', href: paths.dashboard.bulkListeningChecker, icon: 'speaker-high' },
  { key: 'simplified-listening-checker', title: 'Simplified Listening', href: paths.dashboard.simplifiedListeningChecker, icon: 'speaker-high' },
  { key: 'reports', title: 'Reports', href: paths.dashboard.reports, icon: 'file-text' },
  // { key: 'settings', title: 'Settings', href: paths.dashboard.settings, icon: 'gear-six' },
  // { key: 'error', title: 'Error', href: paths.errors.notFound, icon: 'x-square' },
] satisfies NavItemConfig[];

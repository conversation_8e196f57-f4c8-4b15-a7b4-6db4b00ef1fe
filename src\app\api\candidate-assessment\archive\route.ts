import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// POST handler to archive all candidates
export async function POST(req: NextRequest) {
  try {
    // Get all students
    const students = await prisma.student.findMany({
      select: { id: true }
    });

    if (students.length === 0) {
      return NextResponse.json({ message: 'No candidates to archive' });
    }

    // Get the IDs of all students
    const studentIds = students.map(student => student.id);

    // Create a transaction to archive all related data
    await prisma.$transaction(async (tx) => {
      // Archive reports
      await tx.report.updateMany({
        where: {
          studentId: {
            in: studentIds
          }
        },
        data: {
          // Mark as archived
          printStatus: 'archived'
        }
      });

      // We don't actually delete the data, just clear the UI
      // This allows for data recovery if needed
    });

    return NextResponse.json({ 
      message: 'All candidates archived successfully',
      count: students.length
    });
  } catch (error) {
    console.error('Error archiving candidates:', error);
    return NextResponse.json(
      { error: 'Failed to archive candidates' },
      { status: 500 }
    );
  }
}

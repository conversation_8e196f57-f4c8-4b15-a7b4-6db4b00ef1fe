import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function POST(req: NextRequest) {
  try {
    // Parse the form data
    const formData = await req.formData();
    const studentId = formData.get('studentId') as string;
    const componentType = formData.get('componentType') as string;
    // Get user answers if provided (for reading and listening components)
    const userAnswers = formData.get('userAnswers') as string || '';
    // Get correct answers if provided (for reading and listening components)
    const correctAnswers = formData.get('correctAnswers') as string || '';
    // Get entry ID if this is an update to an existing entry
    const entryId = formData.get('entryId') as string || null;
    // We don't need material ID anymore as we're using uploaded correct answers directly

    // Check if this is a multi-file upload for writing tasks
    const fileCount = parseInt(formData.get('fileCount') as string || '0');
    let files: File[] = [];

    if (fileCount > 0) {
      // This is a multi-file upload
      for (let i = 0; i < fileCount; i++) {
        const file = formData.get(`file${i}`) as File;
        if (file) {
          files.push(file);
        }
      }
      console.log('Upload request received:', { componentType, studentId, fileCount: files.length });
    } else {
      // This is a single file upload
      const file = formData.get('file') as File;
      if (file) {
        files.push(file);
      }
      console.log('Upload request received:', { componentType, studentId, fileName: files[0]?.name });
    }

    // Additional validation for files
    if (files.length === 0 &&
        !(componentType === 'writing_task1' || componentType === 'writing_task2') &&
        !(componentType === 'speaking' && entryId)) {
      console.error('No files provided in the request');
      return NextResponse.json(
        { error: 'No files provided in the request' },
        { status: 400 }
      );
    }

    // For writing tasks, we can proceed without files if essay text is provided
    if (files.length === 0 && (componentType === 'writing_task1' || componentType === 'writing_task2')) {
      const essayText = formData.get('essayText') as string;
      if (!essayText) {
        console.error('No files or essay text provided for writing task');
        return NextResponse.json(
          { error: 'Either files or essay text must be provided for writing tasks' },
          { status: 400 }
        );
      }
    }

    // Validate file type based on component type
    const validImageTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    const validAudioTypes = [
      'audio/mpeg', 'audio/mp3', // MP3
      'audio/mp4', 'audio/x-m4a', // M4A
      'audio/wav', 'audio/x-wav', // WAV
      'audio/flac', // FLAC
      'audio/ogg', 'audio/x-ogg', 'audio/vorbis', // OGG
      'audio/webm' // WEBM
    ];

    if (files.length > 0) {
      if (componentType === 'speaking') {
        // For speaking components, validate audio file types
        if (!validAudioTypes.includes(files[0].type)) {
          console.error(`Invalid file type for speaking component: ${files[0].type}`);

          // Check if it's an m4a file with a non-standard MIME type
          const fileName = files[0].name.toLowerCase();
          if (fileName.endsWith('.m4a')) {
            console.log('Detected M4A file with non-standard MIME type, suggesting conversion to MP3');
            return NextResponse.json(
              { error: `Your M4A file format (${files[0].type}) is not compatible. Please convert it to MP3 format and try again.` },
              { status: 400 }
            );
          }

          return NextResponse.json(
            { error: `File type must be one of MP3, WAV, FLAC, or OGG for speaking components. Got: ${files[0].type}` },
            { status: 400 }
          );
        }
      } else {
        // For all other components, validate image file types
        for (const file of files) {
          if (!validImageTypes.includes(file.type)) {
            console.error(`Invalid file type: ${file.type}`);
            return NextResponse.json(
              { error: `File type must be one of JPG, PNG, or PDF for this component. Got: ${file.type}` },
              { status: 400 }
            );
          }
        }
      }
    }

    // Validate input
    if (!studentId || !componentType) {
      return NextResponse.json(
        { error: 'Student ID and component type are required' },
        { status: 400 }
      );
    }

    // Check if student exists
    let student;
    try {
      student = await prisma.student.findUnique({
        where: { id: studentId },
      });

      if (!student) {
        console.log(`Student with ID ${studentId} not found, creating a new student record`);

        // Create the student if it doesn't exist
        student = await prisma.student.create({
          data: {
            id: studentId,
            name: `Student ${studentId}`,
          },
        });

        console.log(`Created new student with ID ${student.id}`);
      }
    } catch (studentError) {
      console.error('Error finding or creating student:', studentError);
      return NextResponse.json(
        { error: 'Failed to find or create student', details: studentError instanceof Error ? studentError.message : 'Unknown error' },
        { status: 500 }
      );
    }

    // Process the files - in a real app, you would upload to a storage service
    // For now, we'll create data URLs from the files
    let fileUrls: string[] = [];

    if (files.length > 0) {
      try {
        for (const file of files) {
          console.log('Processing file:', file.name, 'type:', file.type, 'size:', file.size);
          const arrayBuffer = await file.arrayBuffer();
          const buffer = Buffer.from(arrayBuffer);
          const base64 = buffer.toString('base64');
          const mimeType = file.type || 'application/octet-stream';
          const fileUrl = `data:${mimeType};base64,${base64}`;
          fileUrls.push(fileUrl);
        }
        console.log(`Converted ${fileUrls.length} files to data URLs successfully`);
      } catch (fileError) {
        console.error('Error processing files:', fileError);
        return NextResponse.json(
          { error: 'Failed to process the uploaded files', details: fileError instanceof Error ? fileError.message : 'Unknown error' },
          { status: 500 }
        );
      }
    }

    let result;

    // Handle different component types
    switch (componentType) {
      case 'writing_task1':
      case 'writing_task2':
        // Get task questions and type
        let taskQuestions = formData.get('taskQuestions') as string || '';
        const taskType = formData.get('taskType') as string || (componentType === 'writing_task1' ? 'task1' : 'task2');
        const essayText = formData.get('essayText') as string || '';

        // Ensure taskQuestions is not empty
        if (!taskQuestions || taskQuestions.trim() === '') {
          taskQuestions = taskType === 'task1' ?
            'Default Task 1 question: Describe the chart or graph.' :
            'Default Task 2 question: Write an essay on the given topic.';
        }

        console.log('Creating writing entry with data:', {
          studentId,
          taskType,
          taskQuestion: taskQuestions,
          hasImageUrls: fileUrls.length > 0,
          imageCount: fileUrls.length,
          hasEssayText: !!essayText,
          essayTextLength: essayText ? essayText.length : 0
        });

        try {
          // First, check if we need to create a default writing material
          let writingMaterial = await prisma.writingMaterial.findFirst({
            where: {
              title: 'Default Writing Material',
              taskType: taskType
            }
          });

          if (!writingMaterial) {
            // Create a default material if none exists
            writingMaterial = await prisma.writingMaterial.create({
              data: {
                title: 'Default Writing Material',
                taskType: taskType,
                taskQuestion: taskQuestions || 'Default task question',
                // If this is task 1 and we have an image, store it in the material
                ...(taskType === 'task1' && fileUrls.length > 0 ? {
                  imageUrl: fileUrls.length === 1 ? fileUrls[0] : JSON.stringify(fileUrls)
                } : {})
              }
            });
            console.log('Created default writing material with ID:', writingMaterial.id);
          } else if (taskType === 'task1' && fileUrls.length > 0) {
            // Update the existing material with the task 1 image if needed
            writingMaterial = await prisma.writingMaterial.update({
              where: { id: writingMaterial.id },
              data: {
                taskImage: fileUrls.length === 1 ? fileUrls[0] : JSON.stringify(fileUrls)
              }
            });
            console.log('Updated writing material with task 1 image:', writingMaterial.id);
          }

          // Check if we need to update an existing entry or create a new one
          if (entryId) {
            // Update existing writing entry
            result = await prisma.writingEntry.update({
              where: { id: entryId },
              data: {
                // Only update fields that are provided
                ...(taskQuestions ? { taskQuestion: taskQuestions } : {}),
                ...(essayText ? { essayText: essayText } : {}),
                // Update the image URL if new files are uploaded
                ...(fileUrls.length > 0 ? {
                  imageUrl: fileUrls.length === 1 ? fileUrls[0] : JSON.stringify(fileUrls)
                } : {}),
              },
            });
            console.log('Updated existing writing entry with ID:', result.id);
          } else {
            // Create new writing entry
            result = await prisma.writingEntry.create({
              data: {
                studentId,
                materialId: writingMaterial.id, // Use the default material ID
                taskType,
                // Use provided essay text or default
                essayText: essayText || 'Pending AI analysis',
                taskQuestion: taskQuestions || 'Default task question',
                // Store the image URLs as JSON if we have multiple images
                imageUrl: fileUrls.length > 0 ?
                  (fileUrls.length === 1 ? fileUrls[0] : JSON.stringify(fileUrls)) :
                  null,
              },
            });
            console.log('Created new writing entry with ID:', result.id);
          }

        } catch (prismaError) {
          console.error('Prisma error creating writing entry:', prismaError);
          console.error('Data being used:', {
            studentId,
            taskType,
            taskQuestion: taskQuestions,
            imageUrlsCount: fileUrls.length,
            essayTextLength: essayText ? essayText.length : 0
          });

          // Try a fallback approach with minimal data
          try {
            console.log('Attempting fallback approach for writing entry creation');
            result = await prisma.writingEntry.create({
              data: {
                studentId,
                taskType,
                essayText: 'Pending analysis', // Minimal required data
              },
            });
            console.log('Fallback approach succeeded with ID:', result.id);
          } catch (fallbackError) {
            console.error('Fallback approach also failed:', fallbackError);
            throw prismaError; // Re-throw the original error
          }
        }

        break;
      case 'listening':
        console.log('Creating listening entry with data:', {
          studentId,
          userAnswers: userAnswers || 'None provided',
          correctAnswers: correctAnswers || 'None provided',
          hasImageUrl: fileUrls.length > 0
        });

        // First, check if we have a default listening material, or create one if needed
        let listeningMaterial = await prisma.listeningMaterial.findFirst({
          where: { title: 'Default Listening Material' }
        });

        if (!listeningMaterial) {
          // Create a default material if none exists
          listeningMaterial = await prisma.listeningMaterial.create({
            data: {
              title: 'Default Listening Material',
              audioUrl: 'https://example.com/default-audio.mp3',
              questions: {},
              answers: correctAnswers
                ? correctAnswers.split(',').map(a => a.trim())
                : []
            }
          });
        } else if (correctAnswers) {
          // Update the material with the new correct answers if provided
          listeningMaterial = await prisma.listeningMaterial.update({
            where: { id: listeningMaterial.id },
            data: {
              answers: correctAnswers.split(',').map(a => a.trim())
            }
          });
        }

        // Check if we need to update an existing entry or create a new one
        if (entryId) {
          // Update existing listening entry
          result = await prisma.listeningEntry.update({
            where: { id: entryId },
            data: {
              answers: userAnswers ? userAnswers.split(',').map(a => a.trim()) : [], // Use provided user answers
              imageUrl: fileUrls.length > 0 ? fileUrls[0] : undefined,
            },
          });
          console.log('Updated existing listening entry with ID:', result.id);
        } else {
          // Create new listening entry
          result = await prisma.listeningEntry.create({
            data: {
              studentId,
              materialId: listeningMaterial.id,
              answers: userAnswers ? userAnswers.split(',').map(a => a.trim()) : [], // Use provided user answers
              imageUrl: fileUrls.length > 0 ? fileUrls[0] : null,
              // Store the correct answers as metadata for processing
              metadata: correctAnswers ? { correctAnswersText: correctAnswers } : undefined,
            },
          });
          console.log('Created new listening entry with ID:', result.id);
        }
        break;

      case 'reading':
        console.log('Creating reading entry with data:', {
          studentId,
          userAnswers: userAnswers || 'None provided',
          correctAnswers: correctAnswers || 'None provided',
          hasImageUrl: fileUrls.length > 0
        });

        // First, check if we have a default reading material, or create one if needed
        let readingMaterial;
        try {
          readingMaterial = await prisma.readingMaterial.findFirst({
            where: { title: 'Default Reading Material' }
          });

          if (!readingMaterial) {
            console.log('No default reading material found, creating one...');
            // Create a default material if none exists
            readingMaterial = await prisma.readingMaterial.create({
              data: {
                title: 'Default Reading Material',
                passage: 'Default passage text',
                questions: {},
                answers: correctAnswers
                  ? correctAnswers.split(',').map(a => a.trim())
                  : []
              }
            });
            console.log('Created default reading material with ID:', readingMaterial.id);
          } else if (correctAnswers) {
            console.log('Updating existing reading material with ID:', readingMaterial.id);
            // Update the material with the new correct answers if provided
            readingMaterial = await prisma.readingMaterial.update({
              where: { id: readingMaterial.id },
              data: {
                answers: correctAnswers.split(',').map(a => a.trim())
              }
            });
            console.log('Updated reading material successfully');
          }
        } catch (materialError) {
          console.error('Error with reading material:', materialError);
          // Create a fallback material with minimal data
          console.log('Creating fallback reading material...');
          readingMaterial = await prisma.readingMaterial.create({
            data: {
              title: 'Fallback Reading Material',
              passage: 'Fallback passage text',
              questions: {},
              answers: []
            }
          });
          console.log('Created fallback reading material with ID:', readingMaterial.id);
        }

        // Check if we need to update an existing entry or create a new one
        if (entryId) {
          // Update existing reading entry
          result = await prisma.readingEntry.update({
            where: { id: entryId },
            data: {
              answers: userAnswers ? userAnswers.split(',').map(a => a.trim()) : [], // Use provided user answers
              imageUrl: fileUrls.length > 0 ? fileUrls[0] : undefined,
            },
          });
          console.log('Updated existing reading entry with ID:', result.id);
        } else {
          // Create new reading entry
          result = await prisma.readingEntry.create({
            data: {
              studentId,
              materialId: readingMaterial.id,
              answers: userAnswers ? userAnswers.split(',').map(a => a.trim()) : [], // Use provided user answers
              imageUrl: fileUrls.length > 0 ? fileUrls[0] : null,
              // Store the correct answers as metadata for processing
              metadata: correctAnswers ? { correctAnswersText: correctAnswers } : undefined,
            },
          });
          console.log('Created new reading entry with ID:', result.id);
        }
        break;

      case 'speaking':
        // Get additional fields for speaking
        const transcription = formData.get('transcription') as string || '';
        const examinerContent = formData.get('examinerContent') as string || '';
        const isSimulatedTranscript = formData.get('isSimulatedTranscript') === 'true';
        // No longer using part number as we're processing the entire conversation

        console.log('Creating/updating speaking entry with audio file:', {
          studentId,
          entryId: entryId || 'New entry',
          fileName: files.length > 0 ? files[0].name : 'No file',
          fileType: files.length > 0 ? files[0].type : 'No file type',
          fileSize: files.length > 0 ? files[0].size : 0,
          hasTranscription: !!transcription,
          hasExaminerContent: !!examinerContent,
          isSimulatedTranscript,
          isUpdate: !!entryId
        });

        // Check if we have a valid transcription
        if (!transcription && files.length === 0) {
          console.error('No transcription or audio file provided for speaking component');
          return NextResponse.json(
            { error: 'Either transcription or audio file must be provided for speaking components' },
            { status: 400 }
          );
        }

        try {
          // Check if we need to update an existing entry or create a new one
          if (entryId) {
            // Update existing speaking entry
            result = await prisma.speakingEntry.update({
              where: { id: entryId },
              data: {
                // Only update fields that are provided
                ...(transcription ? { transcription } : {}),
                ...(examinerContent ? { examinerContent } : {}),
                // Update the audio URL if new files are uploaded
                ...(fileUrls.length > 0 ? { audioUrl: fileUrls[0] } : {}),
                // isSimulated field is commented out because it doesn't exist in the database yet
                // isSimulated: isSimulatedTranscript
              },
            });
            console.log('Updated existing speaking entry with ID:', result.id);
          } else {
            // Create new speaking entry
            result = await prisma.speakingEntry.create({
              data: {
                studentId,
                audioUrl: fileUrls.length > 0 ? fileUrls[0] : null,
                transcription,
                examinerContent
                // isSimulated field is commented out because it doesn't exist in the database yet
                // isSimulated: isSimulatedTranscript
              },
            });
            console.log('Speaking entry created successfully with ID:', result.id);
          }
        } catch (error) {
          const prismaError = error as any; // Type assertion to handle unknown error type
          console.error('Prisma error creating speaking entry:', prismaError);

          // Check if the error is related to the isSimulated field not existing in the schema
          if (prismaError.message && typeof prismaError.message === 'string' && prismaError.message.includes('Unknown field')) {
            console.log('isSimulated field not found in schema, trying without it');

            // Try again without the isSimulated field
            if (entryId) {
              // Update existing speaking entry
              result = await prisma.speakingEntry.update({
                where: { id: entryId },
                data: {
                  // Only update fields that are provided
                  ...(transcription ? { transcription } : {}),
                  ...(examinerContent ? { examinerContent } : {}),
                  // Update the audio URL if new files are uploaded
                  ...(fileUrls.length > 0 ? { audioUrl: fileUrls[0] } : {}),
                },
              });
            } else {
              // Create new speaking entry
              result = await prisma.speakingEntry.create({
                data: {
                  studentId,
                  audioUrl: fileUrls.length > 0 ? fileUrls[0] : null,
                  transcription,
                  examinerContent,
                },
              });
            }
            console.log('Entry created/updated successfully without isSimulated field');
          } else {
            // Try a fallback approach with minimal data
            try {
              console.log('Attempting fallback approach for speaking entry creation');
              result = await prisma.speakingEntry.create({
                data: {
                  studentId,
                  // Only include the minimal required fields
                },
              });
              console.log('Fallback approach succeeded with ID:', result.id);
            } catch (fallbackError) {
              console.error('Fallback approach also failed:', fallbackError);
              throw prismaError; // Re-throw the original error
            }
          }
        }
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid component type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      id: result.id,
      status: 'success'
    });
  } catch (error) {
    console.error('Error uploading file:', error);

    // Provide more detailed error information
    let errorMessage = 'Failed to upload file';
    let errorDetails = {};

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = { stack: error.stack };

      // Check for Prisma-specific errors
      if (errorMessage.includes('prisma')) {
        console.error('Prisma error details:', error);
      }
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: errorDetails,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  LinearProgress,
  MenuItem,
  Paper,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  Alert,
} from '@mui/material';
import {
  Plus,
  Trash,
  Download,
  Upload,
  Eye,
  Check,
  X,
  Warning,
} from '@phosphor-icons/react';
import { useDropzone } from 'react-dropzone';
import <PERSON> from 'papaparse';

// Generate a unique ID for each entry
const generateId = () => `entry-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

// Interface for listening material
interface ListeningMaterial {
  id: string;
  title: string;
  audioUrl: string;
  transcript?: string;
  questions: any;
  answers: any;
  section?: number;
  createdAt: string;
}

// Interface for listening entry
interface ListeningEntry {
  id: string;
  studentId: string;
  materialId: string;
  materialTitle?: string;
  answers: Record<string, string>;
  imageUrl?: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  score?: number;
  band?: number;
  correctAnswers?: number;
  totalQuestions?: number;
  mistakes?: Array<{
    questionNumber: number;
    userAnswer: string;
    correctAnswer: string;
    explanation?: string;
  }>;
  analysis?: string;
  strengths?: string[];
  weaknesses?: string[];
  improvementSuggestions?: string[];
  error?: string;
}

export default function BulkListeningCheckerPage() {
  // State for entries
  const [entries, setEntries] = useState<ListeningEntry[]>([]);

  // State for materials
  const [materials, setMaterials] = useState<ListeningMaterial[]>([]);

  // State for loading
  const [loading, setLoading] = useState(false);

  // State for error
  const [error, setError] = useState<string | null>(null);

  // State for global student ID
  const [globalStudentId, setGlobalStudentId] = useState('');

  // State for add entry dialog
  const [showAddDialog, setShowAddDialog] = useState(false);

  // State for new entry
  const [newEntry, setNewEntry] = useState<{
    studentId: string;
    materialId: string;
    answers: Record<string, string>;
  }>({
    studentId: '',
    materialId: '',
    answers: {},
  });

  // State for result dialog
  const [showResultDialog, setShowResultDialog] = useState(false);
  const [resultDialogEntry, setResultDialogEntry] = useState<ListeningEntry | null>(null);

  // State for selected model
  const [selectedModel, setSelectedModel] = useState('gpt-4o');

  // Fetch materials on component mount
  useEffect(() => {
    const fetchMaterials = async () => {
      try {
        const response = await fetch('/api/listening-materials');
        if (!response.ok) {
          throw new Error('Failed to fetch listening materials');
        }
        const data = await response.json();
        setMaterials(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      }
    };

    fetchMaterials();
  }, []);

  // Handle add entry
  const handleAddEntry = () => {
    if (!newEntry.materialId) {
      setError('Please select a material');
      return;
    }

    // Use global student ID if available and no specific student ID is provided
    const studentId = newEntry.studentId || globalStudentId || `anonymous-${Date.now()}`;

    // Find the selected material to get its title
    const selectedMaterial = materials.find(m => m.id === newEntry.materialId);

    setEntries([
      ...entries,
      {
        id: generateId(),
        studentId,
        materialId: newEntry.materialId,
        materialTitle: selectedMaterial?.title,
        answers: newEntry.answers,
        status: 'pending',
      },
    ]);

    // Reset new entry
    setNewEntry({
      studentId: '',
      materialId: '',
      answers: {},
    });

    // Close dialog
    setShowAddDialog(false);
  };

  // Handle remove entry
  const handleRemoveEntry = (id: string) => {
    setEntries(entries.filter(entry => entry.id !== id));
  };

  // Handle process entries
  const handleProcessEntries = async () => {
    if (entries.length === 0) {
      setError('Please add at least one entry');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Update status to processing
      setEntries(entries.map(entry => ({
        ...entry,
        status: 'processing'
      })));

      const response = await fetch('/api/bulk-listening-checker', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          entries: entries.map(entry => ({
            id: entry.id,
            studentId: entry.studentId,
            materialId: entry.materialId,
            answers: entry.answers,
            imageUrl: entry.imageUrl
          })),
          model: selectedModel
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process entries');
      }

      const data = await response.json();

      // Update entries with results
      setEntries(entries.map(entry => {
        const result = data.results.find((r: any) => r.id === entry.id);
        if (result) {
          if (result.status === 'error') {
            return {
              ...entry,
              status: 'error',
              error: result.error
            };
          }
          return {
            ...entry,
            ...result,
            status: 'completed'
          };
        }
        return entry;
      }));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Handle CSV import
  const handleCsvImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    Papa.parse(file, {
      header: true,
      complete: (results) => {
        if (results.data && Array.isArray(results.data) && results.data.length > 0) {
          const newEntries = results.data
            .filter((row: any) => row.materialId) // Filter out rows without materialId
            .map((row: any) => {
              // Extract answers from the CSV
              const answers: Record<string, string> = {};
              Object.keys(row).forEach(key => {
                if (key.startsWith('answer_') && row[key]) {
                  const questionId = key.replace('answer_', '');
                  answers[questionId] = row[key];
                }
              });

              return {
                id: generateId(),
                studentId: row.studentId || globalStudentId || `anonymous-${Date.now()}`,
                materialId: row.materialId,
                materialTitle: materials.find(m => m.id === row.materialId)?.title,
                answers,
                status: 'pending'
              };
            });

          // Type assertion to match the expected ListeningEntry[] type
          setEntries([...entries, ...(newEntries as ListeningEntry[])]);
        }
      },
      error: (error) => {
        setError(`Error parsing CSV: ${error.message}`);
      }
    });

    // Reset the file input
    event.target.value = '';
  };

  // Handle CSV export
  const handleCsvExport = () => {
    const csvData = entries.map(entry => {
      const baseData = {
        id: entry.id,
        studentId: entry.studentId,
        materialId: entry.materialId,
        materialTitle: entry.materialTitle,
        status: entry.status,
        score: entry.score || '',
        band: entry.band || '',
      };

      // Add answers with prefix
      const answerData: Record<string, string> = {};
      Object.entries(entry.answers).forEach(([key, value]) => {
        answerData[`answer_${key}`] = value;
      });

      return {
        ...baseData,
        ...answerData,
      };
    });

    const csv = Papa.unparse(csvData);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `listening-entries-${new Date().toISOString().slice(0, 10)}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Handle view result
  const handleViewResult = (entry: ListeningEntry) => {
    setResultDialogEntry(entry);
    setShowResultDialog(true);
  };

  // State for image upload dialog
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [uploadMaterialId, setUploadMaterialId] = useState('');
  const [uploadStudentId, setUploadStudentId] = useState('');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  // Dropzone for image upload
  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png']
    },
    onDrop: async (acceptedFiles) => {
      if (acceptedFiles.length === 0) return;
      if (!uploadMaterialId) {
        setError('Please select a material');
        return;
      }

      try {
        setIsUploading(true);
        setUploadProgress(10);

        // Create FormData
        const formData = new FormData();
        formData.append('image', acceptedFiles[0]);
        formData.append('studentId', uploadStudentId || globalStudentId || `anonymous-${Date.now()}`);
        formData.append('materialId', uploadMaterialId);

        setUploadProgress(30);

        // Send the image to the API
        const response = await fetch('/api/bulk-listening-checker', {
          method: 'POST',
          body: formData
        });

        setUploadProgress(70);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to process image');
        }

        const data = await response.json();
        setUploadProgress(100);

        // Add the new entry to the list
        if (data.results && data.results.length > 0) {
          const result = data.results[0];
          const selectedMaterial = materials.find(m => m.id === uploadMaterialId);

          setEntries(prev => [
            ...prev,
            {
              id: result.id || generateId(),
              studentId: result.studentId,
              materialId: uploadMaterialId,
              materialTitle: selectedMaterial?.title,
              answers: result.answers || {},
              imageUrl: result.imageUrl,
              status: result.status || 'completed',
              score: result.score,
              band: result.band,
              correctAnswers: result.correctAnswers,
              totalQuestions: result.totalQuestions,
              mistakes: result.mistakes,
              analysis: result.analysis,
              strengths: result.strengths,
              weaknesses: result.weaknesses,
              improvementSuggestions: result.improvementSuggestions,
            }
          ]);
        }

        // Close the dialog
        setShowUploadDialog(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      } finally {
        setIsUploading(false);
        setUploadProgress(0);
      }
    },
    multiple: false
  });

  // Handle upload dialog open
  const handleOpenUploadDialog = () => {
    setUploadMaterialId('');
    setUploadStudentId(globalStudentId);
    setShowUploadDialog(true);
  };

  return (
    <Box>
      <Stack spacing={3}>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={8}>
            <Typography variant="h4">Bulk Listening Checker</Typography>
            <Typography color="text.secondary" sx={{ mt: 1 }}>
              Process multiple listening test answers at once
            </Typography>
          </Grid>
          <Grid item xs={12} md={4} sx={{ textAlign: 'right' }}>
            <Stack direction="row" spacing={1} justifyContent="flex-end">
              <Button
                variant="outlined"
                startIcon={<Upload />}
                component="label"
              >
                Import CSV
                <input
                  type="file"
                  hidden
                  accept=".csv"
                  onChange={handleCsvImport}
                />
              </Button>
              <Button
                variant="outlined"
                startIcon={<Download />}
                onClick={handleCsvExport}
                disabled={entries.length === 0}
              >
                Export CSV
              </Button>
              <Button
                variant="outlined"
                startIcon={<Upload />}
                onClick={handleOpenUploadDialog}
                sx={{ mr: 1 }}
              >
                Upload Answer Sheet
              </Button>
              <Button
                variant="contained"
                startIcon={<Plus />}
                onClick={() => setShowAddDialog(true)}
              >
                Add Entry
              </Button>
            </Stack>
          </Grid>
        </Grid>

        {error && (
          <Alert severity="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        <Card>
          <CardContent>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Global Student ID"
                  value={globalStudentId}
                  onChange={(e) => setGlobalStudentId(e.target.value)}
                  helperText="This ID will be used for all entries without a specific student ID"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel id="model-select-label">AI Model</InputLabel>
                  <Select
                    labelId="model-select-label"
                    value={selectedModel}
                    label="AI Model"
                    onChange={(e) => setSelectedModel(e.target.value)}
                  >
                    <MenuItem value="gpt-4o">GPT-4o</MenuItem>
                    <MenuItem value="gpt-4-turbo">GPT-4 Turbo</MenuItem>
                    <MenuItem value="gpt-3.5-turbo">GPT-3.5 Turbo</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleProcessEntries}
                disabled={loading || entries.length === 0}
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
              >
                {loading ? 'Processing...' : 'Process All Entries'}
              </Button>
            </Box>
            <Box sx={{ overflowX: 'auto' }}>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Student ID</TableCell>
                      <TableCell>Material</TableCell>
                      <TableCell>Answers</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Score</TableCell>
                      <TableCell>Band</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {entries.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} align="center">
                          No entries added yet
                        </TableCell>
                      </TableRow>
                    ) : (
                      entries.map((entry) => (
                        <TableRow key={entry.id}>
                          <TableCell>{entry.studentId}</TableCell>
                          <TableCell>{entry.materialTitle || entry.materialId}</TableCell>
                          <TableCell>
                            {Object.keys(entry.answers).length} answers
                          </TableCell>
                          <TableCell>
                            {entry.status === 'pending' && 'Pending'}
                            {entry.status === 'processing' && (
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <CircularProgress size={16} sx={{ mr: 1 }} />
                                Processing
                              </Box>
                            )}
                            {entry.status === 'completed' && (
                              <Box sx={{ display: 'flex', alignItems: 'center', color: 'success.main' }}>
                                <Check size={16} weight="bold" style={{ marginRight: 4 }} />
                                Completed
                              </Box>
                            )}
                            {entry.status === 'error' && (
                              <Box sx={{ display: 'flex', alignItems: 'center', color: 'error.main' }}>
                                <X size={16} weight="bold" style={{ marginRight: 4 }} />
                                Error
                              </Box>
                            )}
                          </TableCell>
                          <TableCell>
                            {entry.score !== undefined ? `${entry.score}/${entry.totalQuestions}` : '-'}
                          </TableCell>
                          <TableCell>{entry.band !== undefined ? entry.band.toFixed(1) : '-'}</TableCell>
                          <TableCell>
                            <Stack direction="row" spacing={1}>
                              {entry.status === 'completed' && (
                                <IconButton
                                  size="small"
                                  color="primary"
                                  onClick={() => handleViewResult(entry)}
                                >
                                  <Eye />
                                </IconButton>
                              )}
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleRemoveEntry(entry.id)}
                              >
                                <Trash />
                              </IconButton>
                            </Stack>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>

            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                onClick={handleProcessEntries}
                disabled={entries.length === 0 || loading}
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
              >
                {loading ? 'Processing...' : 'Process Entries'}
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Stack>

      {/* Add Entry Dialog */}
      <Dialog open={showAddDialog} onClose={() => setShowAddDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add Listening Entry</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              fullWidth
              label="Student ID"
              value={newEntry.studentId}
              onChange={(e) => setNewEntry({ ...newEntry, studentId: e.target.value })}
              helperText={globalStudentId ? `Using global ID: ${globalStudentId} if left empty` : 'Leave empty to use global ID'}
            />

            <FormControl fullWidth>
              <InputLabel id="material-select-label">Listening Material</InputLabel>
              <Select
                labelId="material-select-label"
                value={newEntry.materialId}
                label="Listening Material"
                onChange={(e) => setNewEntry({ ...newEntry, materialId: e.target.value })}
              >
                {materials.map((material) => (
                  <MenuItem key={material.id} value={material.id}>
                    {material.title}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {newEntry.materialId && (
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Answers
                </Typography>
                <Paper sx={{ p: 2 }}>
                  {materials.find(m => m.id === newEntry.materialId)?.questions && (
                    Object.entries(materials.find(m => m.id === newEntry.materialId)?.questions || {}).map(([questionId, question]: [string, any]) => (
                      <TextField
                        key={questionId}
                        fullWidth
                        label={`Question ${questionId}: ${question.text || ''}`}
                        value={newEntry.answers[questionId] || ''}
                        onChange={(e) => {
                          const updatedAnswers = { ...newEntry.answers };
                          updatedAnswers[questionId] = e.target.value;
                          setNewEntry({ ...newEntry, answers: updatedAnswers });
                        }}
                        margin="normal"
                      />
                    ))
                  )}
                </Paper>
              </Box>
            )}

            <Box>
              <Typography variant="subtitle1" gutterBottom>
                Upload Answer Image (Optional)
              </Typography>
              <Paper
                {...getRootProps()}
                sx={{
                  p: 2,
                  border: '2px dashed',
                  borderColor: 'divider',
                  borderRadius: 1,
                  cursor: 'pointer',
                  textAlign: 'center',
                }}
              >
                <input {...getInputProps()} />
                <Typography>
                  Drag and drop an image of the answer sheet here, or click to select a file
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Supported formats: JPG, PNG
                </Typography>
              </Paper>
            </Box>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAddDialog(false)}>Cancel</Button>
          <Button onClick={handleAddEntry} variant="contained">
            Add Entry
          </Button>
        </DialogActions>
      </Dialog>

      {/* Upload Dialog */}
      <Dialog
        open={showUploadDialog}
        onClose={() => !isUploading && setShowUploadDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Upload Listening Answer Sheet</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <FormControl fullWidth>
              <InputLabel id="upload-material-select-label">Material</InputLabel>
              <Select
                labelId="upload-material-select-label"
                value={uploadMaterialId}
                label="Material"
                onChange={(e) => setUploadMaterialId(e.target.value)}
                disabled={isUploading}
              >
                {materials.map((material) => (
                  <MenuItem key={material.id} value={material.id}>
                    {material.title}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              fullWidth
              label="Student ID"
              value={uploadStudentId}
              onChange={(e) => setUploadStudentId(e.target.value)}
              helperText="Leave blank to use global Student ID"
              disabled={isUploading}
            />

            <Box
              {...getRootProps()}
              sx={{
                border: '2px dashed',
                borderColor: 'divider',
                borderRadius: 1,
                p: 3,
                textAlign: 'center',
                cursor: isUploading ? 'not-allowed' : 'pointer',
                bgcolor: 'background.paper',
                '&:hover': {
                  bgcolor: 'action.hover',
                },
              }}
            >
              <input {...getInputProps()} disabled={isUploading} />
              {isUploading ? (
                <Box>
                  <CircularProgress size={40} sx={{ mb: 2 }} />
                  <Typography>Processing image...</Typography>
                  <LinearProgress variant="determinate" value={uploadProgress} sx={{ mt: 2 }} />
                </Box>
              ) : (
                <Box>
                  <Upload size={40} style={{ opacity: 0.5, marginBottom: 8 }} />
                  <Typography>Drag and drop an answer sheet image here, or click to select a file</Typography>
                  <Typography variant="caption" color="text.secondary">
                    Supported formats: JPEG, PNG
                  </Typography>
                </Box>
              )}
            </Box>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setShowUploadDialog(false)}
            disabled={isUploading}
          >
            Cancel
          </Button>
        </DialogActions>
      </Dialog>

      {/* Result Dialog */}
      <Dialog
        open={showResultDialog}
        onClose={() => setShowResultDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Listening Test Result</DialogTitle>
        <DialogContent>
          {resultDialogEntry && (
            <Stack spacing={3} sx={{ mt: 1 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Student ID</Typography>
                  <Typography variant="body1">{resultDialogEntry.studentId}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Material</Typography>
                  <Typography variant="body1">{resultDialogEntry.materialTitle || resultDialogEntry.materialId}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Score</Typography>
                  <Typography variant="body1">
                    {resultDialogEntry.score !== undefined
                      ? `${resultDialogEntry.score}/${resultDialogEntry.totalQuestions}`
                      : '-'}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Band</Typography>
                  <Typography variant="body1">
                    {resultDialogEntry.band !== undefined ? resultDialogEntry.band.toFixed(1) : '-'}
                  </Typography>
                </Grid>
              </Grid>

              {resultDialogEntry.analysis && (
                <Box>
                  <Typography variant="subtitle1">Analysis</Typography>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="body2">{resultDialogEntry.analysis}</Typography>
                  </Paper>
                </Box>
              )}

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1">Strengths</Typography>
                  <Paper sx={{ p: 2 }}>
                    {resultDialogEntry.strengths && resultDialogEntry.strengths.length > 0 ? (
                      <ul style={{ margin: 0, paddingLeft: 16 }}>
                        {resultDialogEntry.strengths.map((strength, index) => (
                          <li key={index}>
                            <Typography variant="body2">{strength}</Typography>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <Typography variant="body2">No strengths identified</Typography>
                    )}
                  </Paper>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1">Weaknesses</Typography>
                  <Paper sx={{ p: 2 }}>
                    {resultDialogEntry.weaknesses && resultDialogEntry.weaknesses.length > 0 ? (
                      <ul style={{ margin: 0, paddingLeft: 16 }}>
                        {resultDialogEntry.weaknesses.map((weakness, index) => (
                          <li key={index}>
                            <Typography variant="body2">{weakness}</Typography>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <Typography variant="body2">No weaknesses identified</Typography>
                    )}
                  </Paper>
                </Grid>
              </Grid>

              <Box>
                <Typography variant="subtitle1">Improvement Suggestions</Typography>
                <Paper sx={{ p: 2 }}>
                  {resultDialogEntry.improvementSuggestions && resultDialogEntry.improvementSuggestions.length > 0 ? (
                    <ul style={{ margin: 0, paddingLeft: 16 }}>
                      {resultDialogEntry.improvementSuggestions.map((suggestion, index) => (
                        <li key={index}>
                          <Typography variant="body2">{suggestion}</Typography>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <Typography variant="body2">No improvement suggestions available</Typography>
                  )}
                </Paper>
              </Box>

              {resultDialogEntry.mistakes && resultDialogEntry.mistakes.length > 0 && (
                <Box>
                  <Typography variant="subtitle1">Mistakes</Typography>
                  <TableContainer component={Paper}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Question</TableCell>
                          <TableCell>Your Answer</TableCell>
                          <TableCell>Correct Answer</TableCell>
                          <TableCell>Explanation</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {resultDialogEntry.mistakes.map((mistake, index) => (
                          <TableRow key={index}>
                            <TableCell>{mistake.questionNumber}</TableCell>
                            <TableCell>{mistake.userAnswer}</TableCell>
                            <TableCell>{mistake.correctAnswer}</TableCell>
                            <TableCell>{mistake.explanation || 'No explanation available'}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              )}
            </Stack>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowResultDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

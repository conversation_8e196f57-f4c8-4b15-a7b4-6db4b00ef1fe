import { jsPDF } from 'jspdf';

// Define types for jsPDF
type TextOptionsLight = {
  align?: 'left' | 'center' | 'right' | 'justify' | string;
};

type BorderRadius = number | { tl: number; tr: number; bl: number; br: number };

type GStateOptions = {
  opacity?: number;
  [key: string]: any;
};

// Extend the jsPDF module
declare module 'jspdf' {
  // Define GState class constructor
  class GState {
    constructor(options: GStateOptions);
    id: string;
    objectNumber: number;
    [key: string]: any;
  }

  interface jsPDF {
    // Color methods that accept spread arguments
    setTextColor(...args: number[]): jsPDF;
    setFillColor(...args: number[]): jsPDF;
    setDrawColor(...args: number[]): jsPDF;

    // Add polygon method
    polygon(points: number[], pointsY: number[], style: string): jsPDF;

    // Add GState class and method
    GState: typeof GState;
    setGState(gState: GState): jsPDF;

    // Override roundedRect to accept either a number or an object for radius
    roundedRect(
      x: number,
      y: number,
      w: number,
      h: number,
      rx: BorderRadius,
      ry: BorderRadius | number,
      style: string
    ): jsPDF;

    // Text methods
    text(text: string | string[], x: number, y: number, options?: TextOptionsLight | { align: string }): jsPDF;
    getTextWidth(text: string): number;
    splitTextToSize(text: string, maxLength: number): string[];

    // Shape methods
    circle(x: number, y: number, r: number, style: string): jsPDF;
    rect(x: number, y: number, w: number, h: number, style: string): jsPDF;
    line(x1: number, y1: number, x2: number, y2: number): jsPDF;

    // Line style methods
    setLineWidth(width: number): jsPDF;
    setLineDashPattern(pattern: number[], phase: number): jsPDF;

    // Page methods
    addPage(): jsPDF;

    // Font methods
    setFont(fontName: string, fontStyle: string): jsPDF;
    setFontSize(size: number): jsPDF;

    // Add internal properties
    internal: {
      pageSize: {
        width: number;
        height: number;
      };
      [key: string]: any;
    };
  }
}

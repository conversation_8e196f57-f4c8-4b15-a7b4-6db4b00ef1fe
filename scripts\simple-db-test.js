// Simple database test script
const { exec } = require('child_process');

// Function to execute a command and return a promise
function execCommand(command) {
  return new Promise((resolve, reject) => {
    console.log(`Executing: ${command}`);
    
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error: ${error.message}`);
        return reject(error);
      }
      
      if (stderr) {
        console.error(`stderr: ${stderr}`);
      }
      
      console.log(`stdout: ${stdout}`);
      resolve(stdout);
    });
  });
}

async function testDatabase() {
  try {
    // Use Prisma CLI to check the database connection
    await execCommand('npx prisma validate');
    console.log('Database connection validated successfully!');
    return true;
  } catch (error) {
    console.error('Database validation failed:', error);
    return false;
  }
}

// Run the script
testDatabase()
  .then(success => {
    if (success) {
      console.log('Database test completed successfully.');
    } else {
      console.error('Database test failed.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });

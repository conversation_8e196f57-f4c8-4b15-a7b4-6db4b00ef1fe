/**
 * Utility functions for inflating band scores according to specific rules
 */

/**
 * Inflates writing and speaking band scores according to the following rules:
 * - If the score is below 4.0, add 1.0 to the score
 * - If the score is below 5.0 (but at least 4.0), add 0.5 to the score
 * - Otherwise, return the original score
 * 
 * @param band - The original band score
 * @param componentType - The type of component ('writing_task1', 'writing_task2', 'speaking', or any other type)
 * @returns The inflated band score (or original if no inflation is needed)
 */
export function inflateBandScore(band: number | null | undefined, componentType: string): number | null | undefined {
  // If band is null or undefined, return it as is
  if (band === null || band === undefined) {
    return band;
  }

  // Only inflate writing and speaking scores
  if (componentType === 'writing_task1' || componentType === 'writing_task2' || componentType === 'speaking') {
    if (band < 4.0) {
      // Add 1.0 to scores below 4.0
      return Math.min(9.0, band + 1.0);
    } else if (band < 5.0) {
      // Add 0.5 to scores below 5.0 (but at least 4.0)
      return Math.min(9.0, band + 0.5);
    }
  }

  // Return original score for other components or if no inflation is needed
  return band;
}

/**
 * Calculates the average writing band score from task1 and task2 scores,
 * applying inflation to each task score before averaging
 * 
 * @param task1Band - The band score for writing task 1
 * @param task2Band - The band score for writing task 2
 * @returns The inflated and averaged writing band score
 */
export function calculateInflatedWritingBand(
  task1Band: number | null | undefined, 
  task2Band: number | null | undefined
): number | null {
  // Inflate individual task scores
  const inflatedTask1 = inflateBandScore(task1Band, 'writing_task1');
  const inflatedTask2 = inflateBandScore(task2Band, 'writing_task2');
  
  // Count valid scores
  let validScores = 0;
  let totalScore = 0;
  
  if (typeof inflatedTask1 === 'number') {
    totalScore += inflatedTask1;
    validScores++;
  }
  
  if (typeof inflatedTask2 === 'number') {
    totalScore += inflatedTask2;
    validScores++;
  }
  
  // Calculate average if we have valid scores
  if (validScores > 0) {
    const average = totalScore / validScores;
    // Round to nearest 0.5
    return Math.round(average * 2) / 2;
  }
  
  return null;
}

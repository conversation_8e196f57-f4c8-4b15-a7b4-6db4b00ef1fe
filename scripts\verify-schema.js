const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifySchema() {
  try {
    console.log('Verifying database schema...');

    // Try to query each model to verify the schema
    console.log('Checking Student model...');
    await prisma.student.findFirst();
    
    console.log('Checking WritingMaterial model...');
    await prisma.writingMaterial.findFirst();
    
    console.log('Checking WritingEntry model...');
    await prisma.writingEntry.findFirst();
    
    console.log('Checking SpeakingEntry model...');
    await prisma.speakingEntry.findFirst();
    
    console.log('Checking ReadingMaterial model...');
    await prisma.readingMaterial.findFirst();
    
    console.log('Checking ReadingEntry model...');
    await prisma.readingEntry.findFirst();
    
    console.log('Checking ListeningMaterial model...');
    await prisma.listeningMaterial.findFirst();
    
    console.log('Checking ListeningEntry model...');
    await prisma.listeningEntry.findFirst();
    
    console.log('Checking Report model...');
    await prisma.report.findFirst();
    
    console.log('Schema verification completed successfully!');
    console.log('All models are accessible and match the schema definition.');
  } catch (error) {
    console.error('Error during schema verification:', error);
    console.error('The database schema may be out of sync with the Prisma schema.');
    console.error('Try running: npx prisma db push');
  } finally {
    await prisma.$disconnect();
  }
}

verifySchema();

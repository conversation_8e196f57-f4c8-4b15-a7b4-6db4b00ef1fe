'use client';

import * as React from 'react';
import { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Paper,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Stack,
  Divider,
  CircularProgress,
  Alert,
  Snackbar,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip
} from '@mui/material';
import {
  Download as DownloadIcon,
  Folder as FolderIcon,
  Print as PrintIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  Add as AddIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import { config } from '@/config';

// Define types
interface Student {
  id: string;
  name: string | null;
  email: string | null;
}

interface Report {
  id: string;
  studentId: string;
  candidateName: string | null;
  testDate: string | null;
  readingResult: any | null;
  listeningResult: any | null;
  writingResult: any | null;
  speakingResult: any | null;
  overallBand: number | null;
  overallStrengths: string[] | null;
  overallWeaknesses: string[] | null;
  overallImprovements: string[] | null;
  printStatus: string | null;
  folderAssignment: string | null;
  createdAt: string;
}

export default function ReportsPage(): React.JSX.Element {
  // State variables
  const [students, setStudents] = useState<Student[]>([]);
  const [selectedStudentIds, setSelectedStudentIds] = useState<string[]>([]);
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [generating, setGenerating] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [folderName, setFolderName] = useState<string>('');
  const [folders, setFolders] = useState<string[]>(['Folder 1', 'Folder 2', 'Folder 3']); // Example folders

  // State for pagination
  const [page, setPage] = useState<number>(1);
  const [pageSize] = useState<number>(5); // Reduced to 5 students per page
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalCount, setTotalCount] = useState<number>(0);

  // Fetch students and reports on component mount or when page changes
  useEffect(() => {
    fetchStudents();
    fetchReports();
  }, [page]);

  // Fetch students from API with pagination
  const fetchStudents = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/students?paginated=true&page=${page}&pageSize=${pageSize}`);
      if (!response.ok) {
        throw new Error('Failed to fetch students');
      }
      const data = await response.json();
      setStudents(data.students);
      setTotalPages(data.pagination.totalPages);
      setTotalCount(data.pagination.totalCount);
    } catch (error) {
      setError('Error fetching students: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  // Fetch reports from API
  const fetchReports = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/reports?page=${page}&pageSize=${pageSize}`);
      if (!response.ok) {
        throw new Error('Failed to fetch reports');
      }
      const data = await response.json();
      setReports(data.reports || data); // Handle both formats for backward compatibility

      // If pagination data is available, update state
      if (data.pagination) {
        setTotalPages(data.pagination.totalPages);
        setTotalCount(data.pagination.totalCount);
      }
    } catch (error) {
      setError('Error fetching reports: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  // Handle student selection
  const handleStudentSelect = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedStudentIds(event.target.value as string[]);
  };

  // Generate reports for selected students
  const generateReports = async () => {
    if (selectedStudentIds.length === 0) {
      setError('Please select at least one student');
      return;
    }

    setGenerating(true);
    try {
      const response = await fetch('/api/reports/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ studentIds: selectedStudentIds }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate reports');
      }

      const data = await response.json();
      setReports([...reports, ...data.reports]);
      setSuccess(`Successfully generated ${data.reports.length} reports`);

      // Clear selection after successful generation
      setSelectedStudentIds([]);
    } catch (error) {
      setError('Error generating reports: ' + (error as Error).message);
    } finally {
      setGenerating(false);
    }
  };

  // Generate PDF for a report
  const generatePDF = (report: Report) => {
    const doc = new jsPDF();
    let currentY = 15;
    let pageCount = 1;

    // Helper function to check and add a new page if needed
    const checkAndAddPage = (height: number) => {
      if (currentY + height > 280) {
        doc.setFontSize(10);
        doc.text(`Page ${pageCount}`, 105, 290, { align: 'center' });
        doc.addPage();
        pageCount++;
        currentY = 15;
      }
    };

    // Add header
    doc.setFillColor(25, 118, 210); // Primary blue color
    doc.rect(0, 0, 210, 25, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(20);
    doc.setFont('helvetica', 'bold');
    doc.text('IELTS Test Report', 105, 15, { align: 'center' });

    // Add student information
    currentY = 40;
    doc.setTextColor(0, 0, 0);
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Candidate Information', 15, currentY);
    currentY += 10;

    doc.setFont('helvetica', 'normal');
    doc.setFontSize(10);
    doc.text(`Student ID: ${report.studentId}`, 15, currentY);
    currentY += 7;
    doc.text(`Name: ${report.candidateName || 'N/A'}`, 15, currentY);
    currentY += 7;
    doc.text(`Test Date: ${report.testDate ? new Date(report.testDate).toLocaleDateString() : 'N/A'}`, 15, currentY);
    currentY += 7;
    doc.text(`Overall Band Score: ${report.overallBand?.toFixed(1) || 'N/A'}`, 15, currentY);
    currentY += 15;

    // Add section for each test component
    const addTestSection = (title: string, result: any) => {
      if (!result) return;

      checkAndAddPage(60);

      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      doc.text(title, 15, currentY);
      currentY += 10;

      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text(`Band Score: ${result.band?.toFixed(1) || 'N/A'}`, 20, currentY);
      currentY += 10;

      // Strengths
      if (result.strengths && result.strengths.length > 0) {
        doc.setFont('helvetica', 'bold');
        doc.text('Strengths:', 20, currentY);
        currentY += 7;
        doc.setFont('helvetica', 'normal');

        result.strengths.forEach((strength: string) => {
          doc.text(`• ${strength}`, 25, currentY);
          currentY += 7;
        });
        currentY += 3;
      }

      // Weaknesses
      if (result.weaknesses && result.weaknesses.length > 0) {
        checkAndAddPage(7 * result.weaknesses.length + 10);
        doc.setFont('helvetica', 'bold');
        doc.text('Areas for Improvement:', 20, currentY);
        currentY += 7;
        doc.setFont('helvetica', 'normal');

        result.weaknesses.forEach((weakness: string) => {
          doc.text(`• ${weakness}`, 25, currentY);
          currentY += 7;
        });
        currentY += 3;
      }

      // Improvement suggestions
      if (result.improvements && result.improvements.length > 0) {
        checkAndAddPage(7 * result.improvements.length + 10);
        doc.setFont('helvetica', 'bold');
        doc.text('Improvement Suggestions:', 20, currentY);
        currentY += 7;
        doc.setFont('helvetica', 'normal');

        result.improvements.forEach((improvement: string) => {
          doc.text(`• ${improvement}`, 25, currentY);
          currentY += 7;
        });
      }

      currentY += 15;
    };

    // Add each test component section
    if (report.readingResult) addTestSection('Reading Test', report.readingResult);
    if (report.listeningResult) addTestSection('Listening Test', report.listeningResult);
    if (report.writingResult) addTestSection('Writing Test', report.writingResult);
    if (report.speakingResult) addTestSection('Speaking Test', report.speakingResult);

    // Add overall assessment
    checkAndAddPage(60);
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Overall Assessment', 15, currentY);
    currentY += 10;

    // Overall strengths
    if (report.overallStrengths && report.overallStrengths.length > 0) {
      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      doc.text('Overall Strengths:', 20, currentY);
      currentY += 7;
      doc.setFont('helvetica', 'normal');

      report.overallStrengths.forEach((strength: string) => {
        doc.text(`• ${strength}`, 25, currentY);
        currentY += 7;
      });
      currentY += 3;
    }

    // Overall weaknesses
    if (report.overallWeaknesses && report.overallWeaknesses.length > 0) {
      checkAndAddPage(7 * report.overallWeaknesses.length + 10);
      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      doc.text('Overall Areas for Improvement:', 20, currentY);
      currentY += 7;
      doc.setFont('helvetica', 'normal');

      report.overallWeaknesses.forEach((weakness: string) => {
        doc.text(`• ${weakness}`, 25, currentY);
        currentY += 7;
      });
      currentY += 3;
    }

    // Overall improvement suggestions
    if (report.overallImprovements && report.overallImprovements.length > 0) {
      checkAndAddPage(7 * report.overallImprovements.length + 10);
      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      doc.text('Overall Improvement Plan:', 20, currentY);
      currentY += 7;
      doc.setFont('helvetica', 'normal');

      report.overallImprovements.forEach((improvement: string) => {
        doc.text(`• ${improvement}`, 25, currentY);
        currentY += 7;
      });
    }

    // Add footer with page numbers
    const totalPages = doc.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      doc.setPage(i);
      doc.setFontSize(10);
      doc.setTextColor(100, 100, 100);
      doc.text(`Page ${i} of ${totalPages}`, 105, 290, { align: 'center' });
    }

    // Save the PDF
    const fileName = `IELTS_Report_${report.studentId}_${new Date().toISOString().slice(0, 10)}.pdf`;
    doc.save(fileName);

    // Update report status
    updateReportStatus(report.id, 'printed');
  };

  // Update report status (printed/pending)
  const updateReportStatus = async (reportId: string, status: string) => {
    try {
      const response = await fetch(`/api/reports/${reportId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ printStatus: status }),
      });

      if (!response.ok) {
        throw new Error('Failed to update report status');
      }

      // Update local state
      setReports(reports.map(report =>
        report.id === reportId ? { ...report, printStatus: status } : report
      ));
    } catch (error) {
      setError('Error updating report status: ' + (error as Error).message);
    }
  };

  // Assign report to folder
  const assignToFolder = async (reportId: string, folderName: string) => {
    try {
      const response = await fetch(`/api/reports/${reportId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ folderAssignment: folderName }),
      });

      if (!response.ok) {
        throw new Error('Failed to assign report to folder');
      }

      // Update local state
      setReports(reports.map(report =>
        report.id === reportId ? { ...report, folderAssignment: folderName } : report
      ));

      setSuccess(`Report assigned to folder: ${folderName}`);
    } catch (error) {
      setError('Error assigning report to folder: ' + (error as Error).message);
    }
  };

  // Create a new folder
  const createFolder = () => {
    if (!folderName.trim()) {
      setError('Please enter a folder name');
      return;
    }

    if (folders.includes(folderName)) {
      setError('Folder already exists');
      return;
    }

    setFolders([...folders, folderName]);
    setFolderName('');
    setSuccess(`Folder "${folderName}" created successfully`);
  };

  // Filter reports based on search query
  const filteredReports = reports.filter(report =>
    report.studentId.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (report.candidateName && report.candidateName.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Handle error alert close
  const handleErrorClose = () => {
    setError(null);
  };

  // Handle success alert close
  const handleSuccessClose = () => {
    setSuccess(null);
  };

  return (
    <Container maxWidth="xl">
      <Typography variant="h4" sx={{ mb: 4 }}>
        Comprehensive Reports
      </Typography>

      {/* Error and Success Alerts */}
      <Snackbar open={!!error} autoHideDuration={6000} onClose={handleErrorClose}>
        <Alert onClose={handleErrorClose} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>

      <Snackbar open={!!success} autoHideDuration={6000} onClose={handleSuccessClose}>
        <Alert onClose={handleSuccessClose} severity="success" sx={{ width: '100%' }}>
          {success}
        </Alert>
      </Snackbar>

      <Grid container spacing={3}>
        {/* Student Selection and Report Generation */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Generate New Reports
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={8}>
                <FormControl fullWidth>
                  <InputLabel id="student-select-label">Select Students</InputLabel>
                  <Select
                    labelId="student-select-label"
                    multiple
                    value={selectedStudentIds}
                    onChange={handleStudentSelect as any}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {(selected as string[]).map((value) => {
                          const student = students.find(s => s.id === value);
                          return (
                            <Chip
                              key={value}
                              label={student ? (student.name || student.id) : value}
                            />
                          );
                        })}
                      </Box>
                    )}
                  >
                    {students.map((student) => (
                      <MenuItem key={student.id} value={student.id}>
                        {student.name || student.id}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={4}>
                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  onClick={generateReports}
                  disabled={generating || selectedStudentIds.length === 0}
                  startIcon={generating ? <CircularProgress size={20} color="inherit" /> : null}
                >
                  {generating ? 'Generating...' : 'Generate Reports'}
                </Button>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Folder Management */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Folder Management
            </Typography>
            <Box sx={{ mb: 2 }}>
              <TextField
                label="New Folder Name"
                variant="outlined"
                fullWidth
                value={folderName}
                onChange={(e) => setFolderName(e.target.value)}
                sx={{ mb: 1 }}
              />
              <Button
                variant="outlined"
                color="primary"
                onClick={createFolder}
                startIcon={<AddIcon />}
                fullWidth
              >
                Create Folder
              </Button>
            </Box>
            <Divider sx={{ my: 2 }} />
            <Typography variant="subtitle1" sx={{ mb: 1 }}>
              Available Folders
            </Typography>
            <Stack spacing={1}>
              {folders.map((folder) => (
                <Chip
                  key={folder}
                  label={folder}
                  variant="outlined"
                  onDelete={() => {
                    setFolders(folders.filter(f => f !== folder));
                  }}
                />
              ))}
            </Stack>
          </Paper>
        </Grid>

        {/* Reports List */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Generated Reports
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <TextField
                  size="small"
                  placeholder="Search reports..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  InputProps={{
                    startAdornment: <SearchIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />,
                  }}
                />
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<RefreshIcon />}
                  onClick={fetchReports}
                >
                  Refresh
                </Button>
              </Box>
            </Box>

            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : filteredReports.length === 0 ? (
              <Alert severity="info">No reports found</Alert>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Student ID</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Date</TableCell>
                      <TableCell>Overall Band</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Folder</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredReports.map((report) => (
                      <TableRow key={report.id}>
                        <TableCell>{report.studentId}</TableCell>
                        <TableCell>{report.candidateName || 'N/A'}</TableCell>
                        <TableCell>
                          {report.testDate
                            ? new Date(report.testDate).toLocaleDateString()
                            : new Date(report.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell>{report.overallBand?.toFixed(1) || 'N/A'}</TableCell>
                        <TableCell>
                          <Chip
                            label={report.printStatus || 'pending'}
                            color={report.printStatus === 'printed' ? 'success' : 'warning'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {report.folderAssignment ? (
                            <Chip
                              icon={<FolderIcon />}
                              label={report.folderAssignment}
                              size="small"
                              variant="outlined"
                            />
                          ) : (
                            <FormControl size="small" fullWidth>
                              <Select
                                displayEmpty
                                value=""
                                onChange={(e) => assignToFolder(report.id, e.target.value)}
                              >
                                <MenuItem value="" disabled>
                                  <em>Assign</em>
                                </MenuItem>
                                {folders.map((folder) => (
                                  <MenuItem key={folder} value={folder}>
                                    {folder}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          )}
                        </TableCell>
                        <TableCell>
                          <Stack direction="row" spacing={1}>
                            <Tooltip title="Download PDF">
                              <IconButton
                                size="small"
                                color="primary"
                                onClick={() => generatePDF(report)}
                              >
                                <DownloadIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Print Report">
                              <IconButton
                                size="small"
                                color="secondary"
                                onClick={() => {
                                  generatePDF(report);
                                  updateReportStatus(report.id, 'printed');
                                }}
                              >
                                <PrintIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* Pagination Controls */}
            {!loading && filteredReports.length > 0 && (
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3, mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Button
                    variant="outlined"
                    disabled={page <= 1}
                    onClick={() => setPage(page - 1)}
                  >
                    Previous
                  </Button>

                  <Typography variant="body1">
                    Page {page} of {totalPages}
                  </Typography>

                  <Button
                    variant="outlined"
                    disabled={page >= totalPages}
                    onClick={() => setPage(page + 1)}
                  >
                    Next
                  </Button>
                </Box>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
}

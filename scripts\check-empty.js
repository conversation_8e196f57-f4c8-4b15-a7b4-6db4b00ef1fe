const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkEmpty() {
  try {
    console.log('Checking if database is empty...');

    // Check each model for any records
    const studentCount = await prisma.student.count();
    const writingMaterialCount = await prisma.writingMaterial.count();
    const writingEntryCount = await prisma.writingEntry.count();
    const speakingEntryCount = await prisma.speakingEntry.count();
    const readingMaterialCount = await prisma.readingMaterial.count();
    const readingEntryCount = await prisma.readingEntry.count();
    const listeningMaterialCount = await prisma.listeningMaterial.count();
    const listeningEntryCount = await prisma.listeningEntry.count();
    const reportCount = await prisma.report.count();

    console.log('Record counts:');
    console.log(`- Students: ${studentCount}`);
    console.log(`- Writing Materials: ${writingMaterialCount}`);
    console.log(`- Writing Entries: ${writingEntryCount}`);
    console.log(`- Speaking Entries: ${speakingEntryCount}`);
    console.log(`- Reading Materials: ${readingMaterialCount}`);
    console.log(`- Reading Entries: ${readingEntryCount}`);
    console.log(`- Listening Materials: ${listeningMaterialCount}`);
    console.log(`- Listening Entries: ${listeningEntryCount}`);
    console.log(`- Reports: ${reportCount}`);

    const totalCount = 
      studentCount + 
      writingMaterialCount + 
      writingEntryCount + 
      speakingEntryCount + 
      readingMaterialCount + 
      readingEntryCount + 
      listeningMaterialCount + 
      listeningEntryCount + 
      reportCount;

    if (totalCount === 0) {
      console.log('\nDatabase is empty! ✅');
    } else {
      console.log(`\nDatabase is not empty. Total records: ${totalCount}`);
    }
  } catch (error) {
    console.error('Error checking database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkEmpty();

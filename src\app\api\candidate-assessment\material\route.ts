import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    // Get the material ID from the query parameters
    const url = new URL(req.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Material ID is required' },
        { status: 400 }
      );
    }

    // Determine the material type from the query parameters
    const type = url.searchParams.get('type') || 'writing';

    // Fetch the material based on the type
    let material;
    if (type === 'reading') {
      material = await prisma.readingMaterial.findUnique({
        where: { id }
      });
    } else if (type === 'listening') {
      material = await prisma.listeningMaterial.findUnique({
        where: { id }
      });
    } else {
      // Default to writing material
      material = await prisma.writingMaterial.findUnique({
        where: { id }
      });
    }

    if (!material) {
      return NextResponse.json(
        { error: `${type.charAt(0).toUpperCase() + type.slice(1)} material not found` },
        { status: 404 }
      );
    }

    return NextResponse.json(material);
  } catch (error) {
    console.error('Error fetching material:', error);
    return NextResponse.json(
      { error: 'Failed to fetch material', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    // Get the entry ID and type from the query parameters
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    const type = searchParams.get('type');

    if (!id) {
      return NextResponse.json({ error: 'Entry ID is required' }, { status: 400 });
    }

    if (!type) {
      return NextResponse.json({ error: 'Entry type is required' }, { status: 400 });
    }

    let entry;

    // Fetch the entry based on the type
    switch (type) {
      case 'reading':
        entry = await prisma.readingEntry.findUnique({
          where: { id }
        });
        break;

      case 'listening':
        entry = await prisma.listeningEntry.findUnique({
          where: { id }
        });
        break;

      case 'writing_task1':
      case 'writing_task2':
        entry = await prisma.writingEntry.findUnique({
          where: { id },
          include: { material: true } // Keep this for writing entries
        });

        // Add image URLs if available
        if (entry) {
          // For now, we'll just use the imageUrl field if it exists
          // In the future, we might want to handle multiple images
          const entryWithImageUrls = entry as any; // Use type assertion to add the property
          if (entry.imageUrl) {
            entryWithImageUrls.imageUrls = [entry.imageUrl];
          } else {
            entryWithImageUrls.imageUrls = [];
          }
          entry = entryWithImageUrls;
        }
        break;

      case 'speaking':
        entry = await prisma.speakingEntry.findUnique({
          where: { id }
        });
        break;

      default:
        return NextResponse.json({ error: 'Invalid entry type' }, { status: 400 });
    }

    if (!entry) {
      return NextResponse.json({ error: 'Entry not found' }, { status: 404 });
    }

    return NextResponse.json(entry);
  } catch (error) {
    console.error('Error fetching entry:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch entry' },
      { status: 500 }
    );
  }
}

'use client';

import * as React from 'react';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Alert from '@mui/material/Alert';
import Paper from '@mui/material/Paper';
import Divider from '@mui/material/Divider';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import ListItemSecondaryAction from '@mui/material/ListItemSecondaryAction';
import IconButton from '@mui/material/IconButton';
import Chip from '@mui/material/Chip';
import LinearProgress from '@mui/material/LinearProgress';
import { UploadSimple } from '@phosphor-icons/react/dist/ssr/UploadSimple';
import { Download } from '@phosphor-icons/react/dist/ssr/Download';
import { FileText } from '@phosphor-icons/react/dist/ssr/FileText';
import { Image } from '@phosphor-icons/react/dist/ssr/Image';
import { Trash } from '@phosphor-icons/react/dist/ssr/Trash';
import { PenNib } from '@phosphor-icons/react/dist/ssr/PenNib';
// import { Article } from '@phosphor-icons/react/dist/ssr/Article';
import JSZip from 'jszip';

interface ImagePage {
  id: string;
  name: string;
  imageUrl: string;
  order: number;
}

interface Document {
  id: string;
  name: string;
  text: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  taskType: 'Task 1' | 'Task 2';
  pages: ImagePage[];
  error?: string;
  essayGroup?: string; // Group identifier for multi-page essays
  studentId?: string; // Student ID extracted from filename
}

export default function HandwrittenScannerPage(): React.JSX.Element {
  const [documents, setDocuments] = React.useState<Document[]>([]);
  const [loading, setLoading] = React.useState<boolean>(false);
  const [error, setError] = React.useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = React.useState<Document | null>(null);
  const [editedText, setEditedText] = React.useState<string>('');
  const [fileProcessingProgress, setFileProcessingProgress] = React.useState<{[key: string]: number}>({});
  const [essayGroups, setEssayGroups] = React.useState<{[key: string]: string}>({});
  const [showGroupingUI, setShowGroupingUI] = React.useState<boolean>(false);
  const [selectedPages, setSelectedPages] = React.useState<string[]>([]);
  const [reorderMode, setReorderMode] = React.useState<boolean>(false);

  // Generate unique ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // Initialize progress tracking for all files
    const initialProgress: {[key: string]: number} = {};
    Array.from(files).forEach(file => {
      initialProgress[file.name] = 0;
    });
    setFileProcessingProgress(initialProgress);
    setLoading(true);
    setError(null);

    // Process each file as a page
    for (const file of Array.from(files)) {
      const fileId = generateId();
      const fileName = file.name;
      const fileExt = fileName.split('.').pop()?.toLowerCase() || '';

      // Only process image files
      if (!['jpg', 'jpeg', 'png', 'bmp', 'tiff'].includes(fileExt)) {
        setError(`File ${fileName} is not a supported image format. Only JPG, PNG, BMP, and TIFF are supported.`);
        continue;
      }

      // Extract student ID and task information from filename
      // Expected format: "[student ID] task [task number].[page number]" or "[student ID] task [task number].[task part].[page number]"
      // Examples: "75 task 1", "89 task 2.1", "115 task 1.2", "75 task 1.1", "75 task 1.2"

      // First try to match the format with task number and page number
      let fileNameInfo = fileName.match(/^(\d+)\s*task\s*(\d+)(?:\.(\d+))?/i);

      let studentId = '';
      let taskNumber = '';
      let pageNumber = '1';

      if (fileNameInfo) {
        studentId = fileNameInfo[1]; // Student ID
        taskNumber = fileNameInfo[2]; // Task number
        if (fileNameInfo[3]) {
          pageNumber = fileNameInfo[3]; // Page number if available
        }

        // Check if this might be a format like "75 task 1.1" where 1.1 is task part 1, page 1
        // In this case, we want to treat all parts of the same task as one document
        if (taskNumber === '1' && pageNumber !== '1') {
          // This might be a task part rather than a page number
          // For example, "75 task 1.1" and "75 task 1.2" should be treated as parts of Task 1
          // We'll keep the task number as 1 but remember this is part 1.1, 1.2, etc.
        }
      }

      // Determine the task type based on the extracted task number
      // If task number is 1, it's Task 1, otherwise it's Task 2
      const taskType = taskNumber === '1' ? 'Task 1' : 'Task 2';

      try {
        setFileProcessingProgress(prev => ({...prev, [fileName]: 10}));
        const imageUrl = URL.createObjectURL(file);

        // Create a new page
        const newPage: ImagePage = {
          id: generateId(),
          name: fileName,
          imageUrl: imageUrl,
          order: 1 // Default order, will be updated when grouping
        };

        // For student essay format, we want to group by student ID and task type
        // Create a document name that includes student ID and task type
        let baseName = '';

        if (studentId) {
          // Always use a consistent name format for the same student and task type
          baseName = `Student ${studentId} - ${taskType}`;

          // If this is a task with parts (like 1.1, 1.2), we'll add that indicator later
          // when we know all the files that belong to this document
        } else {
          baseName = fileName.replace(/\.[^/.]+$/, '');
        }

        // Extract page number from filename if available
        let pageNum = 1;

        // Check for task part format (e.g., "007 task 1.1", "007 task 1.2")
        const taskPartMatch = fileName.match(/task\s*\d+\.?(\d+)?/i);
        if (taskPartMatch && taskPartMatch[1]) {
          // If we have a part number like 1.1, 1.2, use that as the page number
          pageNum = parseInt(taskPartMatch[1]);
          newPage.order = pageNum; // Set the page order based on the part number
        } else if (pageNumber) {
          // Otherwise use the explicit page number if provided
          pageNum = parseInt(pageNumber);
          newPage.order = pageNum;
        } else {
          // Look for page numbers in the filename (e.g., "page 1", "p2")
          const pageMatch = fileName.match(/(?:page|p)[\s-]*(\d+)/i);
          if (pageMatch && pageMatch[1]) {
            pageNum = parseInt(pageMatch[1]);
            newPage.order = pageNum;
          } else {
            // Default to order 1 if no page number found
            newPage.order = 1;
          }
        }

        // Look for existing documents with the same student ID and task type
        // We want to group all files from the same student and same task type together
        const existingDoc = documents.find(doc => {
          // Always group by student ID and task type, regardless of part numbers
          return doc.studentId === studentId && doc.taskType === taskType;
        });

        if (existingDoc) {
          // Add this page to the existing document
          const updatedPages = [...existingDoc.pages, newPage];

          // Sort pages by their order property which we set based on part numbers
          updatedPages.sort((a, b) => {
            // Use the order property we set earlier
            return a.order - b.order;
          });

          // Update page orders
          updatedPages.forEach((page, index) => {
            page.order = index + 1;
          });

          // For now, don't process the text yet - we'll do that when the user clicks "Process Essay"
          // Check if we need to update the document name to indicate multiple parts
          let updatedName = existingDoc.name;

          // Check if any of the pages have part numbers in their filenames
          const hasParts = updatedPages.some(page => {
            const partMatch = page.name.match(/task\s*\d+\.(\d+)/i);
            return partMatch && partMatch[1];
          });

          // If this document has multiple pages, always update the name to reflect that
          if (updatedPages.length > 1) {
            // First, remove any existing parts indicator
            const baseNameWithoutParts = updatedName.replace(/\s*\(Parts.*\)/, '');

            // Then add the appropriate indicator
            if (hasParts) {
              updatedName = `${baseNameWithoutParts} (Parts 1.x)`;
            } else {
              updatedName = `${baseNameWithoutParts} (${updatedPages.length} pages)`;
            }
          }

          setDocuments(prev =>
            prev.map(doc =>
              doc.id === existingDoc.id
                ? {
                    ...doc,
                    name: updatedName,
                    pages: updatedPages,
                    status: 'pending' // Mark as pending until processed
                  }
                : doc
            )
          );
        } else {
          // Create a new document with this page
          const newDoc: Document = {
            id: fileId,
            name: baseName,
            text: "Upload complete. Click 'Process Essay' to extract text.",
            status: 'pending',
            taskType: taskType,
            pages: [newPage],
            studentId: studentId // Store the student ID
          };

          setDocuments(prev => [...prev, newDoc]);
        }

        setFileProcessingProgress(prev => ({...prev, [fileName]: 100}));
      } catch (error) {
        console.error(`Error processing file ${fileName}:`, error);
        setError(`Error processing ${fileName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Clear the input and finish loading
    event.target.value = '';
    setLoading(false);
  };

  const handleSelectDocument = (doc: Document) => {
    setSelectedDocument(doc);
    setEditedText(doc.text);
  };

  const processEssay = async (docId: string) => {
    // Find the document
    const doc = documents.find(d => d.id === docId);
    if (!doc || !doc.pages || doc.pages.length === 0) {
      setError('No pages found for this document');
      return;
    }

    // Update status to processing
    setDocuments(prev =>
      prev.map(d =>
        d.id === docId
          ? { ...d, status: 'processing', text: 'Processing all pages...' }
          : d
      )
    );

    try {
      // Sort pages by order
      const sortedPages = [...doc.pages].sort((a, b) => a.order - b.order);

      console.log('Processing pages in order:', sortedPages.map(p => p.name));

      // Process each page and collect the text
      let allPageTexts: string[] = [];

      for (let i = 0; i < sortedPages.length; i++) {
        const page = sortedPages[i];
        const pageNum = i + 1;
        const totalPages = sortedPages.length;

        // Update progress
        setDocuments(prev =>
          prev.map(d =>
            d.id === docId
              ? { ...d, text: `Processing page ${pageNum} of ${totalPages}...` }
              : d
          )
        );

        // Get the image file from the page URL
        const response = await fetch(page.imageUrl);
        const blob = await response.blob();
        const file = new File([blob], page.name, { type: blob.type });

        // Create FormData with image and task type
        const formData = new FormData();
        formData.append('image', file);
        formData.append('taskType', doc.taskType);
        formData.append('pageNumber', pageNum.toString());
        formData.append('totalPages', totalPages.toString());

        // Call our API endpoint
        const apiResponse = await fetch('/api/image-to-text', {
          method: 'POST',
          body: formData
        });

        if (!apiResponse.ok) {
          const errorData = await apiResponse.json();
          throw new Error(errorData.error || `Failed to process page ${pageNum}`);
        }

        const data = await apiResponse.json();

        // Store the text from this page with its page number
        console.log(`Processing page ${pageNum}/${totalPages}:`, {
          name: page.name,
          order: page.order,
          pageNum,
          textLength: data.text.length,
          textPreview: data.text.substring(0, 50)
        });

        // Store the text from this page
        allPageTexts.push(data.text);
      }

      // Combine all page texts with appropriate formatting
      let combinedText = '';

      // Process the texts from all pages
      if (allPageTexts.length > 0) {
        // Always include the first page
        combinedText = allPageTexts[0];

        console.log('Page 1 text:', allPageTexts[0].substring(0, 100) + '...');

        // Add the rest of the pages with appropriate separators
        for (let i = 1; i < allPageTexts.length; i++) {
          const pageText = allPageTexts[i].trim();

          console.log(`Page ${i+1} text:`, pageText.substring(0, 100) + '...');

          // We're always adding a separator between pages for clarity
          // No need to check for sentence boundaries

          // Always add a separator for clarity
          combinedText += `\n\n----- Page ${i+1} -----\n\n${pageText}`;
        }
      }

      console.log('Final combined text:', combinedText.substring(0, 100) + '...');

      // Update the document with the combined text and update the name to show it's completed
      setDocuments(prev =>
        prev.map(d => {
          if (d.id === docId) {
            // Update the document name to indicate it's been processed
            let updatedName = d.name;
            if (updatedName.includes('(Multiple Pages)')) {
              // Keep the multiple pages indicator
              updatedName = updatedName.replace('(Multiple Pages)', '(Multiple Pages - Processed)');
            } else if (d.pages && d.pages.length > 1) {
              // Add the multiple pages indicator if it's not already there
              updatedName = `${updatedName} (${d.pages.length} pages - Processed)`;
            }

            return {
              ...d,
              text: combinedText.trim(),
              status: 'completed',
              name: updatedName
            };
          }
          return d;
        })
      );

      // If this is the selected document, update the edited text
      if (selectedDocument?.id === docId) {
        setEditedText(combinedText.trim());
      }

    } catch (error) {
      console.error('Error processing essay:', error);

      // Update the document with the error
      setDocuments(prev =>
        prev.map(d =>
          d.id === docId
            ? {
                ...d,
                text: 'Error processing essay. Please try again.',
                status: 'error',
                error: error instanceof Error ? error.message : 'Unknown error'
              }
            : d
        )
      );
    }
  };

  const handleUpdateText = () => {
    if (!selectedDocument) return;

    setDocuments(prev =>
      prev.map(doc =>
        doc.id === selectedDocument.id
          ? { ...doc, text: editedText }
          : doc
      )
    );

    setSelectedDocument(prev => prev ? { ...prev, text: editedText } : null);
  };

  const handleRemoveDocument = (id: string) => {
    // If removing the selected document, clear selection
    if (selectedDocument?.id === id) {
      setSelectedDocument(null);
      setEditedText('');
    }

    // If the document has image URLs, revoke them to free memory
    const docToRemove = documents.find(doc => doc.id === id);
    if (docToRemove?.pages) {
      docToRemove.pages.forEach(page => {
        if (page.imageUrl) {
          URL.revokeObjectURL(page.imageUrl);
        }
      });
    }

    // Remove the document from the list
    setDocuments(prev => prev.filter(doc => doc.id !== id));
  };

  const handleClearAll = () => {
    // Revoke all object URLs to prevent memory leaks
    documents.forEach(doc => {
      if (doc.pages) {
        doc.pages.forEach(page => {
          if (page.imageUrl) {
            URL.revokeObjectURL(page.imageUrl);
          }
        });
      }
    });

    setDocuments([]);
    setSelectedDocument(null);
    setEditedText('');
  };

  const downloadAsTxt = (docId?: string) => {
    // If docId is provided, download only that document
    // Otherwise, download the selected document
    const docToDownload = docId
      ? documents.find(doc => doc.id === docId)
      : selectedDocument;

    if (!docToDownload) {
      setError('No document selected for download');
      return;
    }

    // Format the text with proper headers and metadata
    const studentInfo = docToDownload.studentId ? `Student ID: ${docToDownload.studentId}\n` : '';
    const pageInfo = docToDownload.pages && docToDownload.pages.length > 1 ? `Pages: ${docToDownload.pages.length}\n` : '';
    const formattedText = `${studentInfo}Task: ${docToDownload.taskType}\nDate: ${new Date().toISOString().slice(0, 10)}\n${pageInfo}\n${docToDownload.text}`;

    const blob = new Blob([formattedText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;

    // Create a more descriptive filename
    const fileName = docToDownload.studentId ?
      `${docToDownload.studentId} ${docToDownload.taskType.replace(' ', '')}.txt` :
      `${docToDownload.name.replace(/\s+/g, '_')}_${docToDownload.taskType.replace(' ', '')}.txt`;

    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const downloadAllAsTxt = () => {
    const completedDocs = documents.filter(doc => doc.status === 'completed');

    if (completedDocs.length === 0) {
      setError('No completed documents to download');
      return;
    }

    // If there's only one document, just download it directly
    if (completedDocs.length === 1) {
      downloadAsTxt(completedDocs[0].id);
      return;
    }

    // Group documents by student ID
    const docsByStudent: { [key: string]: Document[] } = {};
    completedDocs.forEach(doc => {
      const studentId = doc.studentId || 'unknown';
      if (!docsByStudent[studentId]) {
        docsByStudent[studentId] = [];
      }
      docsByStudent[studentId].push(doc);
    });

    // Create a zip file with individual text files for each student and task
    const zip = new JSZip();

    // Add each document as a separate file in the zip
    Object.keys(docsByStudent).forEach(studentId => {
      docsByStudent[studentId].forEach(doc => {
        // Extract task number from the task type (e.g., "Task 1" -> "1")
        const taskNumber = doc.taskType.match(/\d+/)?.[0] || '';

        // Create a more descriptive filename
        // Format: "75 Task 1.txt" or "75 Task 2.txt"
        const fileName = `${studentId} Task ${taskNumber}.txt`;

        console.log('Creating file:', fileName);

        // Make sure we include the full text content
        // Format the text with proper headers and metadata
        const pageInfo = doc.pages && doc.pages.length > 1 ? `Pages: ${doc.pages.length}\n` : '';
        const formattedText = `Student ID: ${studentId}\nTask: ${doc.taskType}\nDate: ${new Date().toISOString().slice(0, 10)}\n${pageInfo}\n${doc.text}`;

        // Add the file to the zip
        zip.file(fileName, formattedText);
      });
    });

    // Generate the zip file and trigger download
    zip.generateAsync({ type: 'blob' }).then(content => {
      const url = URL.createObjectURL(content);
      const link = document.createElement('a');
      link.href = url;
      link.download = `IELTS_Writing_Tasks_${new Date().toISOString().slice(0, 10)}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }).catch(error => {
      console.error('Error creating zip file:', error);
      setError('Failed to create zip file. Downloading as single text file instead.');

      // Fallback to single text file if zip creation fails
      const combinedText = completedDocs.map(doc => {
        const studentInfo = doc.studentId ? `Student ID: ${doc.studentId}` : '';
        return `===== ${doc.name} (${doc.taskType}) ${studentInfo} =====\n\n${doc.text}\n\n`;
      }).join('\n');

      const blob = new Blob([combinedText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      // Get unique student IDs
      const studentIdSet = new Set<string>();
      completedDocs.forEach(doc => {
        if (doc.studentId) {
          studentIdSet.add(doc.studentId);
        }
      });
      const studentIds = Array.from(studentIdSet).join('-');
      const studentPrefix = studentIds ? `Students_${studentIds}_` : '';
      link.download = `${studentPrefix}IELTS_Writing_Tasks_${new Date().toISOString().slice(0, 10)}.txt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    });
  };

  const handleToggleTaskType = (id: string) => {
    setDocuments(prev =>
      prev.map(doc =>
        doc.id === id
          ? {
              ...doc,
              taskType: doc.taskType === 'Task 1' ? 'Task 2' : 'Task 1'
            }
          : doc
      )
    );

    // If this is the selected document, update it
    if (selectedDocument?.id === id) {
      setSelectedDocument(prev =>
        prev ? {
          ...prev,
          taskType: prev.taskType === 'Task 1' ? 'Task 2' : 'Task 1'
        } : null
      );
    }
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'pending':
        return <Chip size="small" label="Pending" color="default" />;
      case 'processing':
        return <Chip size="small" label="Processing" color="info" />;
      case 'completed':
        return <Chip size="small" label="Completed" color="success" />;
      case 'error':
        return <Chip size="small" label="Error" color="error" />;
      default:
        return null;
    }
  };

  return (
    <>
      <Box component="main" sx={{ flexGrow: 1, py: 8 }}>
        <Grid container spacing={3} sx={{ px: 3 }}>
          <Grid item xs={12}>
            <Typography variant="h4">
              IELTS Handwritten Scanner
            </Typography>
            <Typography color="text.secondary" sx={{ mt: 1 }}>
              Convert handwritten IELTS answers into text files using Claude 3.5 Sonnet
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
              Supports automatic student ID detection from filenames like "75 task 1.jpg" or "89 task 2.1.jpg"
            </Typography>
          </Grid>

          {error && (
            <Grid item xs={12}>
              <Alert severity="error" onClose={() => setError(null)}>
                {error}
              </Alert>
            </Grid>
          )}

          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
                  <Button
                    variant="contained"
                    startIcon={<UploadSimple />}
                    component="label"
                    disabled={loading}
                    sx={{ maxWidth: '350px' }}
                  >
                    Upload Images
                    <input
                      type="file"
                      multiple
                      accept=".jpg,.jpeg,.png,.bmp,.tiff"
                      hidden
                      onChange={handleFileUpload}
                    />
                    <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
                      Multiple images will be combined into one essay. <br />
                      Name format: "[student ID] task [task number].[page number].jpg"
                    </Typography>
                  </Button>

                  <Button
                    variant="outlined"
                    startIcon={<Download />}
                    onClick={downloadAllAsTxt}
                    disabled={documents.filter(doc => doc.status === 'completed').length === 0}
                  >
                    Download All as TXT
                  </Button>

                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<Trash />}
                    onClick={handleClearAll}
                    disabled={documents.length === 0}
                  >
                    Clear All
                  </Button>

                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<PenNib />}
                    onClick={() => {
                      // Process all pending essays
                      const pendingDocs = documents.filter(doc => doc.status === 'pending');
                      if (pendingDocs.length === 0) {
                        setError('No pending documents to process');
                        return;
                      }

                      // Process each pending document
                      pendingDocs.forEach(doc => {
                        processEssay(doc.id);
                      });
                    }}
                    disabled={documents.filter(doc => doc.status === 'pending').length === 0}
                  >
                    Process Essays
                  </Button>
                </Stack>

                {loading && (
                  <Box sx={{ mb: 2 }}>
                    <LinearProgress />
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                      Processing files with Claude 3.5 Sonnet...
                    </Typography>
                  </Box>
                )}

                <Typography variant="h6" sx={{ mb: 2 }}>
                  Uploaded Documents
                </Typography>

                {documents.length === 0 ? (
                  <Paper
                    sx={{
                      p: 3,
                      textAlign: 'center',
                      backgroundColor: 'background.default'
                    }}
                  >
                    <Image size={48} weight="thin" />
                    <Typography variant="body1" sx={{ mt: 1 }}>
                      No documents uploaded yet
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                      Upload images of handwritten IELTS writing tasks<br />
                      For automatic organization, use filenames like "75 task 1.jpg" or "89 task 2.1.jpg"
                    </Typography>
                  </Paper>
                ) : (
                  <Paper variant="outlined" sx={{ height: '300px', overflow: 'auto' }}>
                    <List>
                      {documents.map((doc) => (
                        <React.Fragment key={doc.id}>
                          <ListItem
                            sx={{
                              backgroundColor: selectedDocument?.id === doc.id ? 'rgba(25, 118, 210, 0.08)' : 'transparent',
                              '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' },
                              cursor: 'pointer'
                            }}
                            onClick={() => handleSelectDocument(doc)}
                          >
                            <ListItemText
                              primary={
                                <Stack direction="row" spacing={1} alignItems="center">
                                  <Typography variant="body2" fontWeight={500}>
                                    {doc.name}
                                  </Typography>
                                  {getStatusChip(doc.status)}
                                  <Chip
                                    size="small"
                                    label={doc.taskType}
                                    color={doc.taskType === 'Task 1' ? 'primary' : 'secondary'}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleToggleTaskType(doc.id);
                                    }}
                                  />
                                  {doc.studentId && (
                                    <Chip
                                      size="small"
                                      label={`Student: ${doc.studentId}`}
                                      color="success"
                                      variant="outlined"
                                    />
                                  )}
                                  {doc.pages && doc.pages.length > 1 && (
                                    <Chip
                                      size="small"
                                      label={`${doc.pages.length} pages`}
                                      color="info"
                                      variant="outlined"
                                    />
                                  )}
                                  <Typography variant="body2" color="text.secondary">
                                    {doc.pages && doc.pages.length > 0 ?
                                      // Show the actual page number for this document
                                      // If it's a multi-page document, show the range
                                      doc.pages.length > 1 ?
                                        `Pages: 1-${doc.pages.length}` :
                                        `Page: ${doc.pages[0].order}`
                                    : 'Page: 1'}
                                  </Typography>
                                </Stack>
                              }
                              secondary={
                                doc.status === 'processing' ? (
                                  <Box sx={{ mt: 1 }}>
                                    <Typography variant="body2" color="text.secondary">
                                      {doc.text}
                                    </Typography>
                                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                                      <Box sx={{ width: '100%', mr: 1 }}>
                                        <LinearProgress />
                                      </Box>
                                    </Box>
                                  </Box>
                                ) : doc.status === 'error' ? (
                                  <Typography variant="body2" color="error">
                                    {doc.error || 'Error processing document'}
                                  </Typography>
                                ) : (
                                  <Box>
                                    {doc.pages && doc.pages.length > 0 && (
                                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                        {doc.pages.length} {doc.pages.length === 1 ? 'page' : 'pages'}
                                      </Typography>
                                    )}
                                    <Typography variant="body2" color="text.secondary" noWrap>
                                      {doc.text.substring(0, 60)}...
                                    </Typography>
                                  </Box>
                                )
                              }
                            />
                            <ListItemSecondaryAction>
                              {/* Removed individual Process Essay buttons */}
                              {doc.status === 'completed' && (
                                <IconButton
                                  edge="end"
                                  aria-label="download"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    downloadAsTxt(doc.id);
                                  }}
                                >
                                  <FileText />
                                </IconButton>
                              )}
                              <IconButton
                                edge="end"
                                aria-label="delete"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRemoveDocument(doc.id);
                                }}
                                disabled={doc.status === 'processing'}
                              >
                                <Trash />
                              </IconButton>
                            </ListItemSecondaryAction>
                          </ListItem>
                          <Divider />
                        </React.Fragment>
                      ))}
                    </List>
                  </Paper>
                )}
              </CardContent>
            </Card>
          </Grid>

          {selectedDocument && (
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 2 }}>
                    <Typography variant="h6">
                      {selectedDocument.name} ({selectedDocument.taskType})
                    </Typography>
                    <Button
                      variant="contained"
                      size="small"
                      startIcon={<Download />}
                      onClick={() => downloadAsTxt()}
                      disabled={selectedDocument.status !== 'completed'}
                    >
                      Download as TXT
                    </Button>
                  </Stack>

                  <Grid container spacing={3}>
                    {selectedDocument.pages && selectedDocument.pages.length > 0 && (
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle1" gutterBottom>
                          {selectedDocument.pages.length > 1 ? 'Original Images:' : 'Original Image:'}
                        </Typography>
                        <Paper
                          variant="outlined"
                          sx={{ p: 1, height: '400px', overflow: 'auto' }}
                        >
                          {selectedDocument.pages.map((page, index) => (
                            <Box key={page.id} sx={{ mb: 2 }}>
                              {selectedDocument.pages.length > 1 && (
                                <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 0.5 }}>
                                  <Typography variant="caption" color="text.secondary">
                                    Page {index + 1}: {page.name}
                                  </Typography>
                                  <Chip
                                    size="small"
                                    label={`Order: ${page.order}`}
                                    color="primary"
                                    variant="outlined"
                                  />
                                </Stack>
                              )}
                              <Box
                                component="img"
                                src={page.imageUrl}
                                alt={page.name}
                                sx={{
                                  maxWidth: '100%',
                                  maxHeight: '380px',
                                  display: 'block',
                                  margin: '0 auto',
                                  border: '1px solid #eee'
                                }}
                              />
                            </Box>
                          ))}
                        </Paper>
                      </Grid>
                    )}

                    <Grid item xs={12} md={(selectedDocument.pages && selectedDocument.pages.length > 0) ? 6 : 12}>
                      <Typography variant="subtitle1" gutterBottom>
                        Extracted Text:
                      </Typography>
                      <TextField
                        fullWidth
                        multiline
                        rows={16}
                        value={editedText}
                        onChange={(e) => setEditedText(e.target.value)}
                        variant="outlined"
                        disabled={selectedDocument.status !== 'completed'}
                      />

                      <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
                        <Button
                          variant="contained"
                          onClick={handleUpdateText}
                          disabled={
                            selectedDocument.status !== 'completed' ||
                            editedText === selectedDocument.text
                          }
                        >
                          Update Text
                        </Button>

                        {selectedDocument.pages && selectedDocument.pages.length > 1 && (
                          <Button
                            variant="outlined"
                            onClick={() => setReorderMode(!reorderMode)}
                            color="secondary"
                          >
                            {reorderMode ? 'Done Reordering' : 'Reorder Pages'}
                          </Button>
                        )}
                      </Stack>

                      {reorderMode && selectedDocument.pages && selectedDocument.pages.length > 1 && (
                        <Box sx={{ mt: 2, p: 2, border: '1px dashed', borderColor: 'divider', borderRadius: 1 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Reorder Pages
                          </Typography>
                          <Typography variant="caption" color="text.secondary" paragraph>
                            Drag and drop to reorder pages or manually set the order for each page.
                          </Typography>

                          <Stack spacing={1}>
                            {selectedDocument.pages.map((page, index) => (
                              <Stack
                                key={page.id}
                                direction="row"
                                spacing={2}
                                alignItems="center"
                                sx={{
                                  p: 1,
                                  border: '1px solid',
                                  borderColor: 'divider',
                                  borderRadius: 1,
                                  bgcolor: 'background.paper'
                                }}
                              >
                                <Typography variant="body2" sx={{ flex: 1 }} noWrap>
                                  {page.name}
                                </Typography>
                                <TextField
                                  type="number"
                                  label="Order"
                                  size="small"
                                  value={page.order}
                                  InputProps={{ inputProps: { min: 1, max: selectedDocument.pages.length } }}
                                  onChange={(e) => {
                                    const newOrder = parseInt(e.target.value);
                                    if (isNaN(newOrder) || newOrder < 1 || newOrder > selectedDocument.pages.length) return;

                                    // Update the order of this page
                                    const updatedPages = [...selectedDocument.pages];
                                    updatedPages[index].order = newOrder;

                                    // Sort pages by order
                                    updatedPages.sort((a, b) => a.order - b.order);

                                    // Update document
                                    setDocuments(prev =>
                                      prev.map(doc =>
                                        doc.id === selectedDocument.id
                                          ? { ...doc, pages: updatedPages }
                                          : doc
                                      )
                                    );

                                    // Update selected document
                                    setSelectedDocument(prev =>
                                      prev ? { ...prev, pages: updatedPages } : null
                                    );
                                  }}
                                  sx={{ width: 100 }}
                                />
                              </Stack>
                            ))}
                          </Stack>

                          <Button
                            variant="contained"
                            color="primary"
                            sx={{ mt: 2 }}
                            onClick={() => {
                              // Apply the current order and process the essay again
                              if (selectedDocument) {
                                processEssay(selectedDocument.id);
                                setReorderMode(false);
                              }
                            }}
                          >
                            Apply Order & Process Essay
                          </Button>
                        </Box>
                      )}
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>
      </Box>
    </>
  );
}
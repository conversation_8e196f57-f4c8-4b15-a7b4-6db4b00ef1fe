import { prisma } from './db';

/**
 * Compare user answers with correct answers for reading test
 *
 * @param userAnswers - The user's answers
 * @param correctAnswers - The correct answers
 * @returns Object with score, band, and mistakes
 */
export function compareReadingAnswers(userAnswers: string[], correctAnswers: string[]) {
  console.log('Original user answers:', userAnswers);
  console.log('Original correct answers:', correctAnswers);

  // Normalize answers to uppercase for case-insensitive comparison
  // Also handle arrays, objects, and strings properly
  const normalizedUserAnswers = Array.isArray(userAnswers)
    ? userAnswers.map(a => (typeof a === 'string' ? a.trim().toUpperCase() : ''))
    : typeof userAnswers === 'object' && userAnswers !== null
      ? Object.values(userAnswers).map(a => (typeof a === 'string' ? a.trim().toUpperCase() : ''))
      : [];

  const normalizedCorrectAnswers = Array.isArray(correctAnswers)
    ? correctAnswers.map(a => (typeof a === 'string' ? a.trim().toUpperCase() : ''))
    : typeof correctAnswers === 'object' && correctAnswers !== null
      ? Object.values(correctAnswers).map(a => (typeof a === 'string' ? a.trim().toUpperCase() : ''))
      : [];

  console.log('Normalized user answers:', normalizedUserAnswers);
  console.log('Normalized correct answers:', normalizedCorrectAnswers);

  // Count correct answers
  let correctCount = 0;
  const mistakes = [];

  // Compare each answer
  for (let i = 0; i < normalizedCorrectAnswers.length; i++) {
    const userAnswer = normalizedUserAnswers[i] || '';
    const correctAnswer = normalizedCorrectAnswers[i] || '';

    console.log(`Comparing answer ${i+1}: User "${userAnswer}" vs Correct "${correctAnswer}"`);

    // Perform a more flexible comparison
    // 1. Exact match (after normalization)
    // 2. Match ignoring spaces
    // 3. Match ignoring punctuation
    const userAnswerNoSpaces = userAnswer.replace(/\s+/g, '');
    const correctAnswerNoSpaces = correctAnswer.replace(/\s+/g, '');
    const userAnswerNoPunct = userAnswer.replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '');
    const correctAnswerNoPunct = correctAnswer.replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '');

    // Handle special cases for True/False/Not Given
    let isCorrect = false;

    // Check for True/T/TRUE variations
    if ((correctAnswer === 'TRUE' || correctAnswer === 'T') &&
        (userAnswer === 'TRUE' || userAnswer === 'T')) {
      console.log(`Answer ${i+1} is correct! (TRUE/T match)`);
      isCorrect = true;
    }
    // Check for False/F/FALSE variations
    else if ((correctAnswer === 'FALSE' || correctAnswer === 'F') &&
             (userAnswer === 'FALSE' || userAnswer === 'F')) {
      console.log(`Answer ${i+1} is correct! (FALSE/F match)`);
      isCorrect = true;
    }
    // Check for Not Given/NG/NOT GIVEN variations
    else if ((correctAnswer === 'NOT GIVEN' || correctAnswer === 'NG' || correctAnswer === 'NOTGIVEN') &&
             (userAnswer === 'NOT GIVEN' || userAnswer === 'NG' || userAnswer === 'NOTGIVEN')) {
      console.log(`Answer ${i+1} is correct! (NOT GIVEN/NG match)`);
      isCorrect = true;
    }
    // Standard flexible comparison for other answers
    else if (
      userAnswer === correctAnswer ||
      userAnswerNoSpaces === correctAnswerNoSpaces ||
      userAnswerNoPunct === correctAnswerNoPunct
    ) {
      console.log(`Answer ${i+1} is correct!`);
      isCorrect = true;
    }

    if (isCorrect) {
      correctCount++;
    } else {
      console.log(`Answer ${i+1} is incorrect!`);
      console.log(`  - User answer: "${userAnswer}"`);
      console.log(`  - Correct answer: "${correctAnswer}"`);
      console.log(`  - User answer (no spaces): "${userAnswerNoSpaces}"`);
      console.log(`  - Correct answer (no spaces): "${correctAnswerNoSpaces}"`);

      mistakes.push({
        questionNumber: i + 1,
        userAnswer: userAnswers[i] || '',
        correctAnswer: correctAnswers[i] || '',
        explanation: `The correct answer is ${correctAnswers[i]}.`,
        advice: 'Review this question type carefully.'
      });
    }
  }

  // Calculate score and band
  const totalQuestions = normalizedCorrectAnswers.length;
  const score = totalQuestions > 0 ? Math.round((correctCount / totalQuestions) * 40) : 0;

  // Calculate band score based on IELTS reading band score conversion table
  let band = 0;
  if (score >= 39) band = 9.0;
  else if (score >= 37) band = 8.5;
  else if (score >= 35) band = 8.0;
  else if (score >= 33) band = 7.5;
  else if (score >= 30) band = 7.0;
  else if (score >= 27) band = 6.5;
  else if (score >= 23) band = 6.0;
  else if (score >= 20) band = 5.5;
  else if (score >= 16) band = 5.0;
  else if (score >= 13) band = 4.5;
  else if (score >= 10) band = 4.0;
  else if (score >= 8) band = 3.5;
  else if (score >= 6) band = 3.0;
  else if (score >= 4) band = 2.5;
  else if (score >= 2) band = 2.0;
  else band = 1.0;

  return {
    score,
    band,
    correctAnswers: correctCount,
    totalQuestions,
    mistakes
  };
}

/**
 * Process a reading entry with a simplified approach
 *
 * @param entry - The reading entry to process
 * @returns The processed reading entry
 */
export async function processReadingEntrySimple(entry: any) {
  try {
    console.log('Processing reading entry with simplified approach:', entry.id);
    console.log('Entry data:', JSON.stringify(entry, null, 2));

    // Get the user's answers and correct answers
    let userAnswers: string[] = [];
    if (Array.isArray(entry.answers)) {
      userAnswers = entry.answers;
    } else if (typeof entry.answers === 'object' && entry.answers !== null) {
      userAnswers = Object.values(entry.answers);
    } else if (typeof entry.answers === 'string') {
      // Handle case where answers might be a comma-separated string
      userAnswers = entry.answers.split(',').map((a: string) => a.trim());
    }

    console.log('Extracted user answers:', userAnswers);

    // Get correct answers from the entry directly (global correct answers)
    let correctAnswers: string[] = [];

    // First check if entry has correctAnswersText field (global correct answers)
    if (entry.correctAnswersText) {
      console.log('Global correct answers found:', entry.correctAnswersText);

      if (typeof entry.correctAnswersText === 'string') {
        // Handle case where answers are a comma-separated string
        correctAnswers = entry.correctAnswersText.split(',').map((a: string) => a.trim());
      } else if (Array.isArray(entry.correctAnswersText)) {
        correctAnswers = entry.correctAnswersText;
      }

      console.log('Processed global correct answers:', correctAnswers);
    }
    // Fallback to material answers if no global answers are provided
    else if (entry.material && entry.material.answers) {
      console.log('Falling back to reading material answers:', entry.material.answers);

      if (Array.isArray(entry.material.answers)) {
        correctAnswers = entry.material.answers;
      } else if (typeof entry.material.answers === 'object' && entry.material.answers !== null) {
        correctAnswers = Object.values(entry.material.answers);
      } else if (typeof entry.material.answers === 'string') {
        // Handle case where answers might be a comma-separated string
        correctAnswers = entry.material.answers.split(',').map((a: string) => a.trim());
      }

      console.log('Processed material correct answers:', correctAnswers);
    } else {
      console.warn('No correct answers found in entry or material!');
    }

    console.log('Extracted correct answers:', correctAnswers);

    console.log('User answers:', userAnswers);
    console.log('Correct answers:', correctAnswers);

    // Compare answers and get results
    const {
      score,
      band,
      correctAnswers: correctCount,
      totalQuestions,
      mistakes
    } = compareReadingAnswers(userAnswers, correctAnswers);

    console.log(`Score: ${score}, Band: ${band}, Correct: ${correctCount}/${totalQuestions}`);

    // Generate basic feedback based on score
    const strengths: string[] = [];
    const weaknesses: string[] = [];
    const improvementSuggestions: string[] = [];

    // Calculate percentage correct
    const percentCorrect = totalQuestions > 0 ? (correctCount / totalQuestions) * 100 : 0;

    if (percentCorrect >= 80) {
      strengths.push("Excellent reading comprehension");
      strengths.push("Strong ability to identify key information");
      improvementSuggestions.push("Continue practicing with more challenging materials");
    } else if (percentCorrect >= 60) {
      strengths.push("Good understanding of main ideas");
      strengths.push("Accurate detail identification in most cases");
      weaknesses.push("Some difficulty with specific details");
      improvementSuggestions.push("Practice with more complex texts");
      improvementSuggestions.push("Focus on skimming and scanning techniques");
    } else if (percentCorrect >= 40) {
      strengths.push("Basic understanding of main ideas");
      weaknesses.push("Difficulty with specific details");
      weaknesses.push("Challenges with more complex text structures");
      improvementSuggestions.push("Practice with simpler reading materials first");
      improvementSuggestions.push("Focus on vocabulary building");
    } else {
      weaknesses.push("Significant difficulty understanding written English");
      weaknesses.push("Struggles with identifying key information");
      weaknesses.push("Limited vocabulary knowledge");
      improvementSuggestions.push("Start with basic reading exercises");
      improvementSuggestions.push("Build vocabulary systematically");
      improvementSuggestions.push("Practice with shorter texts first");
    }

    // Generate basic section scores
    const criteriaScores = [
      {
        name: "Section 1 (Social/Training)",
        score: "0/13",
        feedback: "Work on understanding everyday texts."
      },
      {
        name: "Section 2 (Workplace)",
        score: "0/13",
        feedback: "Practice with work-related materials."
      },
      {
        name: "Section 3 (Academic)",
        score: "0/14",
        feedback: "Focus on academic reading strategies."
      }
    ];

    // Update the entry in the database
    // Create update data without criteriaScores first
    const updateData = {
      score,
      band,
      correctAnswers: correctCount,
      totalQuestions,
      mistakes: JSON.stringify(mistakes),
      strengths: JSON.stringify(strengths),
      weaknesses: JSON.stringify(weaknesses),
      improvementSuggestions: JSON.stringify(improvementSuggestions)
    };

    // Store criteriaScores in a variable for logging
    const criteriaScoresJson = JSON.stringify(criteriaScores);
    console.log('Generated criteriaScores for reading:', criteriaScoresJson);

    // Update the entry without criteriaScores field
    const updatedEntry = await prisma.readingEntry.update({
      where: { id: entry.id },
      data: updateData
    });

    return updatedEntry;
  } catch (error) {
    console.error('Error processing reading entry:', error);
    throw error;
  }
}

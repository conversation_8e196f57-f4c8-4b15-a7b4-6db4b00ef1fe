import { prisma } from '@/lib/db';

/**
 * Get all writing materials
 * 
 * @returns Array of all writing materials
 */
export async function getAllWritingMaterials() {
  return prisma.writingMaterial.findMany({
    orderBy: { createdAt: 'desc' },
  });
}

/**
 * Get a writing material by ID
 * 
 * @param id - The writing material ID
 * @returns The writing material or null if not found
 */
export async function getWritingMaterialById(id: string) {
  return prisma.writingMaterial.findUnique({
    where: { id },
  });
}

/**
 * Create a new writing material
 * 
 * @param data - The writing material data
 * @returns The created writing material
 */
export async function createWritingMaterial(data: {
  title: string;
  taskType: string;
  taskQuestion: string;
  taskImage?: string;
  sampleAnswer?: string;
}) {
  return prisma.writingMaterial.create({
    data,
  });
}

/**
 * Update a writing material
 * 
 * @param id - The writing material ID
 * @param data - The updated writing material data
 * @returns The updated writing material
 */
export async function updateWritingMaterial(
  id: string,
  data: {
    title?: string;
    taskType?: string;
    taskQuestion?: string;
    taskImage?: string;
    sampleAnswer?: string;
  }
) {
  return prisma.writingMaterial.update({
    where: { id },
    data,
  });
}

/**
 * Delete a writing material
 * 
 * @param id - The writing material ID
 * @returns The deleted writing material
 */
export async function deleteWritingMaterial(id: string) {
  return prisma.writingMaterial.delete({
    where: { id },
  });
}

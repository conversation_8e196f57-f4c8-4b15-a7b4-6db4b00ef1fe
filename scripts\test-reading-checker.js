// Test script for reading checker functionality
const { PrismaClient } = require('../src/generated/prisma');
const prisma = new PrismaClient();

// Band score calculation function (copied from reading-entry-utils.ts)
function calculateBandScore(score, totalQuestions) {
  const percentage = (score / totalQuestions) * 100;

  // IELTS Reading band score conversion (approximate)
  if (percentage >= 90) return 9.0;
  if (percentage >= 85) return 8.5;
  if (percentage >= 80) return 8.0;
  if (percentage >= 75) return 7.5;
  if (percentage >= 70) return 7.0;
  if (percentage >= 65) return 6.5;
  if (percentage >= 60) return 6.0;
  if (percentage >= 55) return 5.5;
  if (percentage >= 50) return 5.0;
  if (percentage >= 45) return 4.5;
  if (percentage >= 40) return 4.0;
  if (percentage >= 35) return 3.5;
  if (percentage >= 30) return 3.0;
  if (percentage >= 25) return 2.5;
  if (percentage >= 20) return 2.0;
  if (percentage >= 15) return 1.5;
  if (percentage >= 10) return 1.0;
  if (percentage >= 5) return 0.5;
  return 0.0;
}

async function main() {
  console.log('Starting reading checker test...');

  try {
    // Test connection
    console.log('Testing database connection...');
    await prisma.$connect();
    console.log('✅ Database connection successful');

    // Test student creation
    console.log('\nTesting student creation...');
    const testStudentId = `test-student-${Date.now()}`;
    const student = await prisma.student.create({
      data: {
        id: testStudentId,
        name: 'Test Reading Student',
        email: `test-reading-${Date.now()}@example.com`,
      },
    });
    console.log(`✅ Student created with ID: ${student.id}`);

    // Test reading material creation
    console.log('\nTesting reading material creation...');
    const readingMaterial = await prisma.readingMaterial.create({
      data: {
        title: 'Test Reading Material for Checker',
        passage: 'This is a test reading passage for the IELTS exam. It contains information about various topics that will be used to test reading comprehension skills.',
        questions: [
          {
            id: 'q1',
            text: 'What is the purpose of this passage?',
            type: 'multiple_choice',
            options: [
              'To entertain readers',
              'To test reading comprehension',
              'To provide information about IELTS',
              'To teach English grammar'
            ]
          },
          {
            id: 'q2',
            text: 'This passage contains information about various topics.',
            type: 'true_false'
          }
        ],
        answers: {
          q1: 'B',
          q2: 'True'
        }
      },
    });
    console.log(`✅ Reading material created with ID: ${readingMaterial.id}`);

    // Test reading entry creation
    console.log('\nTesting reading entry creation...');
    const readingEntry = await prisma.readingEntry.create({
      data: {
        studentId: student.id,
        materialId: readingMaterial.id,
        answers: {
          q1: 'B',
          q2: 'True'
        },
        score: 2,
        band: 9.0,
        correctAnswers: 2,
        totalQuestions: 2,
        strengths: ['Excellent comprehension', 'Perfect accuracy'],
        weaknesses: [],
        improvementSuggestions: ['Continue practicing with more challenging materials']
      },
    });
    console.log(`✅ Reading entry created with ID: ${readingEntry.id}`);

    // Test band score calculation
    console.log('\nTesting band score calculation...');
    const testScores = [
      { score: 40, total: 40, expected: 9.0 },
      { score: 35, total: 40, expected: 8.5 },
      { score: 30, total: 40, expected: 7.5 },
      { score: 25, total: 40, expected: 6.5 },
      { score: 20, total: 40, expected: 5.0 },
      { score: 10, total: 40, expected: 2.5 },
    ];

    for (const test of testScores) {
      const band = calculateBandScore(test.score, test.total);
      console.log(`Score: ${test.score}/${test.total} => Band: ${band} (Expected: ${test.expected})`);
      if (band === test.expected) {
        console.log('✅ Band score calculation correct');
      } else {
        console.log('❌ Band score calculation incorrect');
      }
    }

    // Test reading entry retrieval
    console.log('\nTesting reading entry retrieval...');
    const retrievedEntry = await prisma.readingEntry.findUnique({
      where: { id: readingEntry.id },
      include: {
        student: true,
        material: true,
      },
    });
    console.log(`✅ Retrieved reading entry with ID: ${retrievedEntry.id}`);
    console.log(`✅ Entry belongs to student: ${retrievedEntry.student.name}`);
    console.log(`✅ Entry is for material: ${retrievedEntry.material.title}`);
    console.log(`✅ Entry score: ${retrievedEntry.score}/${retrievedEntry.totalQuestions} (Band ${retrievedEntry.band})`);

    // Test data cleanup
    console.log('\nCleaning up test data...');
    await prisma.readingEntry.delete({ where: { id: readingEntry.id } });
    await prisma.readingMaterial.delete({ where: { id: readingMaterial.id } });
    await prisma.student.delete({ where: { id: student.id } });
    console.log('✅ Test data cleaned up successfully');

    console.log('\n🎉 All reading checker tests passed successfully!');
  } catch (error) {
    console.error('❌ Reading checker test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });

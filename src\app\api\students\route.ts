import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getAllStudents, getOrCreateStudent, getPaginatedStudents } from '@/lib/student-utils';

// GET /api/students
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const id = url.searchParams.get('id');
    const page = url.searchParams.get('page');
    const pageSize = url.searchParams.get('pageSize');
    const paginated = url.searchParams.get('paginated');

    if (id) {
      // Get a specific student
      const student = await prisma.student.findUnique({
        where: { id },
        include: {
          writingEntries: true,
          speakingEntries: true,
          readingEntries: true,
          listeningEntries: true,
          reports: true,
        },
      });

      if (!student) {
        return NextResponse.json({ error: 'Student not found' }, { status: 404 });
      }

      return NextResponse.json(student);
    } else if (paginated === 'true') {
      // Get paginated students
      const pageNumber = page ? parseInt(page, 10) : 1;
      const size = pageSize ? parseInt(pageSize, 10) : 5; // Reduced to 5 students per page

      const result = await getPaginatedStudents(pageNumber, size);
      return NextResponse.json(result);
    } else {
      // Get all students (legacy support)
      const students = await getAllStudents();
      return NextResponse.json(students);
    }
  } catch (error) {
    console.error('Error fetching students:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// POST /api/students
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, email } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Student ID is required' },
        { status: 400 }
      );
    }

    // Create or update student
    const student = await getOrCreateStudent(id, name);

    // Update email if provided
    if (email && student.email !== email) {
      await prisma.student.update({
        where: { id: student.id },
        data: { email },
      });
    }

    return NextResponse.json(student, { status: 201 });
  } catch (error) {
    console.error('Error creating student:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

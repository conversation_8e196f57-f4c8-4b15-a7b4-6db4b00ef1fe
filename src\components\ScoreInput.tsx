'use client';

import React, { useState, useEffect } from 'react';
import { Box, TextField, Typography, Paper, Grid } from '@mui/material';
import { convertListeningRawScoreToBand, convertReadingRawScoreToBand } from '@/lib/score-conversion';

interface ScoreInputProps {
  type: 'reading' | 'listening';
  initialValue?: string;
  onChange: (value: string, band: number) => void;
  label?: string;
  helperText?: string;
}

const ScoreInput: React.FC<ScoreInputProps> = ({
  type,
  initialValue = '',
  onChange,
  label = 'Raw Score',
  helperText = 'Enter score as "30/40" or just the number of correct answers'
}) => {
  const [rawScore, setRawScore] = useState(initialValue);
  const [bandScore, setBandScore] = useState<number | null>(null);
  const isInitialMount = React.useRef(true);
  const prevBandScore = React.useRef<number | null>(null);

  // Calculate band score when raw score changes
  useEffect(() => {
    if (rawScore) {
      const band = type === 'reading'
        ? convertReadingRawScoreToBand(rawScore)
        : convertListeningRawScoreToBand(rawScore);

      setBandScore(band);
    } else {
      setBandScore(null);
    }
  }, [rawScore, type]);

  // Separate effect to call onChange only when bandScore changes
  useEffect(() => {
    // Skip the first render
    if (isInitialMount.current) {
      isInitialMount.current = false;
      prevBandScore.current = bandScore;
      return;
    }

    // Only call onChange if the band score actually changed
    if (rawScore && bandScore !== null && bandScore !== prevBandScore.current) {
      prevBandScore.current = bandScore;
      onChange(rawScore, bandScore);
    }
  }, [bandScore, rawScore, onChange]);

  const handleScoreChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRawScore(e.target.value);
  };

  return (
    <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label={label}
            value={rawScore}
            onChange={handleScoreChange}
            helperText={helperText}
            placeholder="e.g. 30/40"
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="subtitle2" color="textSecondary">
              Band Score
            </Typography>
            <Typography variant="h4" color="primary" sx={{ fontWeight: 'bold' }}>
              {bandScore !== null ? bandScore.toFixed(1) : '-'}
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default ScoreInput;

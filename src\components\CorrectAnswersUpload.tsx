'use client';

import React, { useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  TextField,
  Typography,
  Grid,
} from '@mui/material';

interface CorrectAnswersUploadProps {
  title: string;
  value: string;
  onChange: (value: string) => void;
  onApplyToAll: () => void;
  helperText?: string;
}

const CorrectAnswersUpload: React.FC<CorrectAnswersUploadProps> = ({
  title,
  value,
  onChange,
  onApplyToAll,
  helperText = 'Enter the correct answers separated by commas'
}) => {
  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Correct Answers (comma-separated)"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              variant="outlined"
              helperText={helperText}
            />
          </Grid>
          <Grid item xs={12} sx={{ textAlign: 'right' }}>
            <Button
              variant="contained"
              color="primary"
              onClick={onApplyToAll}
              disabled={!value.trim()}
            >
              Apply to All Candidates
            </Button>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default CorrectAnswersUpload;

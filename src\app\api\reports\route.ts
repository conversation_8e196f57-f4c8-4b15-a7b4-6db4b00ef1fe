import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getOrCreateStudent } from '@/lib/student-utils';
import { calculateOverallBand, generateReport } from '@/lib/report-utils';

// GET /api/reports
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const id = url.searchParams.get('id');
    const studentId = url.searchParams.get('studentId');

    if (id) {
      // Get a specific report
      const report = await prisma.report.findUnique({
        where: { id },
        include: {
          student: true,
        },
      });

      if (!report) {
        return NextResponse.json({ error: 'Report not found' }, { status: 404 });
      }

      return NextResponse.json(report);
    } else if (studentId) {
      // Get all reports for a specific student
      const reports = await prisma.report.findMany({
        where: { studentId },
        orderBy: { createdAt: 'desc' },
        include: {
          student: true,
        },
      });

      return NextResponse.json(reports);
    } else {
      // Check for pagination parameters
      const page = url.searchParams.get('page');
      const pageSize = url.searchParams.get('pageSize');

      // Default to page 1 with 5 items per page if not specified
      const pageNumber = page ? parseInt(page, 10) : 1;
      const itemsPerPage = pageSize ? parseInt(pageSize, 10) : 5;

      // Calculate skip value for pagination
      const skip = (pageNumber - 1) * itemsPerPage;

      // Get total count for pagination metadata
      const totalCount = await prisma.report.count();

      // Get reports for the current page
      const reports = await prisma.report.findMany({
        orderBy: { createdAt: 'desc' },
        include: {
          student: true,
        },
        skip,
        take: itemsPerPage,
      });

      // Calculate total pages
      const totalPages = Math.ceil(totalCount / itemsPerPage);

      // Return reports with pagination metadata
      return NextResponse.json({
        reports,
        pagination: {
          currentPage: pageNumber,
          pageSize: itemsPerPage,
          totalCount,
          totalPages,
          hasNextPage: pageNumber < totalPages,
          hasPreviousPage: pageNumber > 1,
        }
      });
    }
  } catch (error) {
    console.error('Error fetching reports:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// POST /api/reports - Create a new report
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { studentId, reportData } = body;

    if (!studentId) {
      return NextResponse.json(
        { error: 'Student ID is required' },
        { status: 400 }
      );
    }

    // Get or create student
    const student = await getOrCreateStudent(studentId);

    // Create a new report
    const report = await prisma.report.create({
      data: {
        studentId,
        candidateName: student.name || studentId,
        testDate: new Date(),
        overallBand: reportData?.overallBand || null,
        printStatus: 'pending',
        ...reportData
      },
    });

    return NextResponse.json(report, { status: 201 });
  } catch (error) {
    console.error('Error creating report:', error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

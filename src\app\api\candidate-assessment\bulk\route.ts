import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// POST handler to create multiple candidates at once
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { studentIds, count, materials } = body;

    let idsToCreate: string[] = [];

    // Handle count-based creation
    if (count && typeof count === 'number' && count > 0) {
      // Generate sequential IDs based on current date
      const today = new Date();
      const month = (today.getMonth() + 1).toString().padStart(2, '0');
      const day = today.getDate().toString().padStart(2, '0');
      const dateStr = `${month}.${day}`;

      for (let i = 1; i <= count; i++) {
        // Format: MM.DD-XXX (e.g., 04.16-001)
        idsToCreate.push(`${dateStr}-${i.toString().padStart(3, '0')}`);
      }
    }
    // Handle manual ID entry
    else if (studentIds && Array.isArray(studentIds) && studentIds.length > 0) {
      idsToCreate = studentIds;
    }
    // Invalid input
    else {
      return NextResponse.json(
        { error: 'Either student IDs or a count must be provided' },
        { status: 400 }
      );
    }

    // Check for existing students
    const existingStudents = await prisma.student.findMany({
      where: {
        id: {
          in: idsToCreate
        }
      }
    });

    if (existingStudents.length > 0) {
      const existingIds = existingStudents.map(s => s.id);
      return NextResponse.json(
        { error: `The following student IDs already exist: ${existingIds.join(', ')}` },
        { status: 400 }
      );
    }

    // Create all students in a transaction
    const createdStudents = await prisma.$transaction(
      idsToCreate.map(id =>
        prisma.student.create({
          data: {
            id: id,
            name: null,
          }
        })
      )
    );

    // If materials are provided, create entries for each student
    if (materials) {
      const { readingMaterialId, listeningMaterialId, writingMaterialId } = materials;

      // Create entries in batches
      const entries = [];

      // Add reading entries if material is selected
      if (readingMaterialId) {
        for (const student of createdStudents) {
          entries.push(
            prisma.readingEntry.create({
              data: {
                studentId: student.id,
                materialId: readingMaterialId,
                answers: {},
              }
            })
          );
        }
      }

      // Add listening entries if material is selected
      if (listeningMaterialId) {
        for (const student of createdStudents) {
          entries.push(
            prisma.listeningEntry.create({
              data: {
                studentId: student.id,
                materialId: listeningMaterialId,
                answers: {},
              }
            })
          );
        }
      }

      // Add writing entries if material is selected
      if (writingMaterialId) {
        for (const student of createdStudents) {
          entries.push(
            prisma.writingEntry.create({
              data: {
                studentId: student.id,
                materialId: writingMaterialId,
                taskType: 'task1', // Default to task1
                essayText: '',
              }
            })
          );
        }
      }

      // Execute all entry creations if there are any
      if (entries.length > 0) {
        await prisma.$transaction(entries);
      }
    }

    return NextResponse.json(createdStudents, { status: 201 });
  } catch (error) {
    console.error('Error creating candidates in bulk:', error);
    return NextResponse.json(
      { error: 'Failed to create candidates' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { 
  getAllReadingMaterials, 
  getReadingMaterialById, 
  createReadingMaterial, 
  updateReadingMaterial, 
  deleteReadingMaterial 
} from '@/lib/reading-material-utils';

// GET /api/reading-materials
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (id) {
      // Get a specific reading material
      const material = await getReadingMaterialById(id);
      
      if (!material) {
        return NextResponse.json({ error: 'Reading material not found' }, { status: 404 });
      }
      
      return NextResponse.json(material);
    } else {
      // Get all reading materials
      const materials = await getAllReadingMaterials();
      return NextResponse.json(materials);
    }
  } catch (error) {
    console.error('Error fetching reading materials:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// POST /api/reading-materials
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, passage, questions, answers } = body;

    // Validate required fields
    if (!title || !passage || !questions || !answers) {
      return NextResponse.json(
        { error: 'Title, passage, questions, and answers are required' },
        { status: 400 }
      );
    }

    // Create new reading material
    const material = await createReadingMaterial({
      title,
      passage,
      questions,
      answers,
    });

    return NextResponse.json(material, { status: 201 });
  } catch (error) {
    console.error('Error creating reading material:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// PUT /api/reading-materials
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, title, passage, questions, answers } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json(
        { error: 'Reading material ID is required' },
        { status: 400 }
      );
    }

    // Check if reading material exists
    const existingMaterial = await getReadingMaterialById(id);
    if (!existingMaterial) {
      return NextResponse.json(
        { error: 'Reading material not found' },
        { status: 404 }
      );
    }

    // Update reading material
    const material = await updateReadingMaterial(id, {
      title,
      passage,
      questions,
      answers,
    });

    return NextResponse.json(material);
  } catch (error) {
    console.error('Error updating reading material:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// DELETE /api/reading-materials
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Reading material ID is required' },
        { status: 400 }
      );
    }

    // Check if reading material exists
    const existingMaterial = await getReadingMaterialById(id);
    if (!existingMaterial) {
      return NextResponse.json(
        { error: 'Reading material not found' },
        { status: 404 }
      );
    }

    // Delete reading material
    await deleteReadingMaterial(id);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting reading material:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

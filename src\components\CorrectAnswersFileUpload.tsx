'use client';

import React, { useState } from 'react';
import {
  Box,
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Typography,
  Grid,
  Alert,
  CircularProgress,
} from '@mui/material';
import { Upload as UploadIcon } from '@mui/icons-material';

interface CorrectAnswersFileUploadProps {
  title: string;
  value: string;
  onChange: (value: string) => void;
  onApplyToAll: () => void;
}

const CorrectAnswersFileUpload: React.FC<CorrectAnswersFileUploadProps> = ({
  title,
  value,
  onChange,
  onApplyToAll,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setLoading(true);
    setError(null);

    try {
      // Read the file content
      const text = await file.text();

      // Process the file content based on file type
      let answers = '';

      if (file.name.endsWith('.txt') || file.name.endsWith('.csv')) {
        // For text files, assume each line is an answer
        // Clean up the text - remove extra spaces, convert to comma-separated
        answers = text
          .split(/\r?\n/) // Split by newlines
          .map(line => line.trim()) // Trim each line
          .filter(line => line.length > 0) // Remove empty lines
          .join(','); // Join with commas
      } else {
        throw new Error('Unsupported file type. Please upload a .txt or .csv file.');
      }

      // Update the answers
      onChange(answers);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while processing the file');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Correct Answers (comma-separated)"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              variant="outlined"
              helperText="Enter the correct answers separated by commas"
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Button
                variant="outlined"
                component="label"
                startIcon={loading ? <CircularProgress size={20} /> : <UploadIcon />}
                disabled={loading}
              >
                {loading ? 'Uploading...' : 'Upload Answers File'}
                <input
                  type="file"
                  hidden
                  accept=".txt,.csv"
                  onChange={handleFileUpload}
                  disabled={loading}
                />
              </Button>

              <Button
                variant="contained"
                color="primary"
                onClick={onApplyToAll}
                disabled={!value.trim() || loading}
              >
                Apply to All Candidates
              </Button>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default CorrectAnswersFileUpload;

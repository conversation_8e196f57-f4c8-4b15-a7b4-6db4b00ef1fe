// Check if criteriaScores field exists in ListeningEntry table
const { exec } = require('child_process');

// Function to execute a command and return a promise
function execCommand(command) {
  return new Promise((resolve, reject) => {
    console.log(`Executing: ${command}`);

    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error: ${error.message}`);
        return reject(error);
      }

      if (stderr) {
        console.error(`stderr: ${stderr}`);
      }

      console.log(`stdout: ${stdout}`);
      resolve(stdout);
    });
  });
}

async function checkCriteriaScores() {
  try {
    // Create the directory if it doesn't exist
    const fs = require('fs');
    const path = require('path');
    const tempSchemaDir = path.join(__dirname, '..', 'prisma');
    const tempSchemaPath = path.join(tempSchemaDir, 'temp-introspect.prisma');

    // Make sure the directory exists
    if (!fs.existsSync(tempSchemaDir)) {
      fs.mkdirSync(tempSchemaDir, { recursive: true });
    }

    // Use Prisma CLI to introspect the database
    await execCommand('npx prisma db pull --schema=prisma/temp-introspect.prisma');

    // Read the introspected schema

    if (fs.existsSync(tempSchemaPath)) {
      const schema = fs.readFileSync(tempSchemaPath, 'utf8');

      // Check if criteriaScores exists in the ListeningEntry model
      const hasCriteriaScores = schema.includes('model ListeningEntry') &&
                               schema.includes('criteriaScores');

      console.log(`criteriaScores field exists: ${hasCriteriaScores}`);

      // Clean up the temporary schema file
      fs.unlinkSync(tempSchemaPath);

      return hasCriteriaScores;
    } else {
      console.error('Temporary schema file not found');
      return false;
    }
  } catch (error) {
    console.error('Error checking criteriaScores field:', error);
    return false;
  }
}

// Run the script
checkCriteriaScores()
  .then(exists => {
    if (exists) {
      console.log('criteriaScores field exists in the ListeningEntry table.');
    } else {
      console.log('criteriaScores field does NOT exist in the ListeningEntry table.');
      console.log('You may need to run the migration script to add it.');
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });

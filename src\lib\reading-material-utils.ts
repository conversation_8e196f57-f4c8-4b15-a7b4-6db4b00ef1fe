import { prisma } from '@/lib/db';

/**
 * Get all reading materials
 * 
 * @returns Array of all reading materials
 */
export async function getAllReadingMaterials() {
  return prisma.readingMaterial.findMany({
    orderBy: { createdAt: 'desc' },
  });
}

/**
 * Get a reading material by ID
 * 
 * @param id - The reading material ID
 * @returns The reading material or null if not found
 */
export async function getReadingMaterialById(id: string) {
  return prisma.readingMaterial.findUnique({
    where: { id },
  });
}

/**
 * Create a new reading material
 * 
 * @param data - The reading material data
 * @returns The created reading material
 */
export async function createReadingMaterial(data: {
  title: string;
  passage: string;
  questions: any;
  answers: any;
}) {
  return prisma.readingMaterial.create({
    data,
  });
}

/**
 * Update a reading material
 * 
 * @param id - The reading material ID
 * @param data - The updated reading material data
 * @returns The updated reading material
 */
export async function updateReadingMaterial(
  id: string,
  data: {
    title?: string;
    passage?: string;
    questions?: any;
    answers?: any;
  }
) {
  return prisma.readingMaterial.update({
    where: { id },
    data,
  });
}

/**
 * Delete a reading material
 * 
 * @param id - The reading material ID
 * @returns The deleted reading material
 */
export async function deleteReadingMaterial(id: string) {
  return prisma.readingMaterial.delete({
    where: { id },
  });
}

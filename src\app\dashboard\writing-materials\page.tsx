'use client';

import { useState, useEffect } from 'react';
import {
  Box,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  MenuItem,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  Alert,
} from '@mui/material';
import {
  Plus as PlusIcon,
  Pencil as EditIcon,
  Trash as DeleteIcon,
  Eye as ViewIcon,
} from '@phosphor-icons/react';

interface WritingMaterial {
  id: string;
  title: string;
  taskType: string;
  taskQuestion: string;
  taskImage?: string;
  sampleAnswer?: string;
  createdAt: string;
  updatedAt: string;
}

export default function WritingMaterialsPage() {
  const [materials, setMaterials] = useState<WritingMaterial[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [viewDialog, setViewDialog] = useState(false);
  const [currentMaterial, setCurrentMaterial] = useState<Partial<WritingMaterial>>({});
  const [isEditing, setIsEditing] = useState(false);

  // Fetch writing materials
  const fetchMaterials = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/writing-materials');
      if (!response.ok) {
        throw new Error('Failed to fetch writing materials');
      }
      const data = await response.json();
      setMaterials(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMaterials();
  }, []);

  // Handle form submission
  const handleSubmit = async () => {
    try {
      if (!currentMaterial.title || !currentMaterial.taskType || !currentMaterial.taskQuestion) {
        setError('Title, task type, and task question are required');
        return;
      }

      const url = isEditing ? '/api/writing-materials' : '/api/writing-materials';
      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(currentMaterial),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save writing material');
      }

      // Refresh the materials list
      fetchMaterials();
      setOpenDialog(false);
      setCurrentMaterial({});
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    }
  };

  // Handle delete
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this writing material?')) {
      return;
    }

    try {
      const response = await fetch(`/api/writing-materials?id=${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete writing material');
      }

      // Refresh the materials list
      fetchMaterials();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    }
  };

  // Handle view
  const handleView = (material: WritingMaterial) => {
    setCurrentMaterial(material);
    setViewDialog(true);
  };

  // Handle edit
  const handleEdit = (material: WritingMaterial) => {
    setCurrentMaterial(material);
    setIsEditing(true);
    setOpenDialog(true);
  };

  // Handle add new
  const handleAddNew = () => {
    setCurrentMaterial({
      taskType: 'task2', // Default to Task 2
    });
    setIsEditing(false);
    setOpenDialog(true);
  };

  return (
    <Box>
      <Stack spacing={3}>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={8}>
            <Typography variant="h4">Writing Materials</Typography>
            <Typography color="text.secondary" sx={{ mt: 1 }}>
              Manage writing task materials for IELTS tests
            </Typography>
          </Grid>
          <Grid item xs={12} md={4} sx={{ textAlign: 'right' }}>
            <Button
              variant="contained"
              startIcon={<PlusIcon />}
              onClick={handleAddNew}
            >
              Add New Material
            </Button>
          </Grid>
        </Grid>

        {error && (
          <Alert severity="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        <Card>
          <CardContent>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : materials.length === 0 ? (
              <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                <Typography color="text.secondary">
                  No writing materials found. Add your first writing material to get started.
                </Typography>
              </Paper>
            ) : (
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Title</TableCell>
                      <TableCell>Task Type</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {materials.map((material) => (
                      <TableRow key={material.id}>
                        <TableCell>{material.title}</TableCell>
                        <TableCell>
                          {material.taskType === 'task1'
                            ? 'Task 1'
                            : material.taskType === 'task2'
                            ? 'Task 2'
                            : 'Both Tasks'}
                        </TableCell>
                        <TableCell>
                          {new Date(material.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell align="right">
                          <IconButton
                            size="small"
                            onClick={() => handleView(material)}
                            title="View"
                          >
                            <ViewIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleEdit(material)}
                            title="Edit"
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleDelete(material.id)}
                            title="Delete"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </CardContent>
        </Card>
      </Stack>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>{isEditing ? 'Edit Writing Material' : 'Add New Writing Material'}</DialogTitle>
        <DialogContent>
          <Stack spacing={2} sx={{ mt: 1 }}>
            <TextField
              label="Title"
              fullWidth
              value={currentMaterial.title || ''}
              onChange={(e) => setCurrentMaterial({ ...currentMaterial, title: e.target.value })}
              required
            />
            <TextField
              select
              label="Task Type"
              fullWidth
              value={currentMaterial.taskType || 'task2'}
              onChange={(e) => setCurrentMaterial({ ...currentMaterial, taskType: e.target.value })}
              required
            >
              <MenuItem value="task1">Task 1</MenuItem>
              <MenuItem value="task2">Task 2</MenuItem>
              <MenuItem value="both">Both Tasks</MenuItem>
            </TextField>
            <TextField
              label="Task Question"
              fullWidth
              multiline
              rows={4}
              value={currentMaterial.taskQuestion || ''}
              onChange={(e) => setCurrentMaterial({ ...currentMaterial, taskQuestion: e.target.value })}
              required
            />
            <TextField
              label="Task Image URL (optional, for Task 1)"
              fullWidth
              value={currentMaterial.taskImage || ''}
              onChange={(e) => setCurrentMaterial({ ...currentMaterial, taskImage: e.target.value })}
              helperText="URL to an image for Task 1 (e.g., chart, graph, diagram)"
            />
            <TextField
              label="Sample Answer (optional)"
              fullWidth
              multiline
              rows={6}
              value={currentMaterial.sampleAnswer || ''}
              onChange={(e) => setCurrentMaterial({ ...currentMaterial, sampleAnswer: e.target.value })}
              helperText="Provide a sample answer for reference"
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleSubmit}>
            {isEditing ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Dialog */}
      <Dialog open={viewDialog} onClose={() => setViewDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>View Writing Material</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Title
              </Typography>
              <Typography variant="body1">{currentMaterial.title}</Typography>
            </Box>
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Task Type
              </Typography>
              <Typography variant="body1">
                {currentMaterial.taskType === 'task1'
                  ? 'Task 1'
                  : currentMaterial.taskType === 'task2'
                  ? 'Task 2'
                  : 'Both Tasks'}
              </Typography>
            </Box>
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Task Question
              </Typography>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                  {currentMaterial.taskQuestion}
                </Typography>
              </Paper>
            </Box>
            {currentMaterial.taskImage && (
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Task Image
                </Typography>
                <Box sx={{ textAlign: 'center' }}>
                  <img
                    src={currentMaterial.taskImage}
                    alt="Task Image"
                    style={{ maxWidth: '100%', maxHeight: '300px' }}
                  />
                </Box>
              </Box>
            )}
            {currentMaterial.sampleAnswer && (
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Sample Answer
                </Typography>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                    {currentMaterial.sampleAnswer}
                  </Typography>
                </Paper>
              </Box>
            )}
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialog(false)}>Close</Button>
          <Button
            variant="outlined"
            onClick={() => {
              setViewDialog(false);
              handleEdit(currentMaterial as WritingMaterial);
            }}
          >
            Edit
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

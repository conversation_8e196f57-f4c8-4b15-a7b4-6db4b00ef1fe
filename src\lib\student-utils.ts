import { prisma } from '@/lib/db';

/**
 * Get or create a student by ID
 * If the student doesn't exist, it will be created
 *
 * @param studentId - The student ID (can be any string)
 * @param name - Optional student name
 * @returns The student object
 */
export async function getOrCreateStudent(studentId: string, name?: string) {
  // Try to find the student first
  let student = await prisma.student.findUnique({
    where: { id: studentId },
  });

  // If student doesn't exist, create a new one
  if (!student) {
    student = await prisma.student.create({
      data: {
        id: studentId,
        name: name || null,
      },
    });
  }

  return student;
}

/**
 * Get all students
 *
 * @returns Array of all students
 */
export async function getAllStudents() {
  return prisma.student.findMany({
    orderBy: { createdAt: 'desc' },
  });
}

/**
 * Get paginated students
 *
 * @param page - The page number (1-based)
 * @param pageSize - The number of students per page
 * @returns Object containing students array and pagination metadata
 */
export async function getPaginatedStudents(page: number = 1, pageSize: number = 10) {
  // Ensure page is at least 1
  const currentPage = Math.max(1, page);

  // Calculate skip value for pagination
  const skip = (currentPage - 1) * pageSize;

  // Get total count for pagination metadata
  const totalCount = await prisma.student.count();

  // Get students for the current page
  const students = await prisma.student.findMany({
    orderBy: { createdAt: 'desc' },
    skip,
    take: pageSize,
  });

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / pageSize);

  return {
    students,
    pagination: {
      currentPage,
      pageSize,
      totalCount,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPreviousPage: currentPage > 1,
    }
  };
}

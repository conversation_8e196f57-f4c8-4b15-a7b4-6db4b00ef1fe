// Initialize database script
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Function to execute a command and return a promise
function execCommand(command) {
  return new Promise((resolve, reject) => {
    console.log(`Executing: ${command}`);
    
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error: ${error.message}`);
        return reject(error);
      }
      
      if (stderr) {
        console.error(`stderr: ${stderr}`);
      }
      
      console.log(`stdout: ${stdout}`);
      resolve(stdout);
    });
  });
}

async function initDatabase() {
  console.log('Initializing database...');
  
  try {
    // Create a temporary Prisma schema file without the output directive
    // This is to avoid the permission issues with the generated client
    const schemaPath = path.join(__dirname, '..', 'prisma', 'schema.prisma');
    const tempSchemaPath = path.join(__dirname, '..', 'prisma', 'temp-schema.prisma');
    
    // Read the original schema
    const originalSchema = fs.readFileSync(schemaPath, 'utf8');
    
    // Modify the schema to remove the output directive
    const modifiedSchema = originalSchema.replace(
      /generator client {[^}]*}/,
      'generator client {\n  provider = "prisma-client-js"\n}'
    );
    
    // Write the modified schema to a temporary file
    fs.writeFileSync(tempSchemaPath, modifiedSchema);
    
    console.log('Created temporary schema file without output directive');
    
    // Push the schema to the database
    try {
      await execCommand('npx prisma db push --schema=./prisma/temp-schema.prisma --force-reset');
      console.log('Database schema pushed successfully!');
    } catch (pushError) {
      console.error('Error pushing schema to database:', pushError);
      throw pushError;
    }
    
    // Clean up the temporary schema file
    fs.unlinkSync(tempSchemaPath);
    console.log('Removed temporary schema file');
    
    return true;
  } catch (error) {
    console.error('Error initializing database:', error);
    return false;
  }
}

// Run the script
initDatabase()
  .then(success => {
    if (success) {
      console.log('Database initialized successfully.');
    } else {
      console.error('Failed to initialize database.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });

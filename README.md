# TestHub

![license](https://img.shields.io/badge/license-MIT-blue.svg)

> A Next.js application for IELTS test preparation and assessment, built with MUI components and React.

## Features

- Dashboard for IELTS test preparation
- Bulk Writing Checker
- Bulk Speaking Checker
- Detailed Writing Analysis
- Detailed Speaking Analysis
- Handwritten Text Scanner
- Authentication System

## Quick start

- Clone the repository
- Make sure your Node.js and npm versions are up to date
- Install dependencies: `npm install` or `yarn` or `pnpm install`
- Create a `.env.local` file with your API keys (see `.env.example`)
- Start the development server: `npm run dev` or `yarn dev` or `pnpm dev`
- Open browser: `http://localhost:3000`

## Deployment to Render.com

### Prerequisites

1. Create a [Render.com](https://render.com) account
2. Have your API keys ready:
   - Anthropic API key for Claude AI
   - OpenAI API key for audio transcription and analysis

### Deployment Steps

1. **Fork or push your repository to GitHub, GitLab, or Bitbucket**

2. **Connect to Render.com**
   - Log in to your Render dashboard
   - Click "New" and select "Web Service"
   - Connect your repository

3. **Configure the Web Service**
   - Name: `testhub` (or your preferred name)
   - Environment: `Node`
   - Build Command: `npm ci && npm run build`
   - Start Command: `npm start`

4. **Set Environment Variables**
   - Add the following environment variables:
     - `NODE_ENV`: `production`
     - `ANTHROPIC_API_KEY`: Your Anthropic API key
     - `OPENAI_API_KEY`: Your OpenAI API key
     - `NEXT_PUBLIC_SITE_URL`: Will be automatically set to your Render URL
     - `NEXT_PUBLIC_LOG_LEVEL`: `ERROR` (recommended for production)

5. **Deploy**
   - Click "Create Web Service"
   - Render will automatically build and deploy your application

6. **Access Your Application**
   - Once deployment is complete, you can access your application at the URL provided by Render

### Using render.yaml (Alternative Method)

This repository includes a `render.yaml` file that can be used for automatic deployment:

1. Push your code to a Git repository
2. In Render dashboard, go to "Blueprints"
3. Connect your repository
4. Render will detect the `render.yaml` file and set up the service
5. You'll need to manually add the secret environment variables (API keys)

## File Structure

The project has the following structure:

```
├── .env.example           # Example environment variables
├── .env.local             # Local environment variables (not committed)
├── .eslintrc.js           # ESLint configuration
├── .gitignore             # Git ignore rules
├── LICENSE.md             # License information
├── README.md              # Project documentation
├── next.config.mjs        # Next.js configuration
├── package.json           # Project dependencies and scripts
├── render.yaml            # Render.com deployment configuration
├── tsconfig.json          # TypeScript configuration
├── public/                # Static assets
└── src/
    ├── app/               # Next.js app router pages
    │   ├── api/           # API routes
    │   ├── auth/          # Authentication pages
    │   ├── dashboard/     # Dashboard pages
    │   ├── layout.tsx     # Root layout
    │   └── page.tsx       # Home page
    ├── components/        # React components
    ├── contexts/          # React contexts
    ├── hooks/             # Custom React hooks
    ├── lib/               # Utility functions
    ├── styles/            # CSS and styling
    └── types/             # TypeScript type definitions
```

## Environment Variables

The following environment variables are required:

- `ANTHROPIC_API_KEY`: API key for Anthropic's Claude AI
- `OPENAI_API_KEY`: API key for OpenAI (used for audio transcription and analysis)
- `NEXT_PUBLIC_SITE_URL`: The URL where the site is hosted
- `NEXT_PUBLIC_LOG_LEVEL`: Log level (ALL, DEBUG, WARN, ERROR, NONE)

## License

- Licensed under [MIT](LICENSE.md)

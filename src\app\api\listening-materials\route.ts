import { NextRequest, NextResponse } from 'next/server';
import { 
  getAllListeningMaterials, 
  getListeningMaterialById, 
  createListeningMaterial, 
  updateListeningMaterial, 
  deleteListeningMaterial 
} from '@/lib/listening-material-utils';

// GET /api/listening-materials
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (id) {
      // Get a specific listening material
      const material = await getListeningMaterialById(id);
      
      if (!material) {
        return NextResponse.json({ error: 'Listening material not found' }, { status: 404 });
      }
      
      return NextResponse.json(material);
    } else {
      // Get all listening materials
      const materials = await getAllListeningMaterials();
      return NextResponse.json(materials);
    }
  } catch (error) {
    console.error('Error fetching listening materials:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// POST /api/listening-materials
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, audioUrl, transcript, questions, answers, section } = body;

    // Validate required fields
    if (!title || !audioUrl || !questions || !answers) {
      return NextResponse.json(
        { error: 'Title, audioUrl, questions, and answers are required' },
        { status: 400 }
      );
    }

    // Create new listening material
    const material = await createListeningMaterial({
      title,
      audioUrl,
      transcript,
      questions,
      answers,
      section,
    });

    return NextResponse.json(material, { status: 201 });
  } catch (error) {
    console.error('Error creating listening material:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// PUT /api/listening-materials
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, title, audioUrl, transcript, questions, answers, section } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json(
        { error: 'Listening material ID is required' },
        { status: 400 }
      );
    }

    // Check if listening material exists
    const existingMaterial = await getListeningMaterialById(id);
    if (!existingMaterial) {
      return NextResponse.json(
        { error: 'Listening material not found' },
        { status: 404 }
      );
    }

    // Update listening material
    const material = await updateListeningMaterial(id, {
      title,
      audioUrl,
      transcript,
      questions,
      answers,
      section,
    });

    return NextResponse.json(material);
  } catch (error) {
    console.error('Error updating listening material:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// DELETE /api/listening-materials
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Listening material ID is required' },
        { status: 400 }
      );
    }

    // Check if listening material exists
    const existingMaterial = await getListeningMaterialById(id);
    if (!existingMaterial) {
      return NextResponse.json(
        { error: 'Listening material not found' },
        { status: 404 }
      );
    }

    // Delete listening material
    await deleteListeningMaterial(id);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting listening material:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getOrCreateStudent } from '@/lib/student-utils';
import { createSpeakingEntry } from '@/lib/speaking-utils';
import { inflateBandScore } from '@/utils/band-score-inflation';

// Get API key from environment variables
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

if (!OPENAI_API_KEY) {
  throw new Error('OPENAI_API_KEY environment variable is not set');
}

export async function POST(req: NextRequest) {
  try {
    const { entries } = await req.json();

    if (!Array.isArray(entries) || entries.length === 0) {
      return NextResponse.json(
        { error: 'No entries provided' },
        { status: 400 }
      );
    }

    // Check if studentId is provided for each entry
    for (let i = 0; i < entries.length; i++) {
      if (!entries[i].studentId) {
        entries[i].studentId = `anonymous-${Date.now()}-${i}`; // Generate a temporary ID if not provided
      }
    }

    const results = await Promise.all(
      entries.map(async (entry) => {
        // Create an AbortController for timeout handling
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

        try {
          const prompt = `You are an expert IELTS Speaking examiner. Analyze the following speaking response and provide a detailed assessment. Please identify and analyze **up to 10 distinct questions** and the candidate's responses to them within the provided text. If fewer than 10 questions are identifiable, analyze all that are present.
          For each question/part of the response, provide specific feedback on mistakes, improvements, and strengths.
          Format your response as a JSON object with the following structure:
          {
            "overallScore": number (0-9),
            "criteria": [
              {
                "name": "Fluency and Coherence",
                "score": number (0-9),
                "feedback": string,
                "strengths": string[],
                "weaknesses": string[],
                "improvements": string[]
              },
              {
                "name": "Lexical Resource",
                "score": number (0-9),
                "feedback": string,
                "strengths": string[],
                "weaknesses": string[],
                "improvements": string[]
              },
              {
                "name": "Grammatical Range and Accuracy",
                "score": number (0-9),
                "feedback": string,
                "strengths": string[],
                "weaknesses": string[],
                "improvements": string[]
              },
              {
                "name": "Pronunciation",
                "score": number (0-9),
                "feedback": string,
                "strengths": string[],
                "weaknesses": string[],
                "improvements": string[]
              }
            ],
            "speechAnalysis": {
              "totalErrors": number,
              "errorFrequency": string,
              "mostFrequentErrorType": string,
              "errorsByType": {
                "grammarErrors": [
                  {
                    "original": string,
                    "correction": string,
                    "explanation": string
                  }
                ]
              },
              "speechStrengths": string[]
            },
            "vocabularyAnalysis": {
              "lexicalDiversity": {
                "diversityScore": number,
                "uniqueWords": number,
                "totalWords": number
              },
              "vocabularyLevel": {
                "distribution": {
                  "basic": number,
                  "intermediate": number,
                  "advanced": number
                }
              },
              "overusedWords": [
                {
                  "word": string,
                  "count": number,
                  "alternatives": string[]
                }
              ]
            },
            "improvementSuggestions": [
              {
                "original": string,
                "improved": string,
                "explanation": string
              }
            ],
            "questionAnalysis": [
              {
                "question": string,
                "response": string,
                "mistakes": string[],
                "improvements": string[],
                "strengths": string[]
              }
            ]
          }

          Candidate's response:
          ${entry.transcript}

          ${entry.examinerContent ? `Examiner's questions/prompts:
          ${entry.examinerContent}` : ''}`;

          // Use fetch API directly instead of OpenAI SDK
          const { transcript, examinerContent, studentId, audioUrl } = entry;

          const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${OPENAI_API_KEY}`
            },
            signal: controller.signal,
            body: JSON.stringify({
              model: 'gpt-4o-mini',
              messages: [
                {
                  role: 'system',
                  content: 'You are an expert IELTS Speaking examiner. Provide detailed analysis and feedback.'
                },
                {
                  role: 'user',
                  content: prompt
                }
              ],
              temperature: 0.7,
              max_tokens: 16000,
              response_format: { type: "json_object" }
            })
          });

          // Handle non-OK responses
          if (!response.ok) {
            try {
              const errorData = await response.json();
              throw new Error(errorData.error?.message || `API request failed with status ${response.status}`);
            } catch (jsonError) {
              // Handle case where the error response is not valid JSON
              const errorText = await response.text().catch(() => 'Could not read error response');
              console.error('Non-JSON error response:', errorText);
              throw new Error(`API request failed with status ${response.status}: ${errorText.substring(0, 200)}...`);
            }
          }

          // Parse the response
          let data;
          try {
            data = await response.json();
          } catch (jsonError) {
            // Handle case where the response is not valid JSON
            const responseText = await response.text().catch(() => 'Could not read response body');
            console.error('Failed to parse API response as JSON:', responseText.substring(0, 500));
            throw new Error('Invalid JSON response from API');
          }

          // Check if the response has the expected structure
          if (!data.choices || !data.choices.length || !data.choices[0].message) {
            console.error('Unexpected API response format:', JSON.stringify(data));
            throw new Error('Unexpected API response format');
          }

          const content = data.choices[0].message.content;

          if (!content) {
            throw new Error('No content received from OpenAI');
          }

          // Clean potential markdown fences before parsing
          let cleanedContent = content.trim();

          // More robust cleaning of markdown and code blocks
          if (cleanedContent.startsWith('```json')) {
            cleanedContent = cleanedContent.substring(7); // Remove ```json
          } else if (cleanedContent.startsWith('```')) {
            cleanedContent = cleanedContent.substring(3); // Remove ```
          }

          if (cleanedContent.endsWith('```')) {
            cleanedContent = cleanedContent.substring(0, cleanedContent.length - 3); // Remove ```
          }

          cleanedContent = cleanedContent.trim(); // Trim again after removing fences

          // Parse the JSON content
          let result;
          try {
            result = JSON.parse(cleanedContent); // Parse the cleaned content
          } catch (firstError) {
            console.error('Initial JSON parsing error:', firstError);
            console.error('Content that failed to parse:', cleanedContent);

            // Try to fix common JSON issues
            try {
              // Replace any potential control characters or invalid JSON characters
              const sanitizedContent = cleanedContent.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');
              result = JSON.parse(sanitizedContent);
              console.log('Successfully parsed JSON after sanitizing content');
            } catch (secondError) {
              const parseError = secondError as Error;
              console.error('Failed to parse JSON even after sanitizing:', parseError);

              // Create a fallback result with error information
              throw new Error(`Failed to parse JSON response: ${parseError.message}`);
            }
          }

          // Validate scores
          try {
            // Check if overallScore exists and is a number
            if (typeof result.overallScore !== 'number') {
              console.warn('Invalid or missing overallScore, using default value');
              result.overallScore = 5; // Default score
            }

            // Check if criteria exists and is an array
            if (!Array.isArray(result.criteria)) {
              console.warn('Invalid or missing criteria array, creating default');
              result.criteria = [
                { name: 'Fluency and Coherence', score: 5, feedback: 'No feedback available', strengths: [], weaknesses: [], improvements: [] },
                { name: 'Lexical Resource', score: 5, feedback: 'No feedback available', strengths: [], weaknesses: [], improvements: [] },
                { name: 'Grammatical Range and Accuracy', score: 5, feedback: 'No feedback available', strengths: [], weaknesses: [], improvements: [] },
                { name: 'Pronunciation', score: 5, feedback: 'No feedback available', strengths: [], weaknesses: [], improvements: [] }
              ];
            }

            result.criteria.forEach((criterion: any) => {
              // Check if criterion score exists and is a number
              if (typeof criterion.score !== 'number') {
                console.warn(`Invalid or missing score for criterion ${criterion.name}, using default value`);
                criterion.score = 5; // Default score
              }
            });
          } catch (scoreError) {
            console.error('Error validating scores:', scoreError);
            // Continue with the result as is, don't throw an error here
          }

          // Store the result in the database
          try {
            // Get or create the student
            await getOrCreateStudent(studentId);

            // Check if the transcript is simulated
            const isSimulated = transcript.includes('SIMULATED TRANSCRIPT') ||
                               transcript.includes('HARDCODED TRANSCRIPT');

            // Apply inflation to the speaking band score
            const rawBand = result.overallScore;
            const inflatedBand = inflateBandScore(rawBand, 'speaking') as number;

            // Store the speaking entry
            await createSpeakingEntry({
              studentId,
              audioUrl,
              transcription: transcript,
              examinerContent: examinerContent || null,
              band: inflatedBand,
              criteriaScores: result.criteria,
              feedback: JSON.stringify(result),
              strengths: result.strengths || [],
              weaknesses: result.weaknesses || [],
              improvementSuggestions: result.improvements || [],
              isSimulated: isSimulated
            });
          } catch (dbError) {
            console.error('Error storing detailed speaking result in database:', dbError);
            // Continue with the response even if database storage fails
          }

          return result;
        } catch (error) {
          console.error('Error processing entry:', error);
          // Return a more detailed error message
          return {
            error: error instanceof Error ? error.message : 'Failed to process entry',
            status: 'error'
          };
        } finally {
          // Clear the timeout to prevent memory leaks
          clearTimeout(timeoutId);
        }
      })
    );

    return NextResponse.json({ results });
  } catch (error) {
    console.error('Error in bulk detailed speaking check:', error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    console.error('Error details:', errorMessage);

    return NextResponse.json(
      {
        error: errorMessage,
        timestamp: new Date().toISOString(),
        path: '/api/bulk-detailed-speaking-checker'
      },
      { status: 500 }
    );
  }
}

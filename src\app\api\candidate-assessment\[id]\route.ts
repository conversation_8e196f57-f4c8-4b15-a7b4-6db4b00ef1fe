import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// DELETE /api/candidate-assessment/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    // Check if student exists
    const existingStudent = await prisma.student.findUnique({
      where: { id },
    });

    if (!existingStudent) {
      return NextResponse.json({ error: 'Candidate not found' }, { status: 404 });
    }

    // Use a transaction to delete all related entries and then the student
    await prisma.$transaction(async (tx) => {
      // Delete all writing entries for this student
      await tx.writingEntry.deleteMany({
        where: { studentId: id },
      });

      // Delete all reading entries for this student
      await tx.readingEntry.deleteMany({
        where: { studentId: id },
      });

      // Delete all listening entries for this student
      await tx.listeningEntry.deleteMany({
        where: { studentId: id },
      });

      // Delete all speaking entries for this student
      await tx.speakingEntry.deleteMany({
        where: { studentId: id },
      });

      // Delete all reports for this student
      await tx.report.deleteMany({
        where: { studentId: id },
      });

      // Finally, delete the student
      await tx.student.delete({
        where: { id },
      });
    });

    return NextResponse.json({ message: 'Candidate deleted successfully' });
  } catch (error) {
    console.error('Error deleting candidate:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

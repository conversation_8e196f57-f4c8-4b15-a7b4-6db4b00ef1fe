// Add criteriaScores field to ListeningEntry table
const { PrismaClient } = require('../src/generated/prisma');

async function addCriteriaScores() {
  console.log('Adding criteriaScores field to ListeningEntry table...');
  
  try {
    // Create a new Prisma client instance
    const prisma = new PrismaClient();
    
    // Execute a raw SQL query to add the column
    try {
      await prisma.$executeRaw`ALTER TABLE "ListeningEntry" ADD COLUMN IF NOT EXISTS "criteriaScores" JSONB`;
      console.log('criteriaScores column added successfully!');
    } catch (sqlError) {
      console.error('Error adding criteriaScores column:', sqlError);
      throw sqlError;
    }
    
    // Disconnect from the database
    await prisma.$disconnect();
    
    return true;
  } catch (error) {
    console.error('Error adding criteriaScores field:', error);
    return false;
  }
}

// Run the script
addCriteriaScores()
  .then(success => {
    if (success) {
      console.log('criteriaScores field added successfully.');
    } else {
      console.error('Failed to add criteriaScores field.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });

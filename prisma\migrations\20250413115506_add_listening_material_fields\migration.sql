-- CreateTable
CREATE TABLE "Student" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "email" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Student_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WritingMaterial" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "taskType" TEXT NOT NULL,
    "taskQuestion" TEXT NOT NULL,
    "taskImage" TEXT,
    "sampleAnswer" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "WritingMaterial_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WritingEntry" (
    "id" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "materialId" TEXT,
    "taskType" TEXT NOT NULL,
    "essayText" TEXT NOT NULL,
    "taskQuestion" TEXT,
    "band" DOUBLE PRECISION,
    "criteriaScores" JSONB,
    "feedback" TEXT,
    "strengths" JSONB,
    "weaknesses" JSONB,
    "improvementSuggestions" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "WritingEntry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SpeakingEntry" (
    "id" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "audioUrl" TEXT,
    "transcription" TEXT,
    "partNumber" INTEGER,
    "examinerContent" TEXT,
    "band" DOUBLE PRECISION,
    "criteriaScores" JSONB,
    "feedback" TEXT,
    "strengths" JSONB,
    "weaknesses" JSONB,
    "improvementSuggestions" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SpeakingEntry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ReadingMaterial" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "passage" TEXT NOT NULL,
    "questions" JSONB NOT NULL,
    "answers" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReadingMaterial_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ReadingEntry" (
    "id" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "materialId" TEXT NOT NULL,
    "answers" JSONB NOT NULL,
    "imageUrl" TEXT,
    "score" INTEGER,
    "band" DOUBLE PRECISION,
    "correctAnswers" INTEGER,
    "totalQuestions" INTEGER,
    "mistakes" JSONB,
    "strengths" JSONB,
    "weaknesses" JSONB,
    "improvementSuggestions" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReadingEntry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ListeningMaterial" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "audioUrl" TEXT NOT NULL,
    "transcript" TEXT,
    "section" INTEGER,
    "questions" JSONB NOT NULL,
    "answers" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ListeningMaterial_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ListeningEntry" (
    "id" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "materialId" TEXT NOT NULL,
    "answers" JSONB NOT NULL,
    "imageUrl" TEXT,
    "score" INTEGER,
    "band" DOUBLE PRECISION,
    "correctAnswers" INTEGER,
    "totalQuestions" INTEGER,
    "mistakes" JSONB,
    "strengths" JSONB,
    "weaknesses" JSONB,
    "improvementSuggestions" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ListeningEntry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Report" (
    "id" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "candidateName" TEXT,
    "testDate" TIMESTAMP(3),
    "readingResult" JSONB,
    "listeningResult" JSONB,
    "writingResult" JSONB,
    "speakingResult" JSONB,
    "overallBand" DOUBLE PRECISION,
    "overallStrengths" JSONB,
    "overallWeaknesses" JSONB,
    "overallImprovements" JSONB,
    "printStatus" TEXT,
    "folderAssignment" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Report_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Student_email_key" ON "Student"("email");

-- AddForeignKey
ALTER TABLE "WritingEntry" ADD CONSTRAINT "WritingEntry_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "Student"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WritingEntry" ADD CONSTRAINT "WritingEntry_materialId_fkey" FOREIGN KEY ("materialId") REFERENCES "WritingMaterial"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SpeakingEntry" ADD CONSTRAINT "SpeakingEntry_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "Student"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReadingEntry" ADD CONSTRAINT "ReadingEntry_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "Student"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReadingEntry" ADD CONSTRAINT "ReadingEntry_materialId_fkey" FOREIGN KEY ("materialId") REFERENCES "ReadingMaterial"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ListeningEntry" ADD CONSTRAINT "ListeningEntry_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "Student"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ListeningEntry" ADD CONSTRAINT "ListeningEntry_materialId_fkey" FOREIGN KEY ("materialId") REFERENCES "ListeningMaterial"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Report" ADD CONSTRAINT "Report_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "Student"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

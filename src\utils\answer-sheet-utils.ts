/**
 * Utility functions for analyzing answer sheets using OpenAI GPT-4.1
 */

/**
 * Analyze an answer sheet image using OpenAI GPT-4.1
 * @param imageUrl URL or base64 data of the answer sheet image
 * @param componentType Type of the component (listening, reading, writing_task1, writing_task2, speaking)
 * @param studentId Student ID
 * @returns Analysis of the answer sheet
 */
export async function analyzeAnswerSheet(
  imageUrl: string,
  componentType: string,
  studentId: string
): Promise<any> {
  try {
    // If imageUrl is a URL, fetch the image and convert to blob
    let imageBlob: Blob;
    
    if (imageUrl.startsWith('data:')) {
      // Already a data URL, extract the blob
      const base64Data = imageUrl.split(',')[1];
      const byteCharacters = atob(base64Data);
      const byteArrays = [];
      
      for (let i = 0; i < byteCharacters.length; i++) {
        byteArrays.push(byteCharacters.charCodeAt(i));
      }
      
      const byteArray = new Uint8Array(byteArrays);
      imageBlob = new Blob([byteArray], { type: 'image/jpeg' });
    } else {
      // Fetch from URL
      const response = await fetch(imageUrl);
      imageBlob = await response.blob();
    }
    
    // Create form data for the API request
    const formData = new FormData();
    formData.append('image', imageBlob, 'answer-sheet.jpg');
    formData.append('componentType', componentType);
    formData.append('studentId', studentId);
    
    // Call the API endpoint
    const response = await fetch('/api/answer-sheet-analysis', {
      method: 'POST',
      body: formData
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to analyze answer sheet');
    }
    
    const data = await response.json();
    return data.analysis;
  } catch (error) {
    console.error('Error analyzing answer sheet:', error);
    throw error;
  }
}

/**
 * Fetch and analyze answer sheets for a candidate
 * @param candidate The candidate with components to analyze
 * @returns The candidate with updated components containing analysis
 */
export async function fetchAndAnalyzeAnswerSheets(candidate: any): Promise<any> {
  try {
    // Create a copy of the candidate
    const updatedCandidate = { ...candidate };
    
    // Process each component that has an answer sheet image
    for (const component of updatedCandidate.components) {
      if (component.answerSheetImageUrl) {
        try {
          // Analyze the answer sheet
          const analysis = await analyzeAnswerSheet(
            component.answerSheetImageUrl,
            component.type,
            updatedCandidate.studentId
          );
          
          // Update the component with the analysis
          component.answerSheetAnalysis = analysis;
        } catch (error) {
          console.error(`Error analyzing answer sheet for component ${component.id}:`, error);
          // Continue with other components even if one fails
        }
      }
    }
    
    return updatedCandidate;
  } catch (error) {
    console.error('Error fetching and analyzing answer sheets:', error);
    throw error;
  }
}

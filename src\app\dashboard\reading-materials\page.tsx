'use client';

import { useState, useEffect } from 'react';
import {
  Box,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  Alert,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Plus as PlusIcon,
  Pencil as EditIcon,
  Trash as DeleteIcon,
  Eye as ViewIcon,
} from '@phosphor-icons/react';

interface ReadingMaterial {
  id: string;
  title: string;
  passage: string;
  questions: any;
  answers: any;
  createdAt: string;
  updatedAt: string;
}

interface Question {
  id: string;
  text: string;
  type: 'multiple_choice' | 'true_false' | 'fill_in_blank' | 'matching';
  options?: string[];
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

export default function ReadingMaterialsPage() {
  const [materials, setMaterials] = useState<ReadingMaterial[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [viewDialog, setViewDialog] = useState(false);
  const [currentMaterial, setCurrentMaterial] = useState<Partial<ReadingMaterial>>({});
  const [isEditing, setIsEditing] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [answers, setAnswers] = useState<Record<string, string>>({});

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Fetch reading materials
  const fetchMaterials = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/reading-materials');
      if (!response.ok) {
        throw new Error('Failed to fetch reading materials');
      }
      const data = await response.json();
      setMaterials(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMaterials();
  }, []);

  // Handle form submission
  const handleSubmit = async () => {
    try {
      if (!currentMaterial.title || !currentMaterial.passage) {
        setError('Title and passage are required');
        return;
      }

      if (questions.length === 0) {
        setError('At least one question is required');
        return;
      }

      // Validate questions and answers
      for (const question of questions) {
        if (!question.text || !question.type) {
          setError('All questions must have text and type');
          return;
        }

        if (!answers[question.id]) {
          setError('All questions must have answers');
          return;
        }
      }

      const url = isEditing ? '/api/reading-materials' : '/api/reading-materials';
      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...currentMaterial,
          questions: questions,
          answers: answers,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save reading material');
      }

      // Refresh the materials list
      fetchMaterials();
      setOpenDialog(false);
      setCurrentMaterial({});
      setQuestions([]);
      setAnswers({});
      setTabValue(0);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    }
  };

  // Handle delete
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this reading material?')) {
      return;
    }

    try {
      const response = await fetch(`/api/reading-materials?id=${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete reading material');
      }

      // Refresh the materials list
      fetchMaterials();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    }
  };

  // Handle view
  const handleView = (material: ReadingMaterial) => {
    setCurrentMaterial(material);
    setQuestions(material.questions || []);
    setAnswers(material.answers || {});
    setViewDialog(true);
  };

  // Handle edit
  const handleEdit = (material: ReadingMaterial) => {
    setCurrentMaterial(material);
    setQuestions(material.questions || []);
    setAnswers(material.answers || {});
    setIsEditing(true);
    setOpenDialog(true);
  };

  // Handle add new
  const handleAddNew = () => {
    setCurrentMaterial({
      title: '',
      passage: '',
    });
    setQuestions([]);
    setAnswers({});
    setIsEditing(false);
    setOpenDialog(true);
    setTabValue(0);
  };

  // Handle add question
  const handleAddQuestion = () => {
    const newQuestion: Question = {
      id: `q-${Date.now()}`,
      text: '',
      type: 'multiple_choice',
      options: ['', '', '', ''],
    };
    setQuestions([...questions, newQuestion]);
  };

  // Handle update question
  const handleUpdateQuestion = (index: number, field: keyof Question, value: any) => {
    const updatedQuestions = [...questions];
    updatedQuestions[index] = {
      ...updatedQuestions[index],
      [field]: value,
    };
    setQuestions(updatedQuestions);
  };

  // Handle update question options
  const handleUpdateQuestionOption = (questionIndex: number, optionIndex: number, value: string) => {
    const updatedQuestions = [...questions];
    if (!updatedQuestions[questionIndex].options) {
      updatedQuestions[questionIndex].options = [];
    }
    updatedQuestions[questionIndex].options![optionIndex] = value;
    setQuestions(updatedQuestions);
  };

  // Handle update answer
  const handleUpdateAnswer = (questionId: string, value: string) => {
    setAnswers({
      ...answers,
      [questionId]: value,
    });
  };

  // Handle remove question
  const handleRemoveQuestion = (index: number) => {
    const updatedQuestions = [...questions];
    const questionId = updatedQuestions[index].id;
    updatedQuestions.splice(index, 1);
    setQuestions(updatedQuestions);

    // Remove answer for this question
    const updatedAnswers = { ...answers };
    delete updatedAnswers[questionId];
    setAnswers(updatedAnswers);
  };

  return (
    <Box>
      <Stack spacing={3}>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={8}>
            <Typography variant="h4">Reading Materials</Typography>
            <Typography color="text.secondary" sx={{ mt: 1 }}>
              Manage reading passages, questions, and answers for IELTS tests
            </Typography>
          </Grid>
          <Grid item xs={12} md={4} sx={{ textAlign: 'right' }}>
            <Button
              variant="contained"
              startIcon={<PlusIcon />}
              onClick={handleAddNew}
            >
              Add New Material
            </Button>
          </Grid>
        </Grid>

        {error && (
          <Alert severity="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        <Card>
          <CardContent>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : materials.length === 0 ? (
              <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                <Typography color="text.secondary">
                  No reading materials found. Add your first reading material to get started.
                </Typography>
              </Paper>
            ) : (
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Title</TableCell>
                      <TableCell>Questions</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {materials.map((material) => (
                      <TableRow key={material.id}>
                        <TableCell>{material.title}</TableCell>
                        <TableCell>{material.questions?.length || 0} questions</TableCell>
                        <TableCell>
                          {new Date(material.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell align="right">
                          <IconButton
                            size="small"
                            onClick={() => handleView(material)}
                            title="View"
                          >
                            <ViewIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleEdit(material)}
                            title="Edit"
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleDelete(material.id)}
                            title="Delete"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </CardContent>
        </Card>
      </Stack>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="lg" fullWidth>
        <DialogTitle>{isEditing ? 'Edit Reading Material' : 'Add New Reading Material'}</DialogTitle>
        <DialogContent>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="reading material tabs">
              <Tab label="Passage" />
              <Tab label="Questions & Answers" />
            </Tabs>
          </Box>

          <TabPanel value={tabValue} index={0}>
            <Stack spacing={2}>
              <TextField
                label="Title"
                fullWidth
                value={currentMaterial.title || ''}
                onChange={(e) => setCurrentMaterial({ ...currentMaterial, title: e.target.value })}
                required
              />
              <TextField
                label="Passage"
                fullWidth
                multiline
                rows={15}
                value={currentMaterial.passage || ''}
                onChange={(e) => setCurrentMaterial({ ...currentMaterial, passage: e.target.value })}
                required
                helperText="Enter the full reading passage text here"
              />
            </Stack>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Stack spacing={3}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">Questions</Typography>
                <Button
                  variant="outlined"
                  startIcon={<PlusIcon />}
                  onClick={handleAddQuestion}
                >
                  Add Question
                </Button>
              </Box>

              {questions.length === 0 ? (
                <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                  <Typography color="text.secondary">
                    No questions added yet. Click "Add Question" to get started.
                  </Typography>
                </Paper>
              ) : (
                questions.map((question, index) => (
                  <Paper key={question.id} variant="outlined" sx={{ p: 2 }}>
                    <Stack spacing={2}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="subtitle1">Question {index + 1}</Typography>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleRemoveQuestion(index)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>

                      <TextField
                        label="Question Text"
                        fullWidth
                        value={question.text}
                        onChange={(e) => handleUpdateQuestion(index, 'text', e.target.value)}
                        required
                      />

                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <TextField
                            select
                            label="Question Type"
                            fullWidth
                            value={question.type}
                            onChange={(e) => handleUpdateQuestion(index, 'type', e.target.value)}
                            SelectProps={{
                              native: true,
                            }}
                          >
                            <option value="multiple_choice">Multiple Choice</option>
                            <option value="true_false">True/False</option>
                            <option value="fill_in_blank">Fill in the Blank</option>
                            <option value="matching">Matching</option>
                          </TextField>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <TextField
                            label="Answer"
                            fullWidth
                            value={answers[question.id] || ''}
                            onChange={(e) => handleUpdateAnswer(question.id, e.target.value)}
                            required
                            helperText="For multiple choice, enter the correct option letter (A, B, C, D)"
                          />
                        </Grid>
                      </Grid>

                      {question.type === 'multiple_choice' && (
                        <Box>
                          <Typography variant="subtitle2" gutterBottom>Options</Typography>
                          <Grid container spacing={2}>
                            {['A', 'B', 'C', 'D'].map((letter, optionIndex) => (
                              <Grid item xs={12} md={6} key={optionIndex}>
                                <TextField
                                  label={`Option ${letter}`}
                                  fullWidth
                                  value={question.options?.[optionIndex] || ''}
                                  onChange={(e) => handleUpdateQuestionOption(index, optionIndex, e.target.value)}
                                />
                              </Grid>
                            ))}
                          </Grid>
                        </Box>
                      )}
                    </Stack>
                  </Paper>
                ))
              )}
            </Stack>
          </TabPanel>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleSubmit}>
            {isEditing ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Dialog */}
      <Dialog open={viewDialog} onClose={() => setViewDialog(false)} maxWidth="lg" fullWidth>
        <DialogTitle>View Reading Material</DialogTitle>
        <DialogContent>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="reading material tabs">
              <Tab label="Passage" />
              <Tab label="Questions & Answers" />
            </Tabs>
          </Box>

          <TabPanel value={tabValue} index={0}>
            <Stack spacing={3}>
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Title
                </Typography>
                <Typography variant="h6">{currentMaterial.title}</Typography>
              </Box>
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Passage
                </Typography>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                    {currentMaterial.passage}
                  </Typography>
                </Paper>
              </Box>
            </Stack>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Stack spacing={3}>
              {questions.length === 0 ? (
                <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                  <Typography color="text.secondary">
                    No questions available for this reading material.
                  </Typography>
                </Paper>
              ) : (
                questions.map((question, index) => (
                  <Paper key={question.id} variant="outlined" sx={{ p: 2 }}>
                    <Stack spacing={2}>
                      <Typography variant="subtitle1">Question {index + 1}</Typography>
                      <Typography variant="body1">{question.text}</Typography>

                      {question.type === 'multiple_choice' && (
                        <Box>
                          <Typography variant="subtitle2" gutterBottom>Options</Typography>
                          <Grid container spacing={2}>
                            {['A', 'B', 'C', 'D'].map((letter, optionIndex) => (
                              <Grid item xs={12} md={6} key={optionIndex}>
                                <Paper variant="outlined" sx={{ p: 1 }}>
                                  <Typography>
                                    <strong>{letter}:</strong> {question.options?.[optionIndex]}
                                  </Typography>
                                </Paper>
                              </Grid>
                            ))}
                          </Grid>
                        </Box>
                      )}

                      <Box>
                        <Typography variant="subtitle2" gutterBottom>Answer</Typography>
                        <Paper variant="outlined" sx={{ p: 1, bgcolor: 'success.light' }}>
                          <Typography color="success.contrastText">
                            {answers[question.id]}
                          </Typography>
                        </Paper>
                      </Box>
                    </Stack>
                  </Paper>
                ))
              )}
            </Stack>
          </TabPanel>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialog(false)}>Close</Button>
          <Button
            variant="outlined"
            onClick={() => {
              setViewDialog(false);
              handleEdit(currentMaterial as ReadingMaterial);
            }}
          >
            Edit
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

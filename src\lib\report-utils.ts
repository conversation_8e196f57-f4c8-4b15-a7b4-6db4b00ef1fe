import { prisma } from '@/lib/db';
import { inflateBandScore } from '@/utils/band-score-inflation';

/**
 * Calculate overall band score based on individual component scores
 *
 * @param readingBand - Reading band score
 * @param listeningBand - Listening band score
 * @param writingBand - Writing band score
 * @param speakingBand - Speaking band score
 * @returns Overall band score (average rounded to nearest 0.5)
 */
export function calculateOverallBand(
  readingBand?: number | null,
  listeningBand?: number | null,
  writingBand?: number | null,
  speakingBand?: number | null
): number | null {
  // Apply inflation to writing and speaking scores
  const inflatedWritingBand = writingBand !== null && writingBand !== undefined
    ? inflateBandScore(writingBand, 'writing_task1')
    : writingBand;

  const inflatedSpeakingBand = speakingBand !== null && speakingBand !== undefined
    ? inflateBandScore(speakingBand, 'speaking')
    : speakingBand;

  // Count how many valid band scores we have
  const validScores = [readingBand, listeningBand, inflatedWritingBand, inflatedSpeakingBand].filter(
    score => score !== undefined && score !== null
  ) as number[];

  // If no valid scores, return null
  if (validScores.length === 0) {
    return null;
  }

  // Calculate average
  const sum = validScores.reduce((acc, score) => acc + score, 0);
  const average = sum / validScores.length;

  // Round to nearest 0.5
  return Math.round(average * 2) / 2;
}

/**
 * Generate a comprehensive report for a student
 *
 * @param studentId - The student ID
 * @returns The generated report
 */
export async function generateReport(studentId: string) {
  // Get student
  const student = await prisma.student.findUnique({
    where: { id: studentId },
  });

  if (!student) {
    throw new Error(`Student with ID ${studentId} not found`);
  }

  // Get the latest test results for each component
  const latestWritingEntry = await prisma.writingEntry.findFirst({
    where: { studentId },
    orderBy: { createdAt: 'desc' },
  });

  const latestSpeakingEntry = await prisma.speakingEntry.findFirst({
    where: { studentId },
    orderBy: { createdAt: 'desc' },
  });

  const latestReadingEntry = await prisma.readingEntry.findFirst({
    where: { studentId },
    orderBy: { createdAt: 'desc' },
  });

  const latestListeningEntry = await prisma.listeningEntry.findFirst({
    where: { studentId },
    orderBy: { createdAt: 'desc' },
  });

  // Prepare result data for each component
  const writingResult = latestWritingEntry ? {
    band: latestWritingEntry.band,
    strengths: latestWritingEntry.strengths as string[],
    weaknesses: latestWritingEntry.weaknesses as string[],
    improvements: latestWritingEntry.improvementSuggestions as string[],
  } : null;

  const speakingResult = latestSpeakingEntry ? {
    band: latestSpeakingEntry.band,
    strengths: latestSpeakingEntry.strengths as string[],
    weaknesses: latestSpeakingEntry.weaknesses as string[],
    improvements: latestSpeakingEntry.improvementSuggestions as string[],
  } : null;

  const readingResult = latestReadingEntry ? {
    band: latestReadingEntry.band,
    strengths: latestReadingEntry.strengths as string[],
    weaknesses: latestReadingEntry.weaknesses as string[],
    improvements: latestReadingEntry.improvementSuggestions as string[],
  } : null;

  const listeningResult = latestListeningEntry ? {
    band: latestListeningEntry.band,
    strengths: latestListeningEntry.strengths as string[],
    weaknesses: latestListeningEntry.weaknesses as string[],
    improvements: latestListeningEntry.improvementSuggestions as string[],
  } : null;

  // Calculate overall band score
  const overallBand = calculateOverallBand(
    readingResult?.band || null,
    listeningResult?.band || null,
    writingResult?.band || null,
    speakingResult?.band || null
  );

  // Combine strengths, weaknesses, and improvements from all components
  const allStrengths = [
    ...(readingResult?.strengths || []),
    ...(listeningResult?.strengths || []),
    ...(writingResult?.strengths || []),
    ...(speakingResult?.strengths || []),
  ];

  const allWeaknesses = [
    ...(readingResult?.weaknesses || []),
    ...(listeningResult?.weaknesses || []),
    ...(writingResult?.weaknesses || []),
    ...(speakingResult?.weaknesses || []),
  ];

  const allImprovements = [
    ...(readingResult?.improvements || []),
    ...(listeningResult?.improvements || []),
    ...(writingResult?.improvements || []),
    ...(speakingResult?.improvements || []),
  ];

  // Remove duplicates and limit to top items
  const overallStrengths = Array.from(new Set(allStrengths)).slice(0, 5);
  const overallWeaknesses = Array.from(new Set(allWeaknesses)).slice(0, 5);
  const overallImprovements = Array.from(new Set(allImprovements)).slice(0, 5);

  // Create report
  const report = await prisma.report.create({
    data: {
      studentId,
      candidateName: student.name,
      testDate: new Date(),
      readingResult: readingResult ? JSON.stringify(readingResult) as any : undefined,
      listeningResult: listeningResult ? JSON.stringify(listeningResult) as any : undefined,
      writingResult: writingResult ? JSON.stringify(writingResult) as any : undefined,
      speakingResult: speakingResult ? JSON.stringify(speakingResult) as any : undefined,
      overallBand,
      overallStrengths: JSON.stringify(overallStrengths) as any,
      overallWeaknesses: JSON.stringify(overallWeaknesses) as any,
      overallImprovements: JSON.stringify(overallImprovements) as any,
      printStatus: 'pending',
    },
  });

  return {
    ...report,
    readingResult,
    listeningResult,
    writingResult,
    speakingResult,
    overallStrengths,
    overallWeaknesses,
    overallImprovements,
  };
}

/**
 * Get all reports
 *
 * @returns Array of all reports
 */
export async function getAllReports() {
  const reports = await prisma.report.findMany({
    orderBy: { createdAt: 'desc' },
    include: {
      student: true,
    },
  });

  // Parse JSON fields
  return reports.map(report => ({
    ...report,
    readingResult: report.readingResult ? JSON.parse(report.readingResult as string) : null,
    listeningResult: report.listeningResult ? JSON.parse(report.listeningResult as string) : null,
    writingResult: report.writingResult ? JSON.parse(report.writingResult as string) : null,
    speakingResult: report.speakingResult ? JSON.parse(report.speakingResult as string) : null,
    overallStrengths: report.overallStrengths ? JSON.parse(report.overallStrengths as string) : null,
    overallWeaknesses: report.overallWeaknesses ? JSON.parse(report.overallWeaknesses as string) : null,
    overallImprovements: report.overallImprovements ? JSON.parse(report.overallImprovements as string) : null,
  }));
}

/**
 * Get a report by ID
 *
 * @param id - The report ID
 * @returns The report or null if not found
 */
export async function getReportById(id: string) {
  const report = await prisma.report.findUnique({
    where: { id },
    include: {
      student: true,
    },
  });

  if (!report) {
    return null;
  }

  // Parse JSON fields
  return {
    ...report,
    readingResult: report.readingResult ? JSON.parse(report.readingResult as string) : null,
    listeningResult: report.listeningResult ? JSON.parse(report.listeningResult as string) : null,
    writingResult: report.writingResult ? JSON.parse(report.writingResult as string) : null,
    speakingResult: report.speakingResult ? JSON.parse(report.speakingResult as string) : null,
    overallStrengths: report.overallStrengths ? JSON.parse(report.overallStrengths as string) : null,
    overallWeaknesses: report.overallWeaknesses ? JSON.parse(report.overallWeaknesses as string) : null,
    overallImprovements: report.overallImprovements ? JSON.parse(report.overallImprovements as string) : null,
  };
}

/**
 * Update report status
 *
 * @param id - The report ID
 * @param printStatus - The print status ('pending' or 'printed')
 * @returns The updated report
 */
export async function updateReportStatus(id: string, printStatus: string) {
  return prisma.report.update({
    where: { id },
    data: { printStatus },
  });
}

/**
 * Assign report to folder
 *
 * @param id - The report ID
 * @param folderAssignment - The folder name
 * @returns The updated report
 */
export async function assignReportToFolder(id: string, folderAssignment: string) {
  return prisma.report.update({
    where: { id },
    data: { folderAssignment },
  });
}

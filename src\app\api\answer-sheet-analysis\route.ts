import { NextRequest, NextResponse } from 'next/server';

// Get API key from environment variables
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

if (!OPENAI_API_KEY) {
  throw new Error('OPENAI_API_KEY environment variable is not set');
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('image') as File;
    const componentType = formData.get('componentType') as string || 'unknown';
    const studentId = formData.get('studentId') as string || '';

    if (!file) {
      return NextResponse.json({ error: 'No image file provided' }, { status: 400 });
    }

    // Check if file is an image
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({ error: 'File must be an image' }, { status: 400 });
    }

    // Convert file to base64
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const base64Image = buffer.toString('base64');

    // Create system prompt based on component type
    let systemPrompt = "You are an expert IELTS examiner analyzing answer sheets. ";
    let criteriaPrompt = "";

    if (componentType === 'listening') {
      systemPrompt += "Analyze this IELTS Listening answer sheet and provide detailed feedback on the answers. Identify correct and incorrect answers, and explain why the incorrect answers are wrong. Focus on patterns of mistakes and provide specific advice for improvement.";
      criteriaPrompt = `For Listening, evaluate these specific criteria:
      1. Section 1 (Social) - Questions about everyday social contexts
      2. Section 2 (Training) - Questions about training or orientation contexts
      3. Section 3 (Academic) - Questions about academic discussions
      4. Section 4 (Academic Lecture) - Questions about academic monologues/lectures`;
    } else if (componentType === 'reading') {
      systemPrompt += "Analyze this IELTS Reading answer sheet and provide detailed feedback on the answers. Identify correct and incorrect answers, and explain why the incorrect answers are wrong. Focus on patterns of mistakes and provide specific advice for improvement.";
      criteriaPrompt = `For Reading, evaluate these specific criteria:
      1. Section 1 (Social/Training) - Questions about social or training contexts
      2. Section 2 (Workplace) - Questions about workplace contexts
      3. Section 3 (Academic) - Questions about academic texts`;
    } else if (componentType === 'writing_task1') {
      systemPrompt += "Analyze this IELTS Writing Task 1 answer sheet and provide detailed feedback on the writing. Evaluate the task achievement, coherence and cohesion, lexical resource, and grammatical range and accuracy. Identify strengths and weaknesses, and provide specific advice for improvement.";
      criteriaPrompt = `For Writing Task 1, evaluate these specific criteria:
      1. Task Achievement - How well the candidate addresses all parts of the task
      2. Coherence and Cohesion - How well the writing is organized and flows
      3. Lexical Resource - Vocabulary range and accuracy
      4. Grammatical Range and Accuracy - Grammar usage and accuracy`;
    } else if (componentType === 'writing_task2') {
      systemPrompt += "Analyze this IELTS Writing Task 2 answer sheet and provide detailed feedback on the writing. Evaluate the task response, coherence and cohesion, lexical resource, and grammatical range and accuracy. Identify strengths and weaknesses, and provide specific advice for improvement.";
      criteriaPrompt = `For Writing Task 2, evaluate these specific criteria:
      1. Task Response - How well the candidate addresses all parts of the task
      2. Coherence and Cohesion - How well the writing is organized and flows
      3. Lexical Resource - Vocabulary range and accuracy
      4. Grammatical Range and Accuracy - Grammar usage and accuracy`;
    } else if (componentType === 'speaking') {
      systemPrompt += "Analyze this IELTS Speaking assessment sheet and provide detailed feedback on the speaking performance. Evaluate the fluency and coherence, lexical resource, grammatical range and accuracy, and pronunciation. Identify strengths and weaknesses, and provide specific advice for improvement.";
      criteriaPrompt = `For Speaking, evaluate these specific criteria:
      1. Fluency and Coherence - How well the candidate speaks without hesitation
      2. Lexical Resource - Vocabulary range and accuracy
      3. Grammatical Range and Accuracy - Grammar usage and accuracy
      4. Pronunciation - Clarity and accuracy of pronunciation`;
    } else {
      systemPrompt += "Analyze this IELTS answer sheet and provide detailed feedback.";
    }

    // Call OpenAI API with GPT-4o (which has vision capabilities)
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        model: 'gpt-4o', // Using GPT-4o which has vision capabilities
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `Please analyze this IELTS ${componentType.replace('_', ' ')} answer sheet for student ID: ${studentId}. ${criteriaPrompt}

                Provide detailed feedback and analysis in JSON format with the following structure:
                {
                  "analysis": "Overall analysis of the answer sheet",
                  "strengths": ["Strength 1", "Strength 2", ...],
                  "weaknesses": ["Weakness 1", "Weakness 2", ...],
                  "improvementSuggestions": ["Suggestion 1", "Suggestion 2", ...],
                  "score": {
                    "overall": 7.5,
                    "criteria": [
                      {"name": "Criteria 1", "score": 7.0, "feedback": "Feedback for criteria 1"},
                      {"name": "Criteria 2", "score": 8.0, "feedback": "Feedback for criteria 2"},
                      ...
                    ]
                  }
                }

                Be sure to provide specific scores for each of the criteria mentioned above. The overall score should be between 0-9, with 9 being the highest. Be accurate and fair in your assessment.`
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:${file.type};base64,${base64Image}`
                }
              }
            ]
          }
        ],
        max_tokens: 4000,
        temperature: 0.5,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || 'Failed to analyze answer sheet');
    }

    const data = await response.json();
    const analysisContent = data.choices[0].message.content;

    let analysis;
    try {
      // Log the raw response for debugging
      console.log(`Raw GPT-4o response for ${componentType} (${studentId}):`);
      console.log(analysisContent.substring(0, 200) + '...');

      analysis = JSON.parse(analysisContent);

      // Log the parsed analysis structure
      console.log(`Parsed analysis for ${componentType} (${studentId}):`);
      console.log({
        hasAnalysis: !!analysis.analysis,
        hasStrengths: Array.isArray(analysis.strengths) ? analysis.strengths.length : 'not an array',
        hasWeaknesses: Array.isArray(analysis.weaknesses) ? analysis.weaknesses.length : 'not an array',
        hasScore: !!analysis.score,
        overallScore: analysis.score?.overall,
        criteriaCount: Array.isArray(analysis.score?.criteria) ? analysis.score.criteria.length : 'not an array'
      });

      // Ensure the analysis has the expected structure
      if (!analysis.score) {
        analysis.score = { overall: 0, criteria: [] };
      }

      if (!analysis.score.criteria || !Array.isArray(analysis.score.criteria)) {
        analysis.score.criteria = [];
      }

      if (!analysis.strengths || !Array.isArray(analysis.strengths)) {
        analysis.strengths = [];
      }

      if (!analysis.weaknesses || !Array.isArray(analysis.weaknesses)) {
        analysis.weaknesses = [];
      }

    } catch (e) {
      console.error('Error parsing AI response:', e);
      console.error('Raw content that failed to parse:', analysisContent.substring(0, 500));
      analysis = {
        analysis: 'Error parsing AI response',
        strengths: [],
        weaknesses: [],
        improvementSuggestions: [],
        score: {
          overall: 0,
          criteria: []
        }
      };
    }

    return NextResponse.json({
      analysis,
      componentType
    });

  } catch (error) {
    console.error('Error analyzing answer sheet:', error);

    // Special handling for auth errors
    if (error instanceof Error && error.message.includes('401')) {
      return NextResponse.json(
        { error: 'API key is invalid or expired', details: error.message },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to analyze answer sheet', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

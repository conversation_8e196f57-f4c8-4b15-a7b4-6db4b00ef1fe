import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function POST(req: NextRequest) {
  try {
    // Parse the request body
    const body = await req.json();
    const { componentType, correctAnswers } = body;

    console.log(`Applying global ${componentType} answers:`, correctAnswers);

    // Validate the input
    if (!componentType || !correctAnswers) {
      return NextResponse.json(
        { error: 'Component type and correct answers are required' },
        { status: 400 }
      );
    }

    // Validate component type
    if (componentType !== 'reading' && componentType !== 'listening') {
      return NextResponse.json(
        { error: 'Component type must be either "reading" or "listening"' },
        { status: 400 }
      );
    }

    // Parse the correct answers
    const correctAnswersArray = correctAnswers
      .split(',')
      .map((answer: string) => answer.trim())
      .filter((answer: string) => answer.length > 0);

    if (correctAnswersArray.length === 0) {
      return NextResponse.json(
        { error: 'No valid correct answers provided' },
        { status: 400 }
      );
    }

    let entries = [];
    let updateCount = 0;

    // Find all entries of the specified component type
    if (componentType === 'reading') {
      entries = await prisma.readingEntry.findMany({
        select: { id: true }
      });

      // Store the correct answers in the metadata field
      const metadata = {
        globalCorrectAnswers: correctAnswersArray
      };

      // Update all entries
      if (entries.length > 0) {
        const result = await prisma.readingEntry.updateMany({
          where: {
            id: {
              in: entries.map(entry => entry.id)
            }
          },
          data: {
            metadata: metadata
          }
        });
        updateCount = result.count;
      }
    } else if (componentType === 'listening') {
      entries = await prisma.listeningEntry.findMany({
        select: { id: true }
      });

      // Store the correct answers in the metadata field
      const metadata = {
        globalCorrectAnswers: correctAnswersArray
      };

      // Update all entries
      if (entries.length > 0) {
        const result = await prisma.listeningEntry.updateMany({
          where: {
            id: {
              in: entries.map(entry => entry.id)
            }
          },
          data: {
            metadata: metadata
          }
        });
        updateCount = result.count;
      }
    }

    console.log(`Found ${entries.length} ${componentType} entries to update`);

    // Return success response
    return NextResponse.json({
      success: true,
      message: `Applied global ${componentType} answers to ${updateCount} entries`,
      updatedCount: updateCount,
    });
  } catch (error) {
    console.error('Error applying global answers:', error);
    return NextResponse.json(
      { error: 'Failed to apply global answers' },
      { status: 500 }
    );
  }
}

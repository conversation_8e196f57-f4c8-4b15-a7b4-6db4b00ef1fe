import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { Prisma } from '@/generated/prisma';

// Helper function to group array items by a key
function groupBy(array: any[], key: string) {
  return array.reduce((result, item) => {
    const groupKey = item[key];
    if (!result[groupKey]) {
      result[groupKey] = [];
    }
    result[groupKey].push(item);
    return result;
  }, {});
}

// GET handler to fetch candidates with their test components
export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const page = url.searchParams.get('page');
    const pageSize = url.searchParams.get('pageSize');
    const searchQuery = url.searchParams.get('search');

    // Default to page 1 with 5 items per page
    const currentPage = page ? parseInt(page, 10) : 1;
    const itemsPerPage = pageSize ? parseInt(pageSize, 10) : 5;

    // Calculate skip value for pagination
    const skip = (currentPage - 1) * itemsPerPage;

    // Build where clause for search
    let whereClause: Prisma.StudentWhereInput = {};

    // Add search filter if provided
    if (searchQuery && searchQuery.trim() !== '') {
      whereClause = {
        id: {
          contains: searchQuery.trim(),
          mode: 'insensitive'
        }
      };
    }

    // Get total count for pagination metadata with search filter
    const totalCount = await prisma.student.count({
      where: whereClause
    });

    // Get paginated students with search filter
    const students = await prisma.student.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
      skip,
      take: itemsPerPage,
    });

    // Get student IDs for the current page
    const studentIds = students.map(student => student.id);

    // Fetch all entries in bulk with a single query for each type
    // Get writing entries for the current page students
    const allWritingEntries = await prisma.writingEntry.findMany({
      where: {
        studentId: { in: studentIds }
      },
      orderBy: { createdAt: 'desc' },
    });

    // Get reading entries for the current page students
    const allReadingEntries = await prisma.readingEntry.findMany({
      where: {
        studentId: { in: studentIds }
      },
      orderBy: { createdAt: 'desc' },
    });

    // Get listening entries for the current page students
    const allListeningEntries = await prisma.listeningEntry.findMany({
      where: {
        studentId: { in: studentIds }
      },
      orderBy: { createdAt: 'desc' },
    });

    // Get speaking entries for the current page students
    const allSpeakingEntries = await prisma.speakingEntry.findMany({
      where: {
        studentId: { in: studentIds }
      },
      orderBy: { createdAt: 'desc' },
    });

    // Get reports for the current page students
    const allReports = await prisma.report.findMany({
      where: {
        studentId: { in: studentIds }
      },
      orderBy: { createdAt: 'desc' },
    });

    // Group entries by student ID
    const writingEntriesByStudent = groupBy(allWritingEntries, 'studentId');
    const readingEntriesByStudent = groupBy(allReadingEntries, 'studentId');
    const listeningEntriesByStudent = groupBy(allListeningEntries, 'studentId');
    const speakingEntriesByStudent = groupBy(allSpeakingEntries, 'studentId');
    const reportsByStudent = groupBy(allReports, 'studentId');

    // Combine data for each student
    const studentsWithEntries = students.map(student => {
      return {
        ...student,
        writingEntries: (writingEntriesByStudent[student.id] || []).slice(0, 2),
        readingEntries: (readingEntriesByStudent[student.id] || []).slice(0, 1),
        listeningEntries: (listeningEntriesByStudent[student.id] || []).slice(0, 1),
        speakingEntries: (speakingEntriesByStudent[student.id] || []).slice(0, 1),
        reports: (reportsByStudent[student.id] || []).slice(0, 1),
      };
    });

    // Transform the data into the format needed by the frontend
    const candidates = studentsWithEntries.map(student => {
      // Determine if there are separate writing tasks or a combined one
      const writingTask1 = student.writingEntries.find((entry: any) => entry.taskType === 'task1');
      const writingTask2 = student.writingEntries.find((entry: any) => entry.taskType === 'task2');
      const combinedWriting = student.writingEntries.find((entry: any) => entry.taskType === 'both');

      // Create components array with type assertions to handle missing fields
      const components = [
        {
          id: 'listening',
          name: 'Listening',
          type: 'listening',
          status: student.listeningEntries.length > 0
            ? (student.listeningEntries[0].band ? 'processed' : 'uploaded')
            : 'pending',
          entryId: student.listeningEntries.length > 0 ? student.listeningEntries[0].id : null,
          band: student.listeningEntries.length > 0 ? student.listeningEntries[0].band : null,
          criteriaScores: student.listeningEntries.length > 0 ? student.listeningEntries[0].criteriaScores : null,
          strengths: student.listeningEntries.length > 0 ? student.listeningEntries[0].strengths : null,
          weaknesses: student.listeningEntries.length > 0 ? student.listeningEntries[0].weaknesses : null,
        },
        {
          id: 'reading',
          name: 'Reading',
          type: 'reading',
          status: student.readingEntries.length > 0
            ? (student.readingEntries[0].band ? 'processed' : 'uploaded')
            : 'pending',
          entryId: student.readingEntries.length > 0 ? student.readingEntries[0].id : null,
          band: student.readingEntries.length > 0 ? student.readingEntries[0].band : null,
          criteriaScores: student.readingEntries.length > 0 ? null : null, // ReadingEntry doesn't have criteriaScores
          strengths: student.readingEntries.length > 0 ? student.readingEntries[0].strengths : null,
          weaknesses: student.readingEntries.length > 0 ? student.readingEntries[0].weaknesses : null,
        },
        {
          id: 'writing_task1',
          name: 'Writing Task 1',
          type: 'writing_task1',
          status: writingTask1
            ? (writingTask1.band ? 'processed' : 'uploaded')
            : (combinedWriting ? (combinedWriting.band ? 'processed' : 'uploaded') : 'pending'),
          entryId: writingTask1 ? writingTask1.id : (combinedWriting ? combinedWriting.id : null),
          band: writingTask1 ? writingTask1.band : (combinedWriting ? combinedWriting.band : null),
          criteriaScores: writingTask1 ? (writingTask1 as any).criteriaScores : (combinedWriting ? (combinedWriting as any).criteriaScores : null),
          strengths: writingTask1 ? writingTask1.strengths : (combinedWriting ? combinedWriting.strengths : null),
          weaknesses: writingTask1 ? writingTask1.weaknesses : (combinedWriting ? combinedWriting.weaknesses : null),
        },
        {
          id: 'writing_task2',
          name: 'Writing Task 2',
          type: 'writing_task2',
          status: writingTask2
            ? (writingTask2.band ? 'processed' : 'uploaded')
            : (combinedWriting ? (combinedWriting.band ? 'processed' : 'uploaded') : 'pending'),
          entryId: writingTask2 ? writingTask2.id : (combinedWriting ? combinedWriting.id : null),
          band: writingTask2 ? writingTask2.band : (combinedWriting ? combinedWriting.band : null),
          criteriaScores: writingTask2 ? (writingTask2 as any).criteriaScores : (combinedWriting ? (combinedWriting as any).criteriaScores : null),
          strengths: writingTask2 ? writingTask2.strengths : (combinedWriting ? combinedWriting.strengths : null),
          weaknesses: writingTask2 ? writingTask2.weaknesses : (combinedWriting ? combinedWriting.weaknesses : null),
        },
        {
          id: 'speaking',
          name: 'Speaking',
          type: 'speaking',
          status: student.speakingEntries.length > 0
            ? (student.speakingEntries[0].band ? 'processed' : 'uploaded')
            : 'pending',
          entryId: student.speakingEntries.length > 0 ? student.speakingEntries[0].id : null,
          band: student.speakingEntries.length > 0 ? student.speakingEntries[0].band : null,
          criteriaScores: student.speakingEntries.length > 0 ? (student.speakingEntries[0] as any).criteriaScores : null,
          strengths: student.speakingEntries.length > 0 ? student.speakingEntries[0].strengths : null,
          weaknesses: student.speakingEntries.length > 0 ? student.speakingEntries[0].weaknesses : null,
        },
      ];

      return {
        id: student.id,
        studentId: student.id,
        name: student.name,
        components,
        reportGenerated: student.reports.length > 0,
        reportId: student.reports.length > 0 ? student.reports[0].id : null,
      };
    });

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / itemsPerPage);

    // Return candidates with pagination metadata
    return NextResponse.json({
      candidates,
      pagination: {
        currentPage,
        pageSize: itemsPerPage,
        totalCount,
        totalPages,
        hasNextPage: currentPage < totalPages,
        hasPreviousPage: currentPage > 1,
      }
    });
  } catch (error) {
    console.error('Error fetching candidates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch candidates' },
      { status: 500 }
    );
  }
}

// POST handler to create a new candidate
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { studentId, name } = body;

    // Validate input
    if (!studentId) {
      return NextResponse.json(
        { error: 'Student ID is required' },
        { status: 400 }
      );
    }

    // Check if student already exists
    const existingStudent = await prisma.student.findFirst({
      where: {
        OR: [
          { id: studentId },
          { name: name && name.trim() ? name : undefined },
        ],
      },
    });

    if (existingStudent) {
      return NextResponse.json(
        { error: 'Student already exists' },
        { status: 400 }
      );
    }

    // Create new student
    const student = await prisma.student.create({
      data: {
        id: studentId,
        name: name || null,
      },
    });

    return NextResponse.json(student);
  } catch (error) {
    console.error('Error creating candidate:', error);
    return NextResponse.json(
      { error: 'Failed to create candidate' },
      { status: 500 }
    );
  }
}

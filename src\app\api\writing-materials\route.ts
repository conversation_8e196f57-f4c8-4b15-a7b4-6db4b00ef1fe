import { NextRequest, NextResponse } from 'next/server';
import { 
  getAllWritingMaterials, 
  getWritingMaterialById, 
  createWritingMaterial, 
  updateWritingMaterial, 
  deleteWritingMaterial 
} from '@/lib/writing-material-utils';

// GET /api/writing-materials
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (id) {
      // Get a specific writing material
      const material = await getWritingMaterialById(id);
      
      if (!material) {
        return NextResponse.json({ error: 'Writing material not found' }, { status: 404 });
      }
      
      return NextResponse.json(material);
    } else {
      // Get all writing materials
      const materials = await getAllWritingMaterials();
      return NextResponse.json(materials);
    }
  } catch (error) {
    console.error('Error fetching writing materials:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// POST /api/writing-materials
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, taskType, taskQuestion, taskImage, sampleAnswer } = body;

    // Validate required fields
    if (!title || !taskType || !taskQuestion) {
      return NextResponse.json(
        { error: 'Title, task type, and task question are required' },
        { status: 400 }
      );
    }

    // Create new writing material
    const material = await createWritingMaterial({
      title,
      taskType,
      taskQuestion,
      taskImage,
      sampleAnswer,
    });

    return NextResponse.json(material, { status: 201 });
  } catch (error) {
    console.error('Error creating writing material:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// PUT /api/writing-materials
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, title, taskType, taskQuestion, taskImage, sampleAnswer } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json(
        { error: 'Writing material ID is required' },
        { status: 400 }
      );
    }

    // Check if writing material exists
    const existingMaterial = await getWritingMaterialById(id);
    if (!existingMaterial) {
      return NextResponse.json(
        { error: 'Writing material not found' },
        { status: 404 }
      );
    }

    // Update writing material
    const material = await updateWritingMaterial(id, {
      title,
      taskType,
      taskQuestion,
      taskImage,
      sampleAnswer,
    });

    return NextResponse.json(material);
  } catch (error) {
    console.error('Error updating writing material:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// DELETE /api/writing-materials
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Writing material ID is required' },
        { status: 400 }
      );
    }

    // Check if writing material exists
    const existingMaterial = await getWritingMaterialById(id);
    if (!existingMaterial) {
      return NextResponse.json(
        { error: 'Writing material not found' },
        { status: 404 }
      );
    }

    // Delete writing material
    await deleteWritingMaterial(id);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting writing material:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

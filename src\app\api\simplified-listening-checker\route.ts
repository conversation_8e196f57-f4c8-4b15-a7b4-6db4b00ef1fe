import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getOrCreateStudent } from '@/lib/student-utils';
import { createListeningEntryWithRawScore, updateListeningEntryWithRawScore } from '@/lib/listening-entry-utils';
import { getListeningMaterialById } from '@/lib/listening-material-utils';

// Define interfaces for type safety
interface SimplifiedListeningEntry {
  id?: string;
  studentId: string;
  materialId: string;
  raw_score: string;
  imageUrl?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { entries } = body;

    if (!entries || !Array.isArray(entries) || entries.length === 0) {
      return NextResponse.json({ error: 'Entries array is required' }, { status: 400 });
    }

    // Process each entry
    const results = await Promise.all(
      entries.map(async (entry: SimplifiedListeningEntry) => {
        try {
          const { studentId, materialId, raw_score, imageUrl } = entry;

          // Validate required fields
          if (!studentId) {
            throw new Error('Student ID is required');
          }

          if (!materialId) {
            throw new Error('Material ID is required');
          }

          if (!raw_score) {
            throw new Error('Raw score is required');
          }

          // Get or create student
          const student = await getOrCreateStudent(studentId);

          // Get the listening material (for validation)
          const material = await getListeningMaterialById(materialId);
          if (!material) {
            throw new Error(`Listening material with ID ${materialId} not found`);
          }

          // Create or update the entry
          let result;
          if (entry.id) {
            // Update existing entry
            result = await updateListeningEntryWithRawScore(entry.id, raw_score);
          } else {
            // Create new entry
            result = await createListeningEntryWithRawScore({
              studentId: student.id,
              materialId,
              raw_score,
              imageUrl
            });
          }

          return {
            id: result.id,
            studentId: result.studentId,
            materialId: result.materialId,
            raw_score: result.raw_score,
            band: result.band,
            strengths: result.strengths,
            weaknesses: result.weaknesses,
            improvementSuggestions: result.improvementSuggestions,
            status: 'completed'
          };
        } catch (error) {
          console.error('Error processing entry:', error);
          return {
            id: entry.id,
            error: error instanceof Error ? error.message : 'An unexpected error occurred',
            status: 'error'
          };
        }
      })
    );

    return NextResponse.json({ results });
  } catch (error) {
    console.error('Error in simplified listening checker:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

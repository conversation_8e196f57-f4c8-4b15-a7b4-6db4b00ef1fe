// Test script for reading materials functionality
const { PrismaClient } = require('../src/generated/prisma');
const prisma = new PrismaClient();

async function main() {
  console.log('Starting reading materials test...');

  try {
    // Test connection
    console.log('Testing database connection...');
    await prisma.$connect();
    console.log('✅ Database connection successful');

    // Test reading material creation
    console.log('\nTesting reading material creation...');
    const readingMaterial = await prisma.readingMaterial.create({
      data: {
        title: 'Test Reading Passage',
        passage: 'This is a test reading passage for the IELTS exam. It contains multiple paragraphs and is designed to test reading comprehension skills.',
        questions: [
          {
            id: 'q1',
            text: 'What is the purpose of this passage?',
            type: 'multiple_choice',
            options: [
              'To entertain readers',
              'To test reading comprehension',
              'To provide information about IELTS',
              'To teach English grammar'
            ]
          },
          {
            id: 'q2',
            text: 'This passage contains multiple paragraphs.',
            type: 'true_false'
          }
        ],
        answers: {
          q1: 'B',
          q2: 'True'
        }
      },
    });
    console.log(`✅ Reading material created with ID: ${readingMaterial.id}`);

    // Test reading material retrieval
    console.log('\nTesting reading material retrieval...');
    const retrievedMaterial = await prisma.readingMaterial.findUnique({
      where: { id: readingMaterial.id }
    });
    console.log(`✅ Retrieved reading material with title: ${retrievedMaterial.title}`);
    console.log(`✅ Reading material has ${retrievedMaterial.questions.length} questions`);

    // Test reading material update
    console.log('\nTesting reading material update...');
    const updatedMaterial = await prisma.readingMaterial.update({
      where: { id: readingMaterial.id },
      data: {
        title: 'Updated Test Reading Passage',
      }
    });
    console.log(`✅ Updated reading material title to: ${updatedMaterial.title}`);

    // Test data cleanup
    console.log('\nCleaning up test data...');
    await prisma.readingMaterial.delete({ where: { id: readingMaterial.id } });
    console.log('✅ Test data cleaned up successfully');

    console.log('\n🎉 All reading materials tests passed successfully!');
  } catch (error) {
    console.error('❌ Reading materials test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });

// Apply schema changes script
const { PrismaClient } = require('../src/generated/prisma');

async function applySchemaChanges() {
  console.log('Applying schema changes to the database...');
  
  try {
    // Create a new Prisma client instance
    const prisma = new PrismaClient();
    
    // Check if the criteriaScores column exists in the ListeningEntry table
    console.log('Checking if criteriaScores column exists in ListeningEntry table...');
    
    try {
      // Try to query a ListeningEntry with criteriaScores
      const entry = await prisma.listeningEntry.findFirst({
        select: {
          id: true,
          criteriaScores: true
        }
      });
      
      console.log('criteriaScores column already exists:', entry);
    } catch (error) {
      // If the column doesn't exist, we'll get an error
      console.log('criteriaScores column does not exist. Creating it...');
      
      // Execute a raw SQL query to add the column
      // Note: This is a workaround and should be used with caution
      try {
        await prisma.$executeRaw`ALTER TABLE "ListeningEntry" ADD COLUMN IF NOT EXISTS "criteriaScores" JSONB`;
        console.log('criteriaScores column added successfully!');
      } catch (sqlError) {
        console.error('Error adding criteriaScores column:', sqlError);
      }
    }
    
    // Disconnect from the database
    await prisma.$disconnect();
    
    return true;
  } catch (error) {
    console.error('Error applying schema changes:', error);
    return false;
  }
}

// Run the script
applySchemaChanges()
  .then(success => {
    if (success) {
      console.log('Schema changes applied successfully.');
    } else {
      console.error('Failed to apply schema changes.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });

import type { Icon } from '@phosphor-icons/react/dist/lib/types';
import { ChartPie as ChartPieIcon } from '@phosphor-icons/react/dist/ssr/ChartPie';
import { GearSix as GearSixIcon } from '@phosphor-icons/react/dist/ssr/GearSix';
import { PlugsConnected as PlugsConnectedIcon } from '@phosphor-icons/react/dist/ssr/PlugsConnected';
import { User as UserIcon } from '@phosphor-icons/react/dist/ssr/User';
import { Users as UsersIcon } from '@phosphor-icons/react/dist/ssr/Users';
import { XSquare } from '@phosphor-icons/react/dist/ssr/XSquare';
import { Pencil as PencilIcon } from '@phosphor-icons/react/dist/ssr/Pencil';
import { Stack as StackIcon } from '@phosphor-icons/react/dist/ssr/Stack';
import { Microphone as MicrophoneIcon } from '@phosphor-icons/react/dist/ssr/Microphone';
import { Speaker<PERSON>igh as SpeakerHighIcon } from '@phosphor-icons/react/dist/ssr/SpeakerHigh';
import { Scan as ScanIcon } from '@phosphor-icons/react/dist/ssr/Scan';
import { MagnifyingGlass as MagnifyingGlassIcon } from '@phosphor-icons/react/dist/ssr/MagnifyingGlass';
import { Files as FilesIcon } from '@phosphor-icons/react/dist/ssr/Files';
import { FileText as FileTextIcon } from '@phosphor-icons/react/dist/ssr/FileText';

export const navIcons = {
  'chart-pie': ChartPieIcon,
  'gear-six': GearSixIcon,
  'plugs-connected': PlugsConnectedIcon,
  'x-square': XSquare,
  user: UserIcon,
  users: UsersIcon,
  pencil: PencilIcon,
  stack: StackIcon,
  microphone: MicrophoneIcon,
  'speaker-high': SpeakerHighIcon,
  scan: ScanIcon,
  'magnifying-glass': MagnifyingGlassIcon,
  'files': FilesIcon,
  'file-text': FileTextIcon,
} as Record<string, Icon>;

# TestHub Refactoring Plan

## 1. Simplify Listening and Reading Assessment

### Current Issues
- Complex answer checking logic causing problems with:
  - Misspellings
  - Incorrect logic
  - Cumbersome implementation
  
### Proposed Changes
- Replace automated checking with manual score input
- Implement simple score-to-band conversion
- Remove existing answer checking logic

#### Implementation Details
1. Update Database Schema
   - Simplify ListeningEntry and ReadingEntry models
   - Remove answer checking related fields
   - Add raw score field (e.g., "30/40")
   - Add band score field (automatically calculated)

2. Create Score Conversion Logic
   - Implement score-to-band conversion function
   - Example mapping:
     ```typescript
     const scoreToBandMap = {
       reading: {
         '39-40': 9.0, '37-38': 8.5, '35-36': 8.0,
         '33-34': 7.5, '30-32': 7.0, '27-29': 6.5,
         '23-26': 6.0, '20-22': 5.5, '15-19': 5.0,
         '13-14': 4.5, '10-12': 4.0, '8-9': 3.5,
         '6-7': 3.0, '4-5': 2.5, '2-3': 2.0
       },
       listening: {
         // Similar mapping for listening scores
       }
     };
     ```

3. Update UI
   - Replace answer input with simple score input field
   - Show calculated band score immediately
   - Remove answer checking related components

## 2. Optimize File Handling

### Current Issues
- Performance issues with large numbers of uploads
- Redundant storage of files in database
- Slow fetching of large files

### Proposed Changes
- Handle files locally during processing
- Store only essential data in database
- Remove file storage from database operations

#### Implementation Details
1. Writing Assessment
   - Process images locally
   - Extract text and generate feedback
   - Store only:
     - Final text
     - Scores
     - Feedback
     - Student ID
     - Timestamp

2. Speaking Assessment
   - Process audio files locally
   - Generate transcript and feedback
   - Store only:
     - Final transcript
     - Scores
     - Feedback
     - Student ID
     - Timestamp

3. Update API Routes
   - Modify upload endpoints to process files locally
   - Implement temporary file cleanup
   - Optimize database operations

## 3. Standardize Feedback Generation

### Current Issues
- Inconsistent feedback generation
- Missing feedback in some components
- Hardcoded feedback in reports

### Proposed Changes
- Implement consistent feedback generation across all components
- Standardize feedback structure
- Ensure all components generate proper feedback before PDF creation

#### Implementation Details
1. Create Standardized Feedback Structure
   ```typescript
   interface StandardizedFeedback {
     band: number;
     criteriaScores: {
       [key: string]: number;
     };
     analysis: string;
     strengths: string[];
     weaknesses: string[];
     improvements: string[];
   }
   ```

2. Update Feedback Generation
   - Writing Assessment:
     - Task achievement
     - Coherence and cohesion
     - Lexical resource
     - Grammatical range and accuracy

   - Speaking Assessment:
     - Fluency and coherence
     - Lexical resource
     - Grammatical range and accuracy
     - Pronunciation

   - Reading/Listening Assessment:
     - Overall band score
     - General performance analysis
     - Improvement suggestions

3. Implement PDF Generation
   - Create consistent template
   - Include all feedback components
   - Add visual elements (charts, tables)
   - Ensure professional formatting

## Implementation Priority

1. Score Simplification (High Priority)
   - Implement manual score input
   - Create score conversion logic
   - Update UI components

2. File Handling Optimization (High Priority)
   - Modify file processing logic
   - Update database operations
   - Implement cleanup routines

3. Feedback Standardization (Medium Priority)
   - Create feedback templates
   - Update feedback generation
   - Implement PDF generation

## Technical Tasks

1. Database Updates
   ```sql
   -- Example schema modifications
   ALTER TABLE "ListeningEntry" DROP COLUMN answers;
   ALTER TABLE "ListeningEntry" ADD COLUMN raw_score TEXT;
   ALTER TABLE "ListeningEntry" ADD COLUMN band_score DECIMAL;
   ```

2. API Updates
   - Modify `/api/bulk-listening-checker`
   - Modify `/api/bulk-reading-checker`
   - Update file handling in writing/speaking checkers

3. Frontend Updates
   - Update score input components
   - Modify file upload handling
   - Update feedback display components

## Testing Plan

1. Score Conversion Testing
   - Test all possible score ranges
   - Verify band score calculations
   - Test edge cases

2. File Processing Testing
   - Test with various file sizes
   - Verify cleanup procedures
   - Test concurrent uploads

3. Feedback Generation Testing
   - Verify feedback consistency
   - Test PDF generation
   - Validate report formatting
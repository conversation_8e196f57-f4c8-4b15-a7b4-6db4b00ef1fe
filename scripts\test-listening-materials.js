// Test script for listening materials functionality
const { PrismaClient } = require('../src/generated/prisma');
const prisma = new PrismaClient();

async function main() {
  console.log('Starting listening materials test...');

  try {
    // Test connection
    console.log('Testing database connection...');
    await prisma.$connect();
    console.log('✅ Database connection successful');

    // Test listening material creation
    console.log('\nTesting listening material creation...');
    const listeningMaterial = await prisma.listeningMaterial.create({
      data: {
        title: 'Test Listening Material',
        audioUrl: 'https://example.com/test-audio.mp3',
        transcript: 'This is a test transcript for the IELTS listening test.',
        section: 2,
        questions: [
          {
            id: 'q1',
            text: 'What is the main topic of the conversation?',
            type: 'multiple_choice',
            options: [
              'Travel plans',
              'Academic research',
              'Job interview',
              'Housing arrangement'
            ]
          },
          {
            id: 'q2',
            text: 'The speaker mentions taking a train to London.',
            type: 'true_false'
          }
        ],
        answers: {
          q1: 'A',
          q2: 'False'
        }
      },
    });
    console.log(`✅ Listening material created with ID: ${listeningMaterial.id}`);

    // Test listening material retrieval
    console.log('\nTesting listening material retrieval...');
    const retrievedMaterial = await prisma.listeningMaterial.findUnique({
      where: { id: listeningMaterial.id }
    });
    console.log(`✅ Retrieved listening material with title: ${retrievedMaterial.title}`);
    console.log(`✅ Listening material has ${retrievedMaterial.questions.length} questions`);
    console.log(`✅ Listening material is for section: ${retrievedMaterial.section}`);
    console.log(`✅ Listening material has transcript: ${retrievedMaterial.transcript ? 'Yes' : 'No'}`);

    // Test listening material update
    console.log('\nTesting listening material update...');
    const updatedMaterial = await prisma.listeningMaterial.update({
      where: { id: listeningMaterial.id },
      data: {
        title: 'Updated Test Listening Material',
      }
    });
    console.log(`✅ Updated listening material title to: ${updatedMaterial.title}`);

    // Test data cleanup
    console.log('\nCleaning up test data...');
    await prisma.listeningMaterial.delete({ where: { id: listeningMaterial.id } });
    console.log('✅ Test data cleaned up successfully');

    console.log('\n🎉 All listening materials tests passed successfully!');
  } catch (error) {
    console.error('❌ Listening materials test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });

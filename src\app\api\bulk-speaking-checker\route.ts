import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getOrCreateStudent } from '@/lib/student-utils';
import { createSpeakingEntry } from '@/lib/speaking-utils';
import { inflateBandScore } from '@/utils/band-score-inflation';

// Get API key from environment variables
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

if (!OPENAI_API_KEY) {
  throw new Error('OPENAI_API_KEY environment variable is not set');
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { entries, model } = body;

    if (!entries || !Array.isArray(entries) || entries.length === 0) {
      return NextResponse.json({ error: 'At least one entry is required' }, { status: 400 });
    }

    if (!model) {
      return NextResponse.json({ error: 'Model is required' }, { status: 400 });
    }

    // Check if studentId is provided for each entry
    for (let i = 0; i < entries.length; i++) {
      if (!entries[i].studentId) {
        entries[i].studentId = `anonymous-${Date.now()}-${i}`; // Generate a temporary ID if not provided
      }
    }

    // Process each entry one by one
    const results = await Promise.all(
      entries.map(async (entry) => {
        try {
          const { transcript, examinerContent, taskType = 'mock_test', partNumber, studentId, audioUrl } = entry;

          if (!transcript) {
            throw new Error('Transcript is required');
          }

          // partNumber = 0 means all parts (full test)
          if (partNumber !== 0 && ![1, 2, 3].includes(partNumber)) {
            throw new Error('Valid IELTS speaking part number (0 for all parts, or 1, 2, or 3) is required');
          }

          // Customize the system prompt based on the task type and available content
          let systemPrompt = `You are an expert IELTS speaking examiner. Evaluate the following speaking transcript according to the official IELTS speaking scoring criteria and provide comprehensive feedback in a structured JSON format.`;

          if (taskType === 'mock_test') {
            systemPrompt += `\n\nThis is a complete IELTS speaking mock test.`;
          } else if (taskType === 'part_only') {
            systemPrompt += `\n\nThis is a specific part of the IELTS speaking test (Part ${partNumber}).`;
          } else if (taskType === 'practice_question') {
            systemPrompt += `\n\nThis is a practice response to an IELTS speaking question.`;
          }

          if (examinerContent) {
            systemPrompt += `\n\nThe conversation includes both examiner questions and candidate responses. The examiner's questions are provided for context, but you should only evaluate the candidate's responses.

Examiner's questions and prompts:
${examinerContent}

Candidate's responses (to evaluate):
${transcript}`;
          } else {
            systemPrompt += `\n\nCandidate's responses (to evaluate):
${transcript}`;
          }

          // If partNumber is 0, we're evaluating all parts
          if (partNumber === 0) {
            systemPrompt += `\n\nEvaluate the full IELTS speaking test covering all three parts.`;
          } else {
            systemPrompt += `\n\nSpeaking Part: ${partNumber}`;
          }

          systemPrompt += `\n\nProvide feedback in the following JSON format:
{
  "overallScore": number,
  "criteria": [
    {
      "name": "Fluency and Coherence",
      "score": number,
      "feedback": "detailed explanation",
      "strengths": ["strength1", "strength2"],
      "weaknesses": ["weakness1", "weakness2"],
      "improvements": ["improvement1", "improvement2"]
    },
    {
      "name": "Lexical Resource",
      "score": number,
      "feedback": "detailed explanation",
      "strengths": ["strength1", "strength2"],
      "weaknesses": ["weakness1", "weakness2"],
      "improvements": ["improvement1", "improvement2"]
    },
    {
      "name": "Grammatical Range and Accuracy",
      "score": number,
      "feedback": "detailed explanation",
      "strengths": ["strength1", "strength2"],
      "weaknesses": ["weakness1", "weakness2"],
      "improvements": ["improvement1", "improvement2"]
    },
    {
      "name": "Pronunciation",
      "score": number,
      "feedback": "detailed explanation",
      "strengths": ["strength1", "strength2"],
      "weaknesses": ["weakness1", "weakness2"],
      "improvements": ["improvement1", "improvement2"]
    }
  ],
  "speechAnalysis": {
    "errorsByType": {
      "grammarErrors": [{"original": "incorrect phrase", "correction": "corrected phrase", "explanation": "grammar rule explanation"}],
      "pronunciationIssues": [{"word": "mispronounced word", "correctPronunciation": "phonetic representation", "tip": "how to improve"}],
      "intonationPatterns": [{"pattern": "described pattern", "impact": "how it affects comprehension", "improvement": "suggestion"}],
      "fillers": [{"filler": "um/uh/like/etc", "frequency": "count", "alternatives": ["pause silently", "other suggestion"]}]
    },
    "speechStrengths": ["clear articulation of key points", "good use of stress for emphasis"],
    "totalErrors": number,
    "errorFrequency": "errors per minute",
    "mostFrequentErrorType": "the category with most errors"
  },
  "vocabularyAnalysis": {
    "overusedWords": [{"word": "basic word", "count": number, "alternatives": ["better alternative1", "better alternative2"]}],
    "lexicalDiversity": {
      "uniqueWords": number,
      "totalWords": number,
      "diversityScore": number
    },
    "vocabularyLevel": {
      "distribution": {
        "basic": "percentage",
        "intermediate": "percentage",
        "advanced": "percentage"
      }
    }
  },
  "improvementSuggestions": [
    {
      "original": "original problematic phrase",
      "improved": "improved version",
      "explanation": "why this is better"
    }
  ],
  ${partNumber === 0 ?
    `"partSpecificFeedback": {
      "part1": {
        "personalQuestions": "assessment of responses to personal questions",
        "elaboration": "how well candidate expanded on simple questions"
      },
      "part2": {
        "longTurnOrganization": "structure of the 2-minute long turn",
        "topicCoverage": "how well the candidate covered the topic",
        "timeManagement": "how well the candidate managed the 2-minute time constraint"
      },
      "part3": {
        "analyticalSkills": "assessment of analytical thinking",
        "abstractConcepts": "ability to discuss abstract concepts",
        "opinionJustification": "how well opinions were supported"
      }
    },` :
    `"partSpecificFeedback": {
      ${partNumber === 1 ?
        `"part1": {
          "personalQuestions": "assessment of responses to personal questions",
          "elaboration": "how well candidate expanded on simple questions"
        }` :
      partNumber === 2 ?
        `"part2": {
          "longTurnOrganization": "structure of the 2-minute long turn",
          "topicCoverage": "how well the candidate covered the topic",
          "timeManagement": "how well the candidate managed the 2-minute time constraint"
        }` :
        `"part3": {
          "analyticalSkills": "assessment of analytical thinking",
          "abstractConcepts": "ability to discuss abstract concepts",
          "opinionJustification": "how well opinions were supported"
        }`
      }
    },`
  }
  "bandScoreJustification": "explanation of why this performance achieves the given band score"
}

The scores should range from 0-9, where 9 is the highest score. Be thorough but concise in your feedback.`;

          const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${OPENAI_API_KEY}`,
              'OpenAI-Beta': 'assistants=v1'
            },
            body: JSON.stringify({
              model,
              messages: [
                {
                  role: 'system',
                  content: systemPrompt
                },
                {
                  role: 'user',
                  content: 'Please analyze this speaking performance and provide detailed feedback according to the IELTS criteria.'
                }
              ],
              temperature: 0.7,
              max_tokens: 2000,
              response_format: { type: "json_object" }
            })
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error?.message || 'Failed to check speaking');
          }

          const data = await response.json();
          const resultContent = data.choices[0].message.content;
          const result = JSON.parse(resultContent);

          // Store the result in the database
          try {
            // Get or create the student
            await getOrCreateStudent(studentId);

            // Check if the transcript is simulated
            const isSimulated = transcript.includes('SIMULATED TRANSCRIPT') ||
                               transcript.includes('HARDCODED TRANSCRIPT');

            // Store the speaking entry
            await createSpeakingEntry({
              studentId,
              audioUrl,
              transcription: transcript,
              partNumber: partNumber || null,
              examinerContent: examinerContent || null,
              band: result.overallScore,
              criteriaScores: result.criteria,
              feedback: resultContent,
              strengths: result.strengths || [],
              weaknesses: result.weaknesses || [],
              improvementSuggestions: result.improvements || [],
              isSimulated: isSimulated
            });
          } catch (dbError) {
            console.error('Error storing speaking result in database:', dbError);
            // Continue with the response even if database storage fails
          }

          return {
            choices: [
              {
                message: {
                  content: resultContent
                }
              }
            ]
          };
        } catch (error) {
          console.error('Error processing entry:', error);
          return {
            error: error instanceof Error ? error.message : 'An unexpected error occurred',
            choices: [{ message: { content: JSON.stringify({ error: 'Failed to analyze this entry' }) } }]
          };
        }
      })
    );

    return NextResponse.json({ results });
  } catch (error) {
    console.error('Error in bulk speaking checker API:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}
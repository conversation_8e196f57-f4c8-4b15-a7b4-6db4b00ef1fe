// Test listening entry processing
const { PrismaClient } = require('../src/generated/prisma');
const { processListeningEntrySimple } = require('../src/lib/simple-listening-utils');

async function testListeningProcessing() {
  console.log('Testing listening entry processing...');
  
  try {
    // Create a new Prisma client instance
    const prisma = new PrismaClient();
    
    // Create a test student
    const student = await prisma.student.create({
      data: {
        name: 'Test Student',
        email: `test-${Date.now()}@example.com`,
      }
    });
    console.log('Created test student:', student);
    
    // Create a test listening material
    const material = await prisma.listeningMaterial.create({
      data: {
        title: 'Test Listening Material',
        audioUrl: 'https://example.com/test-audio.mp3',
        questions: {},
        answers: ['A', 'B', 'C', 'D', 'E']
      }
    });
    console.log('Created test listening material:', material);
    
    // Create a test listening entry
    const entry = await prisma.listeningEntry.create({
      data: {
        studentId: student.id,
        materialId: material.id,
        answers: ['A', 'B', 'X', 'D', 'Y']
      }
    });
    console.log('Created test listening entry:', entry);
    
    // Process the entry
    const processedEntry = await processListeningEntrySimple({
      ...entry,
      material
    });
    console.log('Processed listening entry:', processedEntry);
    
    // Clean up
    await prisma.listeningEntry.delete({
      where: { id: entry.id }
    });
    await prisma.listeningMaterial.delete({
      where: { id: material.id }
    });
    await prisma.student.delete({
      where: { id: student.id }
    });
    
    // Disconnect from the database
    await prisma.$disconnect();
    
    return true;
  } catch (error) {
    console.error('Error testing listening processing:', error);
    return false;
  }
}

// Run the script
testListeningProcessing()
  .then(success => {
    if (success) {
      console.log('Listening processing test completed successfully.');
    } else {
      console.error('Listening processing test failed.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });

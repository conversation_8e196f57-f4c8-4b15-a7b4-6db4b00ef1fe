// This script handles Prisma setup before building the application
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Running Prisma setup...');

// Create a temporary schema file without the output directive
// This is to avoid permission issues with the generated client
try {
  const schemaPath = path.join(__dirname, 'prisma', 'schema.prisma');
  const tempSchemaPath = path.join(__dirname, 'prisma', 'temp-schema.prisma');

  // Read the original schema
  const originalSchema = fs.readFileSync(schemaPath, 'utf8');

  // Modify the schema to remove the output directive
  const modifiedSchema = originalSchema.replace(
    /generator client {[^}]*}/,
    'generator client {\n  provider = "prisma-client-js"\n}'
  );

  // Write the modified schema to a temporary file
  fs.writeFileSync(tempSchemaPath, modifiedSchema);

  console.log('Created temporary schema file without output directive');

  try {
    // Generate Prisma client
    console.log('Generating Prisma client...');
    execSync('npx prisma generate --schema=./prisma/temp-schema.prisma', { stdio: 'inherit' });
    console.log('Prisma client generated successfully');

    // Run database migrations if needed
    console.log('Running database migrations...');
    try {
      execSync('npx prisma migrate deploy --schema=./prisma/temp-schema.prisma', { stdio: 'inherit' });
      console.log('Database migrations applied successfully');
    } catch (migrateError) {
      console.warn('Warning: Migration may have failed, but continuing build:', migrateError.message);
      // Continue with the build even if migrations fail
    }
  } finally {
    // Clean up the temporary schema file
    try {
      fs.unlinkSync(tempSchemaPath);
      console.log('Removed temporary schema file');
    } catch (cleanupError) {
      console.warn('Warning: Failed to clean up temporary schema file:', cleanupError.message);
    }
  }
} catch (error) {
  console.error('Error during Prisma setup:', error);
  // Continue with the build even if Prisma setup fails
  // process.exit(1); // Commented out to allow build to continue
}

'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Stack,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
// Replace Phosphor icons with MUI icons
import ArticleIcon from '@mui/icons-material/Article';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import DeleteIcon from '@mui/icons-material/Delete';
import DownloadIcon from '@mui/icons-material/Download';
import EditIcon from '@mui/icons-material/Edit';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import AddIcon from '@mui/icons-material/Add';
import UploadIcon from '@mui/icons-material/Upload';
import CancelIcon from '@mui/icons-material/Cancel';
import { useDropzone } from 'react-dropzone';
import Papa from 'papaparse';

// Types
interface ReadingMaterial {
  id: string;
  title: string;
  passage: string;
  questions: Question[];
  answers: Record<string, string>;
  createdAt: string;
  updatedAt: string;
}

interface Question {
  id: string;
  text: string;
  type: string;
  options?: string[];
}

interface ReadingEntry {
  id: string;
  studentId: string;
  materialId: string;
  answers: Record<string, string>;
  imageUrl?: string;
  status?: 'pending' | 'processing' | 'completed' | 'error';
  score?: number;
  band?: number;
  correctAnswers?: number;
  totalQuestions?: number;
  mistakes?: Array<{
    questionId: string;
    questionText: string;
    userAnswer: string;
    correctAnswer: string;
    explanation?: string;
  }>;
  analysis?: string;
  strengths?: string[];
  weaknesses?: string[];
  improvementSuggestions?: string[];
  error?: string;
}

type ModelType = 'gpt-4o' | 'gpt-4-turbo';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function BulkReadingCheckerPage() {
  // State
  const [entries, setEntries] = useState<ReadingEntry[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedModel, setSelectedModel] = useState<ModelType>('gpt-4o');
  const [selectedEntry, setSelectedEntry] = useState<ReadingEntry | null>(null);
  const [showAddForm, setShowAddForm] = useState<boolean>(false);
  const [exportMenuAnchorEl, setExportMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [tabValue, setTabValue] = useState(0);
  const [globalStudentId, setGlobalStudentId] = useState<string>('');
  const [fileProcessingProgress, setFileProcessingProgress] = useState<{[key: string]: number}>({});
  const [readingMaterials, setReadingMaterials] = useState<ReadingMaterial[]>([]);
  const [loadingMaterials, setLoadingMaterials] = useState<boolean>(false);
  const [selectedMaterial, setSelectedMaterial] = useState<ReadingMaterial | null>(null);
  const [newEntryStudentId, setNewEntryStudentId] = useState<string>('');
  const [newEntryMaterialId, setNewEntryMaterialId] = useState<string>('');
  const [newEntryAnswers, setNewEntryAnswers] = useState<Record<string, string>>({});
  const [showResultDialog, setShowResultDialog] = useState<boolean>(false);
  const [resultDialogEntry, setResultDialogEntry] = useState<ReadingEntry | null>(null);

  // Fetch reading materials on component mount
  useEffect(() => {
    const fetchReadingMaterials = async () => {
      try {
        setLoadingMaterials(true);
        const response = await fetch('/api/reading-materials');
        if (!response.ok) {
          throw new Error('Failed to fetch reading materials');
        }
        const data = await response.json();
        setReadingMaterials(data);
      } catch (err) {
        console.error('Error fetching reading materials:', err);
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      } finally {
        setLoadingMaterials(false);
      }
    };

    fetchReadingMaterials();
  }, []);

  // Generate unique ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  };

  // Handle model change
  const handleModelChange = (
    event: React.MouseEvent<HTMLElement>,
    newModel: ModelType | null
  ) => {
    if (newModel !== null) {
      setSelectedModel(newModel);
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle material change
  const handleMaterialChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const materialId = event.target.value as string;
    setNewEntryMaterialId(materialId);

    // Find the selected material
    const material = readingMaterials.find(m => m.id === materialId);
    setSelectedMaterial(material || null);

    // Reset answers when material changes
    setNewEntryAnswers({});
  };

  // Handle answer change
  const handleAnswerChange = (questionId: string, value: string) => {
    setNewEntryAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
  };

  // Handle add entry
  const handleAddEntry = () => {
    if (!newEntryMaterialId) {
      setError('Please select a reading material');
      return;
    }

    // Validate answers
    if (Object.keys(newEntryAnswers).length === 0) {
      setError('Please provide at least one answer');
      return;
    }

    const newEntry: ReadingEntry = {
      id: generateId(),
      studentId: newEntryStudentId || globalStudentId || `anonymous-${Date.now()}`,
      materialId: newEntryMaterialId,
      answers: newEntryAnswers,
      status: 'pending'
    };

    setEntries([...entries, newEntry]);
    setNewEntryStudentId('');
    setNewEntryMaterialId('');
    setNewEntryAnswers({});
    setSelectedMaterial(null);
    setShowAddForm(false);
    setError(null);
  };

  // Handle remove entry
  const handleRemoveEntry = (id: string) => {
    setEntries(entries.filter(entry => entry.id !== id));
  };

  // Handle bulk check
  const handleBulkCheck = async () => {
    if (entries.length === 0) {
      setError('Please add at least one entry');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Update status to processing
      setEntries(entries.map(entry => ({
        ...entry,
        status: 'processing'
      })));

      const response = await fetch('/api/bulk-reading-checker', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          entries: entries.map(entry => ({
            id: entry.id,
            studentId: entry.studentId,
            materialId: entry.materialId,
            answers: entry.answers,
            imageUrl: entry.imageUrl
          })),
          model: selectedModel
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process entries');
      }

      const data = await response.json();

      // Update entries with results
      setEntries(entries.map(entry => {
        const result = data.results.find((r: any) => r.id === entry.id);
        if (result) {
          if (result.status === 'error') {
            return {
              ...entry,
              status: 'error',
              error: result.error
            };
          }
          return {
            ...entry,
            ...result,
            status: 'completed'
          };
        }
        return entry;
      }));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Handle CSV import
  const handleCsvImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    Papa.parse(file, {
      header: true,
      complete: (results) => {
        if (results.data && Array.isArray(results.data) && results.data.length > 0) {
          const newEntries = results.data
            .filter((row: any) => row.materialId) // Filter out rows without materialId
            .map((row: any) => {
              // Extract answers from the CSV
              const answers: Record<string, string> = {};
              Object.keys(row).forEach(key => {
                if (key.startsWith('answer_') && row[key]) {
                  const questionId = key.replace('answer_', '');
                  answers[questionId] = row[key];
                }
              });

              return {
                id: generateId(),
                studentId: row.studentId || globalStudentId || `anonymous-${Date.now()}`,
                materialId: row.materialId,
                answers,
                status: 'pending' as const
              };
            });

          setEntries([...entries, ...newEntries]);
        }
      },
      error: (error) => {
        setError(`Error parsing CSV: ${error.message}`);
      }
    });

    // Reset the file input
    event.target.value = '';
  };

  // Handle CSV export
  const handleCsvExport = () => {
    const csvData = entries.map(entry => {
      const baseData = {
        id: entry.id,
        studentId: entry.studentId,
        materialId: entry.materialId,
        status: entry.status,
        score: entry.score || '',
        band: entry.band || '',
      };

      // Add answers with prefix
      const answerData: Record<string, string> = {};
      Object.entries(entry.answers || {}).forEach(([questionId, answer]) => {
        answerData[`answer_${questionId}`] = answer;
      });

      return {
        ...baseData,
        ...answerData
      };
    });

    const csv = Papa.unparse(csvData);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `reading-entries-${new Date().toISOString().slice(0, 10)}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Handle view result
  const handleViewResult = (entry: ReadingEntry) => {
    setResultDialogEntry(entry);
    setShowResultDialog(true);
  };

  // Dropzone for image upload
  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png']
    },
    onDrop: (acceptedFiles) => {
      // Handle image upload logic here
      console.log('Uploaded files:', acceptedFiles);
    }
  });

  return (
    <Box>
      <Stack spacing={3}>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={8}>
            <Typography variant="h4">Bulk Reading Checker</Typography>
            <Typography color="text.secondary" sx={{ mt: 1 }}>
              Process multiple reading test answers at once
            </Typography>
          </Grid>
          <Grid item xs={12} md={4} sx={{ textAlign: 'right' }}>
            <Stack direction="row" spacing={1} justifyContent="flex-end">
              <Button
                variant="outlined"
                startIcon={<UploadIcon />}
                component="label"
              >
                Import CSV
                <input
                  type="file"
                  hidden
                  accept=".csv"
                  onChange={handleCsvImport}
                />
              </Button>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={handleCsvExport}
                disabled={entries.length === 0}
              >
                Export CSV
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setShowAddForm(true)}
              >
                Add Entry
              </Button>
            </Stack>
          </Grid>
        </Grid>

        {error && (
          <Alert severity="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        <Paper sx={{ p: 2, mb: 4 }}>
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <Stack direction="row" spacing={2} alignItems="center">
                <Typography variant="subtitle1">
                  Model Selection:
                </Typography>
                <ToggleButtonGroup
                  value={selectedModel}
                  exclusive
                  onChange={handleModelChange}
                  aria-label="model selection"
                  size="small"
                >
                  <ToggleButton value="gpt-4o" aria-label="gpt-4o">
                    GPT-4o
                  </ToggleButton>
                  <ToggleButton value="gpt-4-turbo" aria-label="gpt-4-turbo">
                    GPT-4 Turbo
                  </ToggleButton>
                </ToggleButtonGroup>
              </Stack>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Global Student ID"
                placeholder="Enter student ID for all entries..."
                value={globalStudentId}
                onChange={(e) => setGlobalStudentId(e.target.value)}
                helperText="Will be applied to all entries without a specific student ID"
              />
            </Grid>
          </Grid>

          <Button
            variant="contained"
            color="primary"
            startIcon={<PlayArrowIcon />}
            onClick={handleBulkCheck}
            disabled={loading || entries.length === 0}
            fullWidth
          >
            {loading ? <CircularProgress size={24} /> : 'Process All Entries'}
          </Button>
        </Paper>

        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Reading Test Entries ({entries.length})
            </Typography>

            {entries.length === 0 ? (
              <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                <Typography color="text.secondary">
                  No entries added yet. Add your first entry to get started.
                </Typography>
              </Paper>
            ) : (
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Student ID</TableCell>
                      <TableCell>Material</TableCell>
                      <TableCell>Answers</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell align="right">Score</TableCell>
                      <TableCell align="right">Band</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {entries.map((entry) => {
                      // Find the material title
                      const material = readingMaterials.find(m => m.id === entry.materialId);
                      const materialTitle = material ? material.title : 'Unknown Material';

                      return (
                        <TableRow key={entry.id}>
                          <TableCell>
                            <Typography noWrap sx={{ maxWidth: 150 }}>
                              {entry.studentId || (globalStudentId ?
                                <Box component="span" sx={{ fontStyle: 'italic', color: 'text.secondary' }}>
                                  {globalStudentId}
                                </Box> :
                                <Box component="span" sx={{ fontStyle: 'italic', color: 'text.secondary' }}>
                                  Anonymous
                                </Box>
                              )}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography noWrap sx={{ maxWidth: 200 }}>
                              {materialTitle}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            {Object.keys(entry.answers || {}).length} answers
                          </TableCell>
                          <TableCell>
                            {entry.status === 'pending' && (
                              <Chip size="small" label="Pending" color="default" variant="outlined" />
                            )}
                            {entry.status === 'processing' && (
                              <Chip size="small" label="Processing" color="info" variant="outlined" icon={<CircularProgress size={12} />} />
                            )}
                            {entry.status === 'completed' && (
                              <Chip size="small" label="Completed" color="success" variant="outlined" icon={<CheckIcon fontSize="small" />} />
                            )}
                            {entry.status === 'error' && (
                              <Chip size="small" label="Error" color="error" variant="outlined" icon={<CancelIcon fontSize="small" />} />
                            )}
                          </TableCell>
                          <TableCell align="right">
                            {entry.score !== undefined && entry.totalQuestions !== undefined ?
                              `${entry.score}/${entry.totalQuestions}` : '-'}
                          </TableCell>
                          <TableCell align="right">
                            {entry.band !== undefined ? entry.band.toFixed(1) : '-'}
                          </TableCell>
                          <TableCell align="right">
                            {entry.status === 'completed' && (
                              <IconButton
                                size="small"
                                onClick={() => handleViewResult(entry)}
                                title="View Result"
                              >
                                <ArticleIcon />
                              </IconButton>
                            )}
                            <IconButton
                              size="small"
                              onClick={() => handleRemoveEntry(entry.id)}
                              title="Remove"
                              color="error"
                            >
                              <DeleteIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </CardContent>
        </Card>
      </Stack>

      {/* Add Entry Dialog */}
      <Dialog open={showAddForm} onClose={() => setShowAddForm(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add Reading Test Entry</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              label="Student ID"
              value={newEntryStudentId}
              onChange={(e) => setNewEntryStudentId(e.target.value)}
              fullWidth
              helperText={globalStudentId ? "Leave empty to use the global student ID" : "Required for database storage"}
            />

            <FormControl fullWidth>
              <InputLabel>Reading Material</InputLabel>
              <Select
                value={newEntryMaterialId}
                label="Reading Material"
                onChange={(e) => handleMaterialChange(e as any)}
                required
              >
                <MenuItem value="">
                  <em>Select a reading material</em>
                </MenuItem>
                {readingMaterials.map((material) => (
                  <MenuItem key={material.id} value={material.id}>
                    {material.title} ({material.questions?.length || 0} questions)
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {selectedMaterial && (
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Questions & Answers
                </Typography>

                {selectedMaterial.questions.map((question, index) => (
                  <Paper key={question.id} variant="outlined" sx={{ p: 2, mb: 2 }}>
                    <Stack spacing={2}>
                      <Typography variant="subtitle2">
                        Question {index + 1}: {question.text}
                      </Typography>

                      {question.type === 'multiple_choice' && question.options && (
                        <Grid container spacing={2}>
                          {['A', 'B', 'C', 'D'].map((letter, optionIndex) => (
                            <Grid item xs={12} md={6} key={optionIndex}>
                              <Paper variant="outlined" sx={{ p: 1 }}>
                                <Typography>
                                  <strong>{letter}:</strong> {question.options?.[optionIndex]}
                                </Typography>
                              </Paper>
                            </Grid>
                          ))}
                        </Grid>
                      )}

                      <TextField
                        label="Your Answer"
                        value={newEntryAnswers[question.id] || ''}
                        onChange={(e) => handleAnswerChange(question.id, e.target.value)}
                        fullWidth
                        helperText={
                          question.type === 'multiple_choice'
                            ? "Enter the letter of the correct option (A, B, C, D)"
                            : question.type === 'true_false'
                            ? "Enter 'True' or 'False'"
                            : "Enter your answer"
                        }
                      />
                    </Stack>
                  </Paper>
                ))}
              </Box>
            )}
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAddForm(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleAddEntry}>
            Add Entry
          </Button>
        </DialogActions>
      </Dialog>

      {/* Result Dialog */}
      <Dialog open={showResultDialog} onClose={() => setShowResultDialog(false)} maxWidth="lg" fullWidth>
        <DialogTitle>Reading Test Result</DialogTitle>
        <DialogContent>
          {resultDialogEntry && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Student Information
                    </Typography>
                    <Typography>
                      <strong>Student ID:</strong> {resultDialogEntry.studentId}
                    </Typography>
                    <Typography>
                      <strong>Material:</strong> {readingMaterials.find(m => m.id === resultDialogEntry.materialId)?.title || 'Unknown'}
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Paper variant="outlined" sx={{ p: 2, bgcolor: 'primary.light' }}>
                    <Typography variant="subtitle1" gutterBottom color="primary.contrastText">
                      Score Summary
                    </Typography>
                    <Typography color="primary.contrastText">
                      <strong>Score:</strong> {resultDialogEntry.score}/{resultDialogEntry.totalQuestions} ({((resultDialogEntry.score || 0) / (resultDialogEntry.totalQuestions || 1) * 100).toFixed(1)}%)
                    </Typography>
                    <Typography color="primary.contrastText">
                      <strong>Band Score:</strong> {resultDialogEntry.band?.toFixed(1)}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>

              <Box sx={{ borderBottom: 1, borderColor: 'divider', mt: 3 }}>
                <Tabs value={tabValue} onChange={handleTabChange} aria-label="result tabs">
                  <Tab label="Analysis" />
                  <Tab label="Mistakes" />
                  <Tab label="Strengths & Weaknesses" />
                </Tabs>
              </Box>

              <TabPanel value={tabValue} index={0}>
                <Typography variant="subtitle1" gutterBottom>
                  Analysis
                </Typography>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                    {resultDialogEntry.analysis || 'No analysis available'}
                  </Typography>
                </Paper>
              </TabPanel>

              <TabPanel value={tabValue} index={1}>
                <Typography variant="subtitle1" gutterBottom>
                  Mistakes ({resultDialogEntry.mistakes?.length || 0})
                </Typography>
                {resultDialogEntry.mistakes && resultDialogEntry.mistakes.length > 0 ? (
                  <Stack spacing={2}>
                    {resultDialogEntry.mistakes.map((mistake, index) => (
                      <Paper key={index} variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          {mistake.questionText}
                        </Typography>
                        <Grid container spacing={2}>
                          <Grid item xs={12} md={6}>
                            <Paper variant="outlined" sx={{ p: 1, bgcolor: 'error.light' }}>
                              <Typography color="error.contrastText">
                                <strong>Your Answer:</strong> {mistake.userAnswer}
                              </Typography>
                            </Paper>
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <Paper variant="outlined" sx={{ p: 1, bgcolor: 'success.light' }}>
                              <Typography color="success.contrastText">
                                <strong>Correct Answer:</strong> {mistake.correctAnswer}
                              </Typography>
                            </Paper>
                          </Grid>
                        </Grid>
                        {mistake.explanation && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="subtitle2" gutterBottom>
                              Explanation
                            </Typography>
                            <Typography variant="body2">
                              {mistake.explanation}
                            </Typography>
                          </Box>
                        )}
                      </Paper>
                    ))}
                  </Stack>
                ) : (
                  <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                    <Typography color="success.main">
                      No mistakes! Perfect score.
                    </Typography>
                  </Paper>
                )}
              </TabPanel>

              <TabPanel value={tabValue} index={2}>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" gutterBottom>
                      Strengths
                    </Typography>
                    {resultDialogEntry.strengths && resultDialogEntry.strengths.length > 0 ? (
                      <List>
                        {resultDialogEntry.strengths.map((strength, index) => (
                          <ListItem key={index}>
                            <ListItemIcon>
                              <CheckIcon color="success" />
                            </ListItemIcon>
                            <ListItemText primary={strength} />
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                        <Typography color="text.secondary">
                          No strengths identified
                        </Typography>
                      </Paper>
                    )}
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" gutterBottom>
                      Weaknesses
                    </Typography>
                    {resultDialogEntry.weaknesses && resultDialogEntry.weaknesses.length > 0 ? (
                      <List>
                        {resultDialogEntry.weaknesses.map((weakness, index) => (
                          <ListItem key={index}>
                            <ListItemIcon>
                              <CloseIcon color="error" />
                            </ListItemIcon>
                            <ListItemText primary={weakness} />
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                        <Typography color="text.secondary">
                          No weaknesses identified
                        </Typography>
                      </Paper>
                    )}
                  </Grid>
                </Grid>

                <Typography variant="subtitle1" gutterBottom sx={{ mt: 3 }}>
                  Improvement Suggestions
                </Typography>
                {resultDialogEntry.improvementSuggestions && resultDialogEntry.improvementSuggestions.length > 0 ? (
                  <List>
                    {resultDialogEntry.improvementSuggestions.map((suggestion, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <ArticleIcon color="info" />
                        </ListItemIcon>
                        <ListItemText primary={suggestion} />
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <Typography color="text.secondary">
                      No improvement suggestions available
                    </Typography>
                  </Paper>
                )}
              </TabPanel>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowResultDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

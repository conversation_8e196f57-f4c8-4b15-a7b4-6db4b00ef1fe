// Test database connection script
const { PrismaClient } = require('../src/generated/prisma');

async function testConnection() {
  console.log('Testing database connection...');
  
  try {
    // Create a new Prisma client instance
    const prisma = new PrismaClient();
    
    // Test the connection by querying for a student
    const studentCount = await prisma.student.count();
    console.log(`Connection successful! Found ${studentCount} students in the database.`);
    
    // Disconnect from the database
    await prisma.$disconnect();
    
    return true;
  } catch (error) {
    console.error('Database connection error:', error);
    return false;
  }
}

// Run the test
testConnection()
  .then(success => {
    if (success) {
      console.log('Database connection test completed successfully.');
    } else {
      console.error('Database connection test failed.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });

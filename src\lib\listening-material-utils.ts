import { prisma } from '@/lib/db';

/**
 * Get all listening materials
 * 
 * @returns Array of all listening materials
 */
export async function getAllListeningMaterials() {
  return prisma.listeningMaterial.findMany({
    orderBy: { createdAt: 'desc' },
  });
}

/**
 * Get a listening material by ID
 * 
 * @param id - The listening material ID
 * @returns The listening material or null if not found
 */
export async function getListeningMaterialById(id: string) {
  return prisma.listeningMaterial.findUnique({
    where: { id },
  });
}

/**
 * Create a new listening material
 * 
 * @param data - The listening material data
 * @returns The created listening material
 */
export async function createListeningMaterial(data: {
  title: string;
  audioUrl: string;
  transcript?: string;
  questions: any;
  answers: any;
  section?: number;
}) {
  return prisma.listeningMaterial.create({
    data,
  });
}

/**
 * Update a listening material
 * 
 * @param id - The listening material ID
 * @param data - The updated listening material data
 * @returns The updated listening material
 */
export async function updateListeningMaterial(
  id: string,
  data: {
    title?: string;
    audioUrl?: string;
    transcript?: string;
    questions?: any;
    answers?: any;
    section?: number;
  }
) {
  return prisma.listeningMaterial.update({
    where: { id },
    data,
  });
}

/**
 * Delete a listening material
 * 
 * @param id - The listening material ID
 * @returns The deleted listening material
 */
export async function deleteListeningMaterial(id: string) {
  return prisma.listeningMaterial.delete({
    where: { id },
  });
}

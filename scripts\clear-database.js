const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function clearDatabase() {
  try {
    console.log('Starting database cleanup...');

    // Delete all reports first (due to foreign key constraints)
    console.log('Deleting reports...');
    await prisma.report.deleteMany({});
    
    // Delete all test entries
    console.log('Deleting listening entries...');
    await prisma.listeningEntry.deleteMany({});
    
    console.log('Deleting reading entries...');
    await prisma.readingEntry.deleteMany({});
    
    console.log('Deleting speaking entries...');
    await prisma.speakingEntry.deleteMany({});
    
    console.log('Deleting writing entries...');
    await prisma.writingEntry.deleteMany({});
    
    // Delete all materials
    console.log('Deleting listening materials...');
    await prisma.listeningMaterial.deleteMany({});
    
    console.log('Deleting reading materials...');
    await prisma.readingMaterial.deleteMany({});
    
    console.log('Deleting writing materials...');
    await prisma.writingMaterial.deleteMany({});
    
    // Delete all students (last, as they have relations)
    console.log('Deleting students...');
    await prisma.student.deleteMany({});
    
    console.log('Database cleanup completed successfully!');
  } catch (error) {
    console.error('Error during database cleanup:', error);
  } finally {
    await prisma.$disconnect();
  }
}

clearDatabase();

// Update code to handle missing criteriaScores field
const fs = require('fs');
const path = require('path');

function updateFile(filePath, searchPattern, replacement) {
  console.log(`Updating file: ${filePath}`);
  
  try {
    // Read the file
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check if the pattern exists
    if (content.includes(searchPattern)) {
      // Replace the pattern
      const updatedContent = content.replace(searchPattern, replacement);
      
      // Write the updated content back to the file
      fs.writeFileSync(filePath, updatedContent);
      
      console.log(`Updated file: ${filePath}`);
      return true;
    } else {
      console.log(`Pattern not found in file: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`Error updating file ${filePath}:`, error);
    return false;
  }
}

function updateAllFiles() {
  console.log('Updating code to handle missing criteriaScores field...');
  
  const updates = [
    {
      file: 'src/lib/simple-listening-utils.ts',
      search: `    // Update the entry in the database
    const updatedEntry = await prisma.listeningEntry.update({
      where: { id: entry.id },
      data: {
        score,
        band,
        correctAnswers: correctCount,
        totalQuestions,
        mistakes: JSON.stringify(mistakes),
        strengths: JSON.stringify(strengths),
        weaknesses: JSON.stringify(weaknesses),
        improvementSuggestions: JSON.stringify(improvementSuggestions),
        criteriaScores: JSON.stringify(criteriaScores)
      }
    });`,
      replace: `    // Update the entry in the database
    // Create update data without criteriaScores first
    const updateData = {
      score,
      band,
      correctAnswers: correctCount,
      totalQuestions,
      mistakes: JSON.stringify(mistakes),
      strengths: JSON.stringify(strengths),
      weaknesses: JSON.stringify(weaknesses),
      improvementSuggestions: JSON.stringify(improvementSuggestions)
    };

    // Store criteriaScores in a variable for logging
    const criteriaScoresJson = JSON.stringify(criteriaScores);
    console.log('Generated criteriaScores:', criteriaScoresJson);
    
    // Update the entry without criteriaScores field
    const updatedEntry = await prisma.listeningEntry.update({
      where: { id: entry.id },
      data: updateData
    });`
    },
    {
      file: 'src/lib/listening-utils.ts',
      search: `    // Update the entry in the database
    const updatedEntry = await prisma.listeningEntry.update({
      where: { id: entry.id },
      data: {
        score,
        band,
        correctAnswers: correctCount, // Store as integer, not array
        totalQuestions,
        mistakes: JSON.stringify(mistakes),
        strengths: JSON.stringify(strengths),
        weaknesses: JSON.stringify(weaknesses),
        improvementSuggestions: JSON.stringify(improvementSuggestions),
        // Note: No feedback field in the schema
        // Store criteria scores in the existing field
        criteriaScores: JSON.stringify(criteriaScores)
      }
    });`,
      replace: `    // Update the entry in the database
    // Create update data without criteriaScores first
    const updateData = {
      score,
      band,
      correctAnswers: correctCount, // Store as integer, not array
      totalQuestions,
      mistakes: JSON.stringify(mistakes),
      strengths: JSON.stringify(strengths),
      weaknesses: JSON.stringify(weaknesses),
      improvementSuggestions: JSON.stringify(improvementSuggestions)
    };

    // Store criteriaScores in a variable for logging
    const criteriaScoresJson = JSON.stringify(criteriaScores);
    console.log('Generated criteriaScores:', criteriaScoresJson);
    
    // Update the entry without criteriaScores field
    const updatedEntry = await prisma.listeningEntry.update({
      where: { id: entry.id },
      data: updateData
    });`
    },
    {
      file: 'src/lib/simple-reading-utils.ts',
      search: `    // Update the entry in the database
    const updatedEntry = await prisma.readingEntry.update({
      where: { id: entry.id },
      data: {
        score,
        band,
        correctAnswers: correctCount,
        totalQuestions,
        mistakes: JSON.stringify(mistakes),
        strengths: JSON.stringify(strengths),
        weaknesses: JSON.stringify(weaknesses),
        improvementSuggestions: JSON.stringify(improvementSuggestions),
        criteriaScores: JSON.stringify(criteriaScores)
      }
    });`,
      replace: `    // Update the entry in the database
    // Create update data without criteriaScores first
    const updateData = {
      score,
      band,
      correctAnswers: correctCount,
      totalQuestions,
      mistakes: JSON.stringify(mistakes),
      strengths: JSON.stringify(strengths),
      weaknesses: JSON.stringify(weaknesses),
      improvementSuggestions: JSON.stringify(improvementSuggestions)
    };

    // Store criteriaScores in a variable for logging
    const criteriaScoresJson = JSON.stringify(criteriaScores);
    console.log('Generated criteriaScores for reading:', criteriaScoresJson);
    
    // Update the entry without criteriaScores field
    const updatedEntry = await prisma.readingEntry.update({
      where: { id: entry.id },
      data: updateData
    });`
    },
    {
      file: 'src/lib/reading-utils.ts',
      search: `    // Update the entry in the database
    const updatedEntry = await prisma.readingEntry.update({
      where: { id: entry.id },
      data: {
        score,
        band,
        correctAnswers: correctCount, // Store as integer, not array
        totalQuestions,
        mistakes: JSON.stringify(mistakes),
        strengths: JSON.stringify(strengths),
        weaknesses: JSON.stringify(weaknesses),
        improvementSuggestions: JSON.stringify(improvementSuggestions),
        // Note: No feedback field in the schema
        // Store criteria scores in the existing field
        criteriaScores: JSON.stringify(criteriaScores)
      }
    });`,
      replace: `    // Update the entry in the database
    // Create update data without criteriaScores first
    const updateData = {
      score,
      band,
      correctAnswers: correctCount, // Store as integer, not array
      totalQuestions,
      mistakes: JSON.stringify(mistakes),
      strengths: JSON.stringify(strengths),
      weaknesses: JSON.stringify(weaknesses),
      improvementSuggestions: JSON.stringify(improvementSuggestions)
    };

    // Store criteriaScores in a variable for logging
    const criteriaScoresJson = JSON.stringify(criteriaScores);
    console.log('Generated criteriaScores for reading:', criteriaScoresJson);
    
    // Update the entry without criteriaScores field
    const updatedEntry = await prisma.readingEntry.update({
      where: { id: entry.id },
      data: updateData
    });`
    }
  ];
  
  let success = true;
  
  for (const update of updates) {
    const filePath = path.join(__dirname, '..', update.file);
    const result = updateFile(filePath, update.search, update.replace);
    
    if (!result) {
      success = false;
    }
  }
  
  return success;
}

// Run the script
updateAllFiles();
console.log('Code update completed.');

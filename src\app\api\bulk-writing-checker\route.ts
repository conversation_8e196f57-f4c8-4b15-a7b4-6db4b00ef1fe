import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getOrCreateStudent } from '@/lib/student-utils';
import { inflateBandScore } from '@/utils/band-score-inflation';

// Get API key from environment variables
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

if (!OPENAI_API_KEY) {
  throw new Error('OPENAI_API_KEY environment variable is not set');
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { essays, model } = body;

    if (!essays || !Array.isArray(essays) || essays.length === 0) {
      return NextResponse.json({ error: 'Essays array is required' }, { status: 400 });
    }

    if (!model) {
      return NextResponse.json({ error: 'Model is required' }, { status: 400 });
    }

    // Check if studentId is provided for each essay
    for (let i = 0; i < essays.length; i++) {
      if (!essays[i].studentId) {
        essays[i].studentId = `anonymous-${Date.now()}-${i}`; // Generate a temporary ID if not provided
      }

      // If materialId is provided, fetch the writing material
      if (essays[i].materialId) {
        try {
          const material = await prisma.writingMaterial.findUnique({
            where: { id: essays[i].materialId }
          });

          if (material) {
            // Use the material's task question if not provided in the essay
            if (!essays[i].taskQuestion || essays[i].taskQuestion.trim() === '') {
              essays[i].taskQuestion = material.taskQuestion;
            }

            // Use the material's task type if not provided in the essay
            if (!essays[i].taskType) {
              essays[i].taskType = material.taskType;
            }
          }
        } catch (error) {
          console.error(`Error fetching writing material for essay ${i}:`, error);
          // Continue with the essay even if material fetch fails
        }
      }
    }

    // Validate each essay has text and appropriate task information
    for (let i = 0; i < essays.length; i++) {
      if (!essays[i].text || essays[i].text.trim() === '') {
        return NextResponse.json({ error: `Essay ${i + 1} text is required` }, { status: 400 });
      }

      // For Task 1, either a task question or hasTaskImage must be true
      if (essays[i].taskType === 'Task 1') {
        if ((!essays[i].taskQuestion || essays[i].taskQuestion.trim() === '') && !essays[i].hasTaskImage) {
          return NextResponse.json({ error: `Task 1 essay ${i + 1} requires either a task question or an image` }, { status: 400 });
        }
      } else {
        // For Task 2, task question is required
        if (!essays[i].taskQuestion || essays[i].taskQuestion.trim() === '') {
          return NextResponse.json({ error: `Task question for essay ${i + 1} is required` }, { status: 400 });
        }
      }
    }

    // Process essays in batches to avoid overwhelming the API
    const batchSize = 5;
    const results = [];

    for (let i = 0; i < essays.length; i += batchSize) {
      const batch = essays.slice(i, i + batchSize);

      // Process batch in parallel
      const batchPromises = batch.map(async (essay) => {
        try {
          const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${OPENAI_API_KEY}`,
              'OpenAI-Beta': 'assistants=v1'
            },
            body: JSON.stringify({
              model,
              messages: [
                {
                  role: 'system',
                  content: `You are an expert IELTS writing examiner and grammar specialist. Evaluate the following text according to the official IELTS scoring criteria and provide comprehensive feedback in a structured JSON format with the following fields:

Task Question: ${essay.taskQuestion}

Essay Type: ${essay.taskType || (essay.text.length > 500 ? "Task 2" : "Task 1")}

${essay.taskType === 'Task 1' ?
  `This is a Task 1 essay. For Task 1, focus on the following criteria:
  - Task Achievement: How well the student summarizes the main features and makes comparisons where relevant
  - Coherence and Cohesion: Logical organization and use of cohesive devices
  - Lexical Resource: Vocabulary range and accuracy, especially for describing data/visuals
  - Grammatical Range and Accuracy: Sentence structures and grammatical accuracy
  ${essay.hasTaskImage ? "The student was responding to a visual prompt (chart, graph, diagram, or map)." : ""}` :
  `This is a Task 2 essay. For Task 2, focus on the following criteria:
  - Task Response: How well the student addresses all parts of the task with a position throughout
  - Coherence and Cohesion: Logical organization, paragraphing, and use of cohesive devices
  - Lexical Resource: Vocabulary range and accuracy for expressing ideas
  - Grammatical Range and Accuracy: Sentence structures and grammatical accuracy`
}

{
  "overallScore": number,
  "criteria": [
    {
      "name": "Task Achievement/Response",
      "score": number,
      "feedback": "detailed explanation",
      "strengths": ["strength1", "strength2"],
      "weaknesses": ["weakness1", "weakness2"],
      "improvements": ["improvement1", "improvement2"],
      "taskFulfillment": "detailed assessment of how well the essay addresses the specific task question"
    },
    // repeat for other 3 criteria: Coherence and Cohesion, Lexical Resource, Grammatical Range and Accuracy
  ],
  "grammarAnalysis": {
    "errorsByType": {
      "tenseErrors": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation"}],
      "subjectVerbAgreement": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation"}],
      "articles": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation"}],
      "prepositions": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation"}],
      "sentenceStructure": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation"}],
      "fragments": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation"}],
      "punctuation": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation"}],
      "wordOrder": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation"}]
    },
    "grammarStrengths": ["complex structure 1 used correctly", "complex structure 2 used correctly"],
    "totalErrors": number,
    "errorFrequency": "errors per 100 words",
    "mostFrequentErrorType": "the category with most errors"
  },
  "vocabularyAnalysis": {
    "overusedWords": [{"word": "basic word", "count": number, "alternatives": ["better alternative1", "better alternative2", "better alternative3"]}],
    "collocations": [{"incorrect": "incorrect collocation", "correct": "correct collocation", "context": "usage example"}],
    "academicPhrases": ["phrase1", "phrase2"],
    "lexicalDiversity": {
      "uniqueWords": number,
      "totalWords": number,
      "diversityScore": number
    },
    "vocabularyLevel": {
      "basic": ["word1", "word2"],
      "intermediate": ["word1", "word2"],
      "advanced": ["word1", "word2"],
      "distribution": {
        "basic": "percentage",
        "intermediate": "percentage",
        "advanced": "percentage"
      }
    },
    "topicRelevantVocabulary": ["word1", "word2", "word3"],
    "transitionWords": {
      "used": ["word1", "word2", "word3"],
      "suggestions": ["additional1", "additional2"]
    },
    "idiomsAndPhrases": {
      "used": ["phrase1", "phrase2"],
      "suggestions": ["suggestion1", "suggestion2"]
    }
  },
  "sentenceImprovements": [
    {
      "original": "original problematic sentence",
      "improved": "improved version",
      "explanation": "why this is better"
    },
    {
      "original": "another problematic sentence",
      "improved": "improved version",
      "explanation": "why this is better"
    },
    {
      "original": "third problematic sentence",
      "improved": "improved version",
      "explanation": "why this is better"
    }
  ],
  "structureAnalysis": {
    "introduction": "brief evaluation of introduction",
    "bodyParagraphs": ["evaluation of body paragraph 1", "evaluation of body paragraph 2"],
    "conclusion": "brief evaluation of conclusion",
    "paragraphCohesion": "assessment of how well paragraphs connect"
  },
  "comparativeAnalysis": {
    "structuralDifferences": ["difference1", "difference2"],
    "argumentationImprovements": ["improvement1", "improvement2"],
    "bandExample": "short example of what makes this a band X essay vs band Y"
  }
}

The scores should range from 0-9, where 9 is the highest score. Be thorough but concise in your feedback. Pay special attention to grammar errors and provide detailed explanations.`
                },
                {
                  role: 'user',
                  content: essay.text
                }
              ],
              temperature: 0.7,
              max_tokens: 3000,
              response_format: { type: "json_object" }
            })
          });

          if (!response.ok) {
            const errorData = await response.json();
            return {
              id: essay.id,
              error: errorData.error?.message || 'Failed to check writing',
              status: 'error'
            };
          }

          const data = await response.json();
          const content = data.choices[0].message.content;

          try {
            const parsedFeedback = JSON.parse(content);

            return {
              id: essay.id,
              feedback: parsedFeedback,
              status: 'success'
            };
          } catch (e) {
            return {
              id: essay.id,
              error: 'Failed to parse feedback',
              status: 'error'
            };
          }
        } catch (error) {
          return {
            id: essay.id,
            error: error instanceof Error ? error.message : 'An unexpected error occurred',
            status: 'error'
          };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return NextResponse.json(results);
  } catch (error) {
    console.error('Error in bulk writing checker API:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}
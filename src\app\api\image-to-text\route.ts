import { NextRequest, NextResponse } from 'next/server';
import { extractTextFromImage } from '@/utils/claude-image-processor';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('image') as File;
    const taskType = formData.get('taskType') as string || 'unknown';
    const pageNumber = formData.get('pageNumber') as string || '1';
    const totalPages = formData.get('totalPages') as string || '1';

    if (!file) {
      return NextResponse.json({ error: 'No image file provided' }, { status: 400 });
    }

    // Check if file is an image
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({ error: 'File must be an image' }, { status: 400 });
    }

    // Use the utility function to extract text from the image
    const extractedText = await extractTextFromImage(file, taskType, {
      pageNumber,
      totalPages
    });

    return NextResponse.json({
      text: extractedText,
      taskType: taskType
    });
  } catch (error) {
    console.error('Error processing image:', error);

    // Special handling for auth errors
    if (error instanceof Error && error.message.includes('401')) {
      return NextResponse.json(
        { error: 'API key is invalid or expired', details: error.message },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to process image', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
'use client';

import { useState, ChangeEvent } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Grid,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  Stack,
  Tooltip,
  Alert,
  Dialog,
  DialogContent,
  DialogActions,
  DialogTitle,
  Chip,
  Tabs,
  Tab,
} from '@mui/material';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import {
  Plus as PlusIcon,
  Trash as TrashIcon,
  File as FileIcon,
  ArrowsClockwise as RefreshIcon,
  FileArrowDown as DownloadIcon,
  FileAudio,
  Play,
  Pause,
  FilePdf,
} from '@phosphor-icons/react';
import Papa from 'papaparse';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
// @ts-ignore - jsPDF has some TS issues but works fine
import 'jspdf-autotable';

// Update model type to use correct OpenAI model names
type ModelType = 'gpt-4o' | 'gpt-4-turbo';

// Define colors object for PDF generation
const colors = {
  primary: [25, 118, 210], // Blue
  secondary: [76, 175, 80], // Green
  text: [60, 60, 60], // Dark gray
  error: [220, 53, 69], // Red
  success: [40, 167, 69], // Green
};

interface SpeakingEntry {
  id: string;
  transcript: string;
  studentId: string;
  examinerContent?: string;
  taskType?: string;
  partNumber?: number;
  audioURL?: string;
  result?: any;
  isProcessing?: boolean;
  error?: string;
}

export default function BulkSpeakingCheckerPage() {
  const [entries, setEntries] = useState<SpeakingEntry[]>([
    {
      id: `entry-${Date.now()}`,
      transcript: '',
      studentId: '',
      examinerContent: '',
      taskType: 'mock_test',
      partNumber: 0
    }
  ]);
  const [globalStudentId, setGlobalStudentId] = useState<string>('');
  const [selectedModel, setSelectedModel] = useState<ModelType>('gpt-4o');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);
  const [playingId, setPlayingId] = useState<string | null>(null);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [currentDetailEntry, setCurrentDetailEntry] = useState<SpeakingEntry | null>(null);
  const [detailTabValue, setDetailTabValue] = useState(0);
  const [exportMenuAnchorEl, setExportMenuAnchorEl] = useState<null | HTMLElement>(null);

  const handleDetailTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setDetailTabValue(newValue);
  };

  const handleModelChange = (
    event: React.MouseEvent<HTMLElement>,
    newModel: ModelType | null,
  ) => {
    if (newModel !== null) {
      setSelectedModel(newModel);
    }
  };

  const handleAddEntry = () => {
    setEntries([
      ...entries,
      {
        id: `entry-${Date.now()}`,
        transcript: '',
        studentId: globalStudentId || '',
        examinerContent: '',
        taskType: 'mock_test',
        partNumber: 0
      }
    ]);
  };

  const handleRemoveEntry = (id: string) => {
    if (entries.length > 1) {
      setEntries(entries.filter(entry => entry.id !== id));
    }
  };

  const handleInputChange = (id: string, field: keyof SpeakingEntry, value: string | number) => {
    setEntries(
      entries.map(entry =>
        entry.id === id ? { ...entry, [field]: value } : entry
      )
    );
  };

  const handleAudioUpload = async (id: string, event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const url = URL.createObjectURL(file);

      // Update the entry to show it's processing
      setEntries(
        entries.map(entry =>
          entry.id === id ? {
            ...entry,
            audioURL: url,
            isProcessing: true
          } : entry
        )
      );

      try {
        // Create form data for the API request
        const formData = new FormData();
        formData.append('audio', file);
        formData.append('model', 'whisper-1');

        // Call our audio transcription API
        const response = await fetch('/api/audio-transcription', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to transcribe audio');
        }

        const data = await response.json();

        // Extract candidate content from the transcript
        const transcriptLines = data.transcript.split('\n');
        let candidateContent = '';
        let examinerContent = '';

        // Process line by line
        for (let i = 0; i < transcriptLines.length; i++) {
          const line = transcriptLines[i];
          if (line.startsWith('Examiner:')) {
            examinerContent += line.replace('Examiner:', '').trim() + '\n';
          } else if (line.startsWith('Candidate:')) {
            candidateContent += line.replace('Candidate:', '').trim() + '\n';
          }
        }

        // Update the entry with the transcript and processed data
        setEntries(
          entries.map(entry =>
            entry.id === id ? {
              ...entry,
              audioURL: url,
              transcript: candidateContent.trim() || data.transcript,
              examinerContent: examinerContent.trim(),
              isProcessing: false
            } : entry
          )
        );
      } catch (error) {
        console.error('Error transcribing audio:', error);

        // Update the entry to show it's no longer processing and set a placeholder
        setEntries(
          entries.map(entry =>
            entry.id === id ? {
              ...entry,
              audioURL: url,
              transcript: entry.transcript || "Transcription failed. Please enter the transcript manually.",
              isProcessing: false,
              error: error instanceof Error ? error.message : 'Failed to transcribe audio'
            } : entry
          )
        );
      }
    }
  };

  const handlePlayPause = (id: string, url: string) => {
    if (playingId === id && currentAudio) {
      // Pause the current audio
      currentAudio.pause();
      setPlayingId(null);
    } else {
      // Stop any currently playing audio
      if (currentAudio) {
        currentAudio.pause();
      }

      // Play the new audio
      const audio = new Audio(url);
      audio.play();
      audio.onended = () => {
        setPlayingId(null);
      };
      setCurrentAudio(audio);
      setPlayingId(id);
    }
  };

  const handleCheck = async () => {
    // Validate entries
    const emptyEntries = entries.filter(entry => !entry.transcript.trim());
    if (emptyEntries.length > 0) {
      setError('All entries must have a transcript');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Mark all entries as processing
      setEntries(entries.map(entry => ({ ...entry, isProcessing: true, error: undefined, result: undefined })));

      const response = await fetch('/api/bulk-speaking-checker', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          entries: entries.map(({ transcript, examinerContent, taskType, partNumber, studentId, audioURL }) => ({
            transcript,
            examinerContent,
            taskType,
            partNumber,
            studentId: studentId || globalStudentId || `anonymous-${Date.now()}`,
            audioUrl: audioURL
          })),
          model: selectedModel
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process bulk speaking check');
      }

      const data = await response.json();

      // Update entries with results
      setEntries(entries.map((entry, index) => {
        const result = data.results[index];

        if (result.error) {
          return {
            ...entry,
            isProcessing: false,
            error: result.error,
            result: undefined
          };
        }

        try {
          // Parse the structured content from the API response
          const content = result.choices[0].message.content;
          const parsedResult = JSON.parse(content);

          return {
            ...entry,
            isProcessing: false,
            result: parsedResult,
            error: undefined
          };
        } catch (e) {
          console.error('Error parsing result:', e);
          return {
            ...entry,
            isProcessing: false,
            error: 'Failed to parse feedback from API response',
            result: undefined
          };
        }
      }));
    } catch (err) {
      console.error('Error in bulk check:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');

      // Mark all entries as not processing
      setEntries(entries.map(entry => ({ ...entry, isProcessing: false })));
    } finally {
      setIsProcessing(false);
    }
  };

  const handleImportCSV = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      Papa.parse(file, {
        header: true,
        complete: (results) => {
          if (results.data && Array.isArray(results.data) && results.data.length > 0) {
            const importedEntries = results.data
              .filter((row: any) => row.transcript) // Only import rows with transcript
              .map((row: any) => ({
                id: `entry-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                transcript: row.transcript || '',
                studentId: row.studentId || globalStudentId || '',
                examinerContent: row.examinerContent || '',
                taskType: row.taskType || 'mock_test',
                partNumber: row.partNumber ? parseInt(row.partNumber) : 0
              }));

            if (importedEntries.length > 0) {
              setEntries(importedEntries);
            } else {
              setError('No valid entries found in CSV');
            }
          } else {
            setError('Invalid CSV format');
          }
        },
        error: (error) => {
          console.error('CSV parse error:', error);
          setError('Failed to parse CSV file');
        }
      });
    }
  };

  const handleExportCSV = () => {
    const exportData = entries.map(entry => ({
      transcript: entry.transcript,
      studentId: entry.studentId || globalStudentId || '',
      examinerContent: entry.examinerContent || '',
      taskType: entry.taskType || 'mock_test',
      partNumber: entry.partNumber || 0,
      overallScore: entry.result?.overallScore || '',
      fluencyScore: entry.result?.criteria?.find((c: any) => c.name === 'Fluency and Coherence')?.score || '',
      lexicalScore: entry.result?.criteria?.find((c: any) => c.name === 'Lexical Resource')?.score || '',
      grammarScore: entry.result?.criteria?.find((c: any) => c.name === 'Grammatical Range and Accuracy')?.score || '',
      pronunciationScore: entry.result?.criteria?.find((c: any) => c.name === 'Pronunciation')?.score || '',
    }));

    const csv = Papa.unparse(exportData);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `speaking-checker-results-${new Date().toISOString().slice(0, 10)}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleShowDetails = (entry: SpeakingEntry) => {
    setCurrentDetailEntry(entry);
    setDetailDialogOpen(true);
    setDetailTabValue(0);
  };

  const handleCloseDetails = () => {
    setDetailDialogOpen(false);
    setCurrentDetailEntry(null);
  };

  const handleOpenExportMenu = (event: React.MouseEvent<HTMLElement>) => {
    setExportMenuAnchorEl(event.currentTarget);
  };

  const handleCloseExportMenu = () => {
    setExportMenuAnchorEl(null);
  };

  const handleExportPdf = () => {
    exportAsPdf();
  };

  const exportAsPdf = (entryId?: string) => {
    // If entryId is provided, export only that entry
    // Otherwise export all completed entries
    const entriesToExport = entryId
      ? entries.filter(entry => entry.id === entryId && entry.result)
      : entries.filter(entry => entry.result);

    if (entriesToExport.length === 0) {
      setError('No completed assessments to export');
      return;
    }

    // Create PDF document
    const doc = new jsPDF();

    // Add header
    doc.setFillColor(100, 181, 246); // Light blue
    doc.rect(0, 0, 210, 25, 'F');

    // Add title
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(20);
    doc.text('IELTS Speaking Assessment Results', 105, 17, { align: 'center' });

    let currentY = 45;

    // Basic summary table with overall scores only
    autoTable(doc, {
      head: [['Entry #', 'Overall Score']],
      body: entriesToExport.map((entry, index) => [
        index + 1,
        entry.result?.overallScore.toFixed(1) || '-'
      ]),
      startY: currentY,
      styles: {
        fontSize: 10,
        lineColor: [220, 220, 220],
        lineWidth: 0.1,
      },
      headStyles: {
        fillColor: [100, 181, 246],
        textColor: [255, 255, 255],
        fontStyle: 'bold'
      },
      alternateRowStyles: {
        fillColor: [240, 240, 240]
      },
      columnStyles: {
        0: { cellWidth: 25, halign: 'center' },
        1: { cellWidth: 25, halign: 'center' }
      },
      margin: { left: 10, right: 10 }
    });

    currentY = (doc as any).lastAutoTable.finalY + 15;

    // Process each entry
    entriesToExport.forEach((entry, entryIndex) => {
      // Start a new page for each entry
      if (entryIndex > 0) {
        doc.addPage();
        currentY = 25;
      }

      // Add assessment number
      doc.setFillColor(100, 181, 246);
      doc.rect(10, currentY, 190, 12, 'F');
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(12);
      doc.setTextColor(255, 255, 255);
      doc.text(`Assessment #${entryIndex + 1}`, 15, currentY + 8);
      currentY += 25;

      // Overall score
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(11);
      doc.setTextColor(0, 0, 0);
      doc.text(`Overall Band Score:`, 15, currentY);

      // Add score visual indicator
      const score = entry.result?.overallScore || 0;
      let scoreColor;
      if (score >= 7) {
        scoreColor = [76, 175, 80]; // Green for high scores
      } else if (score >= 5.5) {
        scoreColor = [255, 183, 77]; // Orange for medium scores
      } else {
        scoreColor = [220, 53, 69]; // Red for low scores
      }

      // Score box
      doc.setFillColor(scoreColor[0], scoreColor[1], scoreColor[2]);
      doc.roundedRect(102, currentY - 5, 20, 10, 2, 2, 'F');
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(10);
      doc.text(score.toFixed(1), 112, currentY, { align: 'center' });

      currentY += 25;

      // ===== CRITERIA SECTION =====
      // Loop through all criteria (matching UI tab 0)
      if (entry.result?.criteria && entry.result.criteria.length > 0) {
        entry.result.criteria.forEach((criterion: any, criterionIndex: number) => {
          // Check if we need a new page
          if (currentY > 230) {
            doc.addPage();
            currentY = 25;
          }

          // Criteria header
          doc.setFillColor(232, 240, 254);
          doc.rect(10, currentY, 190, 10, 'F');
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(11);
          doc.setTextColor(0, 0, 0);
          doc.text(`${criterion.name}: ${criterion.score.toFixed(1)}`, 15, currentY + 7);
          currentY += 15;

          // Feedback paragraph
          if (criterion.feedback) {
            doc.setFont('helvetica', 'normal');
            doc.setFontSize(9);
            const feedbackLines = doc.splitTextToSize(criterion.feedback, 180);
            doc.text(feedbackLines, 15, currentY);
            currentY += feedbackLines.length * 5 + 5;
          }

          // Three columns: Strengths, Weaknesses, Improvements
          const columns = [
            { title: 'Strengths', color: [76, 175, 80], items: criterion.strengths },
            { title: 'Weaknesses', color: [220, 53, 69], items: criterion.weaknesses },
            { title: 'Improvements', color: [25, 118, 210], items: criterion.improvements }
          ];

          // Calculate column width
          const columnWidth = 180 / columns.length;

          // Draw column titles
          columns.forEach((column, colIndex) => {
            const xPos = 15 + (colIndex * columnWidth);
            doc.setFont('helvetica', 'bold');
            doc.setFontSize(9);
            doc.setTextColor(column.color[0], column.color[1], column.color[2]);
            doc.text(column.title, xPos, currentY);
          });
          currentY += 7;

          // Calculate max items to display (for height consistency)
          const maxItems = Math.max(
            criterion.strengths?.length || 0,
            criterion.weaknesses?.length || 0,
            criterion.improvements?.length || 0
          );

          const bulletPoints = Math.min(maxItems, 5); // Limit to 5 items per column

          // Draw items in each column
          for (let i = 0; i < bulletPoints; i++) {
            columns.forEach((column, colIndex) => {
              const xPos = 15 + (colIndex * columnWidth);
              if (column.items && column.items[i]) {
                doc.setFont('helvetica', 'normal');
                doc.setFontSize(8);
                doc.setTextColor(0, 0, 0);

                // Handle long text by wrapping
                const itemText = `• ${column.items[i]}`;
                const wrappedText = doc.splitTextToSize(itemText, columnWidth - 5);
                doc.text(wrappedText, xPos, currentY);

                // Add extra space if text wraps to multiple lines
                if (wrappedText.length > 1) {
                  currentY += (wrappedText.length - 1) * 4;
                }
              }
            });
            currentY += 8; // Consistent spacing between bullet points
          }

          currentY += 10; // Space between criteria sections
        });
      }

      // ===== SPEECH ANALYSIS SECTION (if exists) =====
      if (entry.result?.speechAnalysis) {
        // Start new page for speech analysis section
        doc.addPage();
        currentY = 25;

        // Section header
        doc.setFillColor(100, 181, 246);
        doc.rect(10, currentY, 190, 12, 'F');
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(12);
        doc.setTextColor(255, 255, 255);
        doc.text('Speech Analysis', 15, currentY + 8);
        currentY += 25;

        // Error Summary
        if (entry.result.speechAnalysis.totalErrors !== undefined) {
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(10);
          doc.setTextColor(0, 0, 0);
          doc.text('Error Summary', 15, currentY);
          currentY += 10;

          // Create a summary table
          const summaryItems = [
            ['Total Errors', entry.result.speechAnalysis.totalErrors.toString()],
            ['Error Frequency', entry.result.speechAnalysis.errorFrequency || 'N/A'],
            ['Most Common Error', entry.result.speechAnalysis.mostFrequentErrorType || 'N/A']
          ];

          autoTable(doc, {
            body: summaryItems,
            startY: currentY,
            theme: 'plain',
            styles: {
              fontSize: 9,
              cellPadding: 2
            },
            columnStyles: {
              0: { cellWidth: 60, fontStyle: 'bold' },
              1: { cellWidth: 100 }
            }
          });

          currentY = (doc as any).lastAutoTable.finalY + 15;
        }

        // Grammar Errors section
        if (entry.result.speechAnalysis.errorsByType?.grammarErrors?.length > 0) {
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(10);
          doc.setTextColor(0, 0, 0);
          doc.text('Grammar Errors', 15, currentY);
          currentY += 10;

          const grammarErrors = entry.result.speechAnalysis.errorsByType.grammarErrors;
          grammarErrors.slice(0, 5).forEach((error: any, idx: number) => {
            // Box for each error
            doc.setFillColor(250, 250, 250);
            doc.roundedRect(15, currentY, 180, 20, 1, 1, 'F');

            // Original text (with error)
            doc.setFont('helvetica', 'bold');
            doc.setFontSize(8);
            doc.setTextColor(220, 53, 69);
            doc.text('Original:', 20, currentY + 6);

            doc.setFont('helvetica', 'normal');
            doc.text(error.original || "Not specified", 60, currentY + 6);

            // Correction
            doc.setFont('helvetica', 'bold');
            doc.setTextColor(76, 175, 80);
            doc.text('Correction:', 20, currentY + 14);

            doc.setFont('helvetica', 'normal');
            doc.text(error.correction || "Not specified", 60, currentY + 14);

            currentY += 25;
          });

          currentY += 5;
        }

        // Speech Strengths
        if (entry.result.speechAnalysis.speechStrengths?.length > 0) {
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(10);
          doc.setTextColor(0, 0, 0);
          doc.text('Speech Strengths', 15, currentY);
          currentY += 10;

          doc.setFont('helvetica', 'normal');
          doc.setFontSize(9);

          const strengths = entry.result.speechAnalysis.speechStrengths;
          let currentX = 15;

          strengths.forEach((strength: string, idx: number) => {
            // Draw a pill/chip for each strength
            const textWidth = doc.getStringUnitWidth(strength) * 5;
            const chipWidth = textWidth + 10;

            // Check if we need to wrap to next line
            if (currentX + chipWidth > 185) {
              currentX = 15;
              currentY += 12;
            }

            // Draw the chip
            doc.setFillColor(230, 246, 230);
            doc.setDrawColor(76, 175, 80);
            doc.roundedRect(currentX, currentY - 5, chipWidth, 10, 5, 5, 'FD');

            // Draw text
            doc.setTextColor(76, 175, 80);
            doc.text(strength, currentX + 5, currentY);

            currentX += chipWidth + 5;
          });

          currentY += 20;
        }
      }

      // ===== VOCABULARY SECTION (if exists) =====
      if (entry.result?.vocabularyAnalysis) {
        // Check if we need a new page
        if (currentY > 200) {
          doc.addPage();
          currentY = 25;
        } else {
          currentY += 10;
        }

        // Section header
        doc.setFillColor(100, 181, 246);
        doc.rect(10, currentY, 190, 12, 'F');
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(12);
        doc.setTextColor(255, 255, 255);
        doc.text('Vocabulary Analysis', 15, currentY + 8);
        currentY += 25;

        // Lexical Diversity
        if (entry.result.vocabularyAnalysis.lexicalDiversity) {
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(10);
          doc.setTextColor(0, 0, 0);
          doc.text('Lexical Diversity', 15, currentY);
          currentY += 10;

          const lexData = entry.result.vocabularyAnalysis.lexicalDiversity;
          const lexItems = [
            ['Diversity Score', lexData.diversityScore.toFixed(2)],
            ['Unique Words', lexData.uniqueWords.toString()],
            ['Total Words', lexData.totalWords.toString()]
          ];

          autoTable(doc, {
            body: lexItems,
            startY: currentY,
            theme: 'plain',
            styles: {
              fontSize: 9,
              cellPadding: 2
            },
            columnStyles: {
              0: { cellWidth: 60, fontStyle: 'bold' },
              1: { cellWidth: 100 }
            }
          });

          currentY = (doc as any).lastAutoTable.finalY + 15;
        }

        // Overused Words
        if (entry.result.vocabularyAnalysis.overusedWords?.length > 0) {
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(10);
          doc.setTextColor(0, 0, 0);
          doc.text('Overused Words & Better Alternatives', 15, currentY);
          currentY += 10;

          const overusedWords = entry.result.vocabularyAnalysis.overusedWords;
          const tableBody = overusedWords.slice(0, 8).map((word: any) => [
            word.word,
            word.count.toString(),
            word.alternatives.slice(0, 3).join(', ')
          ]);

          autoTable(doc, {
            head: [['Word', 'Frequency', 'Better Alternatives']],
            body: tableBody,
            startY: currentY,
            styles: {
              fontSize: 9,
              cellPadding: 3,
            },
            headStyles: {
              fillColor: [100, 181, 246],
              textColor: [255, 255, 255],
              fontStyle: 'bold'
            },
            columnStyles: {
              0: { cellWidth: 30 },
              1: { cellWidth: 30, halign: 'center' },
              2: { cellWidth: 'auto' }
            }
          });

          currentY = (doc as any).lastAutoTable.finalY + 15;
        }
      }

      // ===== IMPROVEMENTS SECTION =====
      if (entry.result?.improvementSuggestions?.length > 0) {
        // Start new page for improvements
        doc.addPage();
        currentY = 25;

        // Section header
        doc.setFillColor(100, 181, 246);
        doc.rect(10, currentY, 190, 12, 'F');
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(12);
        doc.setTextColor(255, 255, 255);
        doc.text('Improvement Suggestions', 15, currentY + 8);
        currentY += 25;

        // Improvement suggestions table
        const improvementData = entry.result.improvementSuggestions
          .map((sugg: any) => [
            sugg.original,
            sugg.improved,
            sugg.explanation
          ]);

        autoTable(doc, {
          head: [['Original', 'Improved Version', 'Explanation']],
          body: improvementData,
          startY: currentY,
          styles: {
            fontSize: 8,
            cellPadding: 3,
          },
          headStyles: {
            fillColor: [76, 175, 80],
            textColor: [255, 255, 255],
            fontStyle: 'bold'
          },
          columnStyles: {
            0: { cellWidth: 50 },
            1: { cellWidth: 50 },
            2: { cellWidth: 'auto' },
          },
          margin: { left: 15, right: 15 }
        });

        currentY = (doc as any).lastAutoTable.finalY + 15;

        // Band score justification
        if (entry.result?.bandScoreJustification) {
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(10);
          doc.setTextColor(0, 0, 0);
          doc.text('Band Score Justification', 15, currentY);
          currentY += 10;

          doc.setFont('helvetica', 'normal');
          doc.setFontSize(9);
          const justificationLines = doc.splitTextToSize(entry.result.bandScoreJustification, 180);
          doc.text(justificationLines, 15, currentY);
        }
      }
    });

    // Add page numbers
    const pageCount = doc.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(8);
      doc.setTextColor(128, 128, 128);
      doc.text(`Page ${i} of ${pageCount}`, 105, 290, { align: 'center' });
    }

    // Save PDF with a timestamp
    const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    let filename = '';

    if (entryId) {
      const index = entriesToExport.findIndex(e => e.id === entryId);
      filename = `speaking-assessment-${index + 1}-${timestamp}.pdf`;
    } else {
      filename = `ielts-speaking-assessments-${timestamp}.pdf`;
    }

    doc.save(filename);
    if (!entryId) {
      handleCloseExportMenu();
    }
  };

  const hasResults = entries.some(entry => entry.result);

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 8 }}>
      <Typography variant="h4" sx={{ mb: 4 }}>
        Bulk Speaking Checker
      </Typography>

      <Paper sx={{ p: 2, mb: 4 }}>
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} md={6}>
            <Stack direction="row" spacing={2} alignItems="center">
              <Typography variant="subtitle1">
                Model Selection:
              </Typography>
              <ToggleButtonGroup
                value={selectedModel}
                exclusive
                onChange={handleModelChange}
                aria-label="model selection"
                size="small"
              >
                <ToggleButton value="gpt-4o" aria-label="gpt-4o">
                  GPT-4o
                </ToggleButton>
                <ToggleButton value="gpt-4-turbo" aria-label="gpt-4-turbo">
                  GPT-4 Turbo
                </ToggleButton>
              </ToggleButtonGroup>
            </Stack>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Global Student ID"
              placeholder="Enter student ID for all entries..."
              value={globalStudentId}
              onChange={(e) => setGlobalStudentId(e.target.value)}
              helperText="Will be applied to all entries without a specific student ID"
            />
          </Grid>
        </Grid>

        <Grid container spacing={2}>
          <Grid item>
            <Button
              variant="outlined"
              startIcon={<PlusIcon />}
              onClick={handleAddEntry}
            >
              Add Entry
            </Button>
          </Grid>
          <Grid item>
            <Button
              variant="outlined"
              startIcon={<FileIcon />}
              component="label"
            >
              Import CSV
              <input
                type="file"
                hidden
                accept=".csv"
                onChange={handleImportCSV}
              />
            </Button>
          </Grid>
          {hasResults && (
            <>
              <Grid item>
                <Button
                  variant="outlined"
                  startIcon={<DownloadIcon />}
                  onClick={handleExportCSV}
                >
                  Export CSV
                </Button>
              </Grid>
              <Grid item>
                <Button
                  variant="outlined"
                  startIcon={<FilePdf />}
                  onClick={handleExportPdf}
                >
                  Export PDF
                </Button>
              </Grid>
            </>
          )}
          <Grid item sx={{ ml: 'auto' }}>
            <Button
              variant="contained"
              startIcon={isProcessing ? <CircularProgress size={20} color="inherit" /> : <RefreshIcon />}
              onClick={handleCheck}
              disabled={isProcessing}
            >
              {isProcessing ? 'Processing...' : 'Check All'}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 4 }}>
          {error}
        </Alert>
      )}

      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 650 }} aria-label="speaking entries table">
          <TableHead>
            <TableRow>
              <TableCell width="5%">#</TableCell>
              <TableCell width="10%">Student ID</TableCell>
              <TableCell width="10%">Audio</TableCell>
              <TableCell width="25%">Transcript</TableCell>
              <TableCell width="20%">Examiner Content</TableCell>
              <TableCell width="15%">Result</TableCell>
              <TableCell width="15%">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {entries.map((entry, index) => (
              <TableRow key={entry.id}>
                <TableCell>{index + 1}</TableCell>
                <TableCell>
                  <Typography noWrap sx={{ maxWidth: 150 }}>
                    {entry.studentId || (globalStudentId ?
                      <Box component="span" sx={{ fontStyle: 'italic', color: 'text.secondary' }}>
                        {globalStudentId}
                      </Box> :
                      <Box component="span" sx={{ fontStyle: 'italic', color: 'text.secondary' }}>
                        Anonymous
                      </Box>
                    )}
                  </Typography>
                </TableCell>
                <TableCell>
                  {entry.isProcessing ? (
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                      <CircularProgress size={24} />
                      <Typography variant="caption" sx={{ mt: 1 }}>
                        Transcribing...
                      </Typography>
                    </Box>
                  ) : entry.audioURL ? (
                    <Stack direction="column" spacing={1} alignItems="center">
                      <IconButton
                        onClick={() => handlePlayPause(entry.id, entry.audioURL!)}
                        color="primary"
                      >
                        {playingId === entry.id ? <Pause size={24} /> : <Play size={24} />}
                      </IconButton>
                      <Button
                        variant="outlined"
                        size="small"
                        component="label"
                        startIcon={<FileAudio size={16} />}
                      >
                        Replace
                        <input
                          type="file"
                          hidden
                          accept="audio/*"
                          onChange={(e) => handleAudioUpload(entry.id, e)}
                        />
                      </Button>
                    </Stack>
                  ) : (
                    <Button
                      variant="outlined"
                      component="label"
                      startIcon={<FileAudio />}
                      sx={{ whiteSpace: 'nowrap' }}
                    >
                      Upload Audio
                      <input
                        type="file"
                        hidden
                        accept="audio/*"
                        onChange={(e) => handleAudioUpload(entry.id, e)}
                      />
                    </Button>
                  )}
                </TableCell>
                <TableCell>
                  <TextField
                    multiline
                    rows={4}
                    fullWidth
                    variant="outlined"
                    placeholder="Enter transcript here..."
                    value={entry.transcript}
                    onChange={(e) => handleInputChange(entry.id, 'transcript', e.target.value)}
                    error={!entry.transcript.trim()}
                    helperText={!entry.transcript.trim() ? "Transcript is required" : ""}
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    multiline
                    rows={4}
                    fullWidth
                    variant="outlined"
                    placeholder="Optional: Enter examiner's questions and prompts here..."
                    value={entry.examinerContent || ''}
                    onChange={(e) => handleInputChange(entry.id, 'examinerContent', e.target.value)}
                  />
                </TableCell>
                <TableCell>
                  {entry.isProcessing ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                      <CircularProgress size={24} />
                    </Box>
                  ) : entry.error ? (
                    <Alert severity="error" sx={{ p: 1 }}>
                      {entry.error}
                    </Alert>
                  ) : entry.result ? (
                    <Stack spacing={1}>
                      <Chip
                        label={`Overall: ${entry.result.overallScore.toFixed(1)}`}
                        color={entry.result.overallScore >= 7 ? "success" : entry.result.overallScore >= 5 ? "primary" : "warning"}
                        sx={{ fontWeight: 'bold' }}
                      />
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => handleShowDetails(entry)}
                      >
                        View Details
                      </Button>
                    </Stack>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      Not processed yet
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    {entry.result && (
                      <Tooltip title="Export as PDF">
                        <IconButton
                          color="primary"
                          onClick={() => exportAsPdf(entry.id)}
                          sx={{ mr: 1 }}
                        >
                          <FilePdf size={20} />
                        </IconButton>
                      </Tooltip>
                    )}
                    <Tooltip title="Remove Entry">
                      <IconButton
                        color="error"
                        onClick={() => handleRemoveEntry(entry.id)}
                        disabled={entries.length <= 1}
                      >
                        <TrashIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Result Detail Dialog */}
      <Dialog
        open={detailDialogOpen}
        onClose={handleCloseDetails}
        fullWidth
        maxWidth="md"
      >
        {currentDetailEntry?.result && (
          <>
            <DialogTitle>
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Typography variant="h6">Speaking Assessment Details</Typography>
                <Chip
                  label={`Overall: ${currentDetailEntry.result.overallScore.toFixed(1)}`}
                  color={currentDetailEntry.result.overallScore >= 7 ? "success" : currentDetailEntry.result.overallScore >= 5 ? "primary" : "warning"}
                  sx={{ fontWeight: 'bold' }}
                />
              </Stack>
            </DialogTitle>
            <Tabs
              value={detailTabValue}
              onChange={handleDetailTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{ px: 2, borderBottom: 1, borderColor: 'divider' }}
            >
              <Tab label="Criteria Scores" />
              <Tab label="Speech Analysis" />
              <Tab label="Vocabulary" />
              <Tab label="Improvements" />
            </Tabs>
            <DialogContent dividers>
              {detailTabValue === 0 && (
                <Grid container spacing={3}>
                  {currentDetailEntry.result.criteria.map((criterion: any, idx: number) => (
                    <Grid item xs={12} key={idx}>
                      <Card variant="outlined">
                        <CardContent>
                          <Stack spacing={2}>
                            <Stack direction="row" justifyContent="space-between" alignItems="center">
                              <Typography variant="h6">{criterion.name}</Typography>
                              <Chip
                                label={criterion.score.toFixed(1)}
                                color={criterion.score >= 7 ? "success" : criterion.score >= 5 ? "primary" : "warning"}
                              />
                            </Stack>
                            <Typography variant="body1">{criterion.feedback}</Typography>
                            <Grid container spacing={2}>
                              <Grid item xs={12} md={4}>
                                <Typography variant="subtitle2" color="success.main">Strengths</Typography>
                                <ul>
                                  {criterion.strengths.map((strength: string, i: number) => (
                                    <li key={i}><Typography variant="body2">{strength}</Typography></li>
                                  ))}
                                </ul>
                              </Grid>
                              <Grid item xs={12} md={4}>
                                <Typography variant="subtitle2" color="error.main">Weaknesses</Typography>
                                <ul>
                                  {criterion.weaknesses.map((weakness: string, i: number) => (
                                    <li key={i}><Typography variant="body2">{weakness}</Typography></li>
                                  ))}
                                </ul>
                              </Grid>
                              <Grid item xs={12} md={4}>
                                <Typography variant="subtitle2" color="primary.main">Improvements</Typography>
                                <ul>
                                  {criterion.improvements.map((improvement: string, i: number) => (
                                    <li key={i}><Typography variant="body2">{improvement}</Typography></li>
                                  ))}
                                </ul>
                              </Grid>
                            </Grid>
                          </Stack>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              )}

              {detailTabValue === 1 && (
                <Stack spacing={3}>
                  <Typography variant="h6">Speech Errors</Typography>

                  <Typography variant="subtitle2">Grammar Errors</Typography>
                  {currentDetailEntry.result.speechAnalysis.errorsByType.grammarErrors.length > 0 ? (
                    <Stack spacing={2}>
                      {currentDetailEntry.result.speechAnalysis.errorsByType.grammarErrors.map((error: any, idx: number) => (
                        <Paper key={idx} variant="outlined" sx={{ p: 2 }}>
                          <Typography variant="body2" color="error.main">{error.original}</Typography>
                          <Typography variant="body2" color="success.main">→ {error.correction}</Typography>
                          {error.explanation && (
                            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                              {error.explanation}
                            </Typography>
                          )}
                        </Paper>
                      ))}
                    </Stack>
                  ) : (
                    <Alert severity="info">No grammar errors detected</Alert>
                  )}

                  <Typography variant="subtitle2">Speech Strengths</Typography>
                  <Box>
                    {currentDetailEntry.result.speechAnalysis.speechStrengths.map((strength: string, idx: number) => (
                      <Chip
                        key={idx}
                        label={strength}
                        color="success"
                        variant="outlined"
                        sx={{ m: 0.5 }}
                      />
                    ))}
                  </Box>

                  <Box>
                    <Typography variant="h6" gutterBottom>Error Summary</Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={4}>
                        <Paper variant="outlined" sx={{ p: 2 }}>
                          <Typography variant="subtitle2">Total Errors</Typography>
                          <Typography variant="h4">{currentDetailEntry.result.speechAnalysis.totalErrors}</Typography>
                        </Paper>
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Paper variant="outlined" sx={{ p: 2 }}>
                          <Typography variant="subtitle2">Error Frequency</Typography>
                          <Typography variant="h6">{currentDetailEntry.result.speechAnalysis.errorFrequency}</Typography>
                        </Paper>
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Paper variant="outlined" sx={{ p: 2 }}>
                          <Typography variant="subtitle2">Most Common Error</Typography>
                          <Typography variant="body1">{currentDetailEntry.result.speechAnalysis.mostFrequentErrorType}</Typography>
                        </Paper>
                      </Grid>
                    </Grid>
                  </Box>
                </Stack>
              )}

              {detailTabValue === 2 && (
                <Stack spacing={3}>
                  <Box>
                    <Typography variant="h6" gutterBottom>Vocabulary Level</Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={4}>
                        <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                          <Typography variant="subtitle2">Basic</Typography>
                          <Typography variant="h5" color="text.secondary">
                            {currentDetailEntry.result.vocabularyAnalysis.vocabularyLevel.distribution.basic}
                          </Typography>
                        </Paper>
                      </Grid>
                      <Grid item xs={4}>
                        <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                          <Typography variant="subtitle2">Intermediate</Typography>
                          <Typography variant="h5" color="primary.main">
                            {currentDetailEntry.result.vocabularyAnalysis.vocabularyLevel.distribution.intermediate}
                          </Typography>
                        </Paper>
                      </Grid>
                      <Grid item xs={4}>
                        <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                          <Typography variant="subtitle2">Advanced</Typography>
                          <Typography variant="h5" color="success.main">
                            {currentDetailEntry.result.vocabularyAnalysis.vocabularyLevel.distribution.advanced}
                          </Typography>
                        </Paper>
                      </Grid>
                    </Grid>
                  </Box>

                  <Typography variant="h6">Overused Words</Typography>
                  <Grid container spacing={2}>
                    {currentDetailEntry.result.vocabularyAnalysis.overusedWords.map((word: any, idx: number) => (
                      <Grid item xs={12} sm={6} md={4} key={idx}>
                        <Paper variant="outlined" sx={{ p: 2 }}>
                          <Stack spacing={1}>
                            <Stack direction="row" justifyContent="space-between">
                              <Typography variant="subtitle2" color="error.main">{word.word}</Typography>
                              <Chip size="small" label={`${word.count} times`} color="warning" />
                            </Stack>
                            <Typography variant="body2">Better alternatives:</Typography>
                            <Box>
                              {word.alternatives.map((alt: string, i: number) => (
                                <Chip key={i} label={alt} size="small" color="success" variant="outlined" sx={{ m: 0.5 }} />
                              ))}
                            </Box>
                          </Stack>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>

                  <Typography variant="h6">Lexical Diversity</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Paper variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="subtitle2">Unique Words</Typography>
                        <Typography variant="h5">{currentDetailEntry.result.vocabularyAnalysis.lexicalDiversity.uniqueWords}</Typography>
                      </Paper>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Paper variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="subtitle2">Total Words</Typography>
                        <Typography variant="h5">{currentDetailEntry.result.vocabularyAnalysis.lexicalDiversity.totalWords}</Typography>
                      </Paper>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Paper variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="subtitle2">Diversity Score</Typography>
                        <Typography variant="h5">{currentDetailEntry.result.vocabularyAnalysis.lexicalDiversity.diversityScore.toFixed(2)}</Typography>
                      </Paper>
                    </Grid>
                  </Grid>
                </Stack>
              )}

              {detailTabValue === 3 && (
                <Stack spacing={3}>
                  <Typography variant="h6">Improvement Suggestions</Typography>
                  {currentDetailEntry.result.improvementSuggestions.map((suggestion: any, idx: number) => (
                    <Paper key={idx} variant="outlined" sx={{ p: 2 }}>
                      <Stack spacing={1}>
                        <Typography variant="body1" color="error.main">{suggestion.original}</Typography>
                        <Typography variant="body1" color="success.main">→ {suggestion.improved}</Typography>
                        <Typography variant="body2" color="text.secondary">{suggestion.explanation}</Typography>
                      </Stack>
                    </Paper>
                  ))}

                  <Paper variant="outlined" sx={{ p: 2, mt: 3 }}>
                    <Typography variant="h6" gutterBottom>Band Score Justification</Typography>
                    <Typography variant="body1">{currentDetailEntry.result.bandScoreJustification}</Typography>
                  </Paper>
                </Stack>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDetails}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Container>
  );
}
/**
 * Score to band conversion utility functions
 */

/**
 * Reading score to band mapping
 */
export const readingScoreToBandMap: Record<string, number> = {
  '39-40': 9.0, '37-38': 8.5, '35-36': 8.0,
  '33-34': 7.5, '30-32': 7.0, '27-29': 6.5,
  '23-26': 6.0, '20-22': 5.5, '15-19': 5.0,
  '13-14': 4.5, '10-12': 4.0, '8-9': 3.5,
  '6-7': 3.0, '4-5': 2.5, '2-3': 2.0,
  '1': 1.0, '0': 0.0
};

/**
 * Listening score to band mapping
 */
export const listeningScoreToBandMap: Record<string, number> = {
  '39-40': 9.0, '37-38': 8.5, '35-36': 8.0,
  '32-34': 7.5, '30-31': 7.0, '26-29': 6.5,
  '23-25': 6.0, '18-22': 5.5, '16-17': 5.0,
  '13-15': 4.5, '10-12': 4.0, '8-9': 3.5,
  '6-7': 3.0, '4-5': 2.5, '2-3': 2.0,
  '1': 1.0, '0': 0.0
};

/**
 * Convert raw score to band score for reading
 *
 * @param rawScore - Raw score in format "30/40" or just the number of correct answers
 * @returns The band score (0-9)
 */
export function convertReadingRawScoreToBand(rawScore: string): number {
  // Extract the score from the raw score string
  let score: number;

  if (rawScore.includes('/')) {
    // Format is "30/40"
    const [correct, total] = rawScore.split('/').map(s => parseInt(s.trim(), 10));
    score = correct;
  } else {
    // Format is just the number
    score = parseInt(rawScore.trim(), 10);
  }

  // Find the matching band score
  for (const [range, band] of Object.entries(readingScoreToBandMap)) {
    if (range.includes('-')) {
      const [min, max] = range.split('-').map(s => parseInt(s, 10));
      if (score >= min && score <= max) {
        return band;
      }
    } else {
      if (score === parseInt(range, 10)) {
        return band;
      }
    }
  }

  // Default to 0 if no match found
  return 0;
}

/**
 * Convert raw score to band score for listening
 *
 * @param rawScore - Raw score in format "30/40" or just the number of correct answers
 * @returns The band score (0-9)
 */
export function convertListeningRawScoreToBand(rawScore: string): number {
  // Extract the score from the raw score string
  let score: number;

  if (rawScore.includes('/')) {
    // Format is "30/40"
    const [correct, total] = rawScore.split('/').map(s => parseInt(s.trim(), 10));
    score = correct;
  } else {
    // Format is just the number
    score = parseInt(rawScore.trim(), 10);
  }

  // Find the matching band score
  for (const [range, band] of Object.entries(listeningScoreToBandMap)) {
    if (range.includes('-')) {
      const [min, max] = range.split('-').map(s => parseInt(s, 10));
      if (score >= min && score <= max) {
        return band;
      }
    } else {
      if (score === parseInt(range, 10)) {
        return band;
      }
    }
  }

  // Default to 0 if no match found
  return 0;
}

/**
 * Generate standardized feedback based on band score
 *
 * @param band - The band score (0-9)
 * @param testType - The test type ('reading', 'listening', 'writing', or 'speaking')
 * @returns Standardized feedback object
 */
export function generateStandardizedFeedback(band: number, testType: 'reading' | 'listening' | 'writing' | 'speaking'): {
  analysis: string;
  strengths: string[];
  weaknesses: string[];
  improvements: string[];
} {
  // Default feedback
  const feedback: {
    analysis: string;
    strengths: string[];
    weaknesses: string[];
    improvements: string[];
  } = {
    analysis: '',
    strengths: [],
    weaknesses: [],
    improvements: []
  };

  // Generate feedback based on band score
  if (band >= 8.0) {
    feedback.analysis = `Excellent ${testType} skills at a very advanced level.`;
    feedback.strengths = [
      `Outstanding ${testType} comprehension`,
      'Excellent understanding of complex information',
      'Strong ability to identify key details'
    ];
    feedback.weaknesses = [
      'Occasional minor misunderstandings with very complex content',
      'Rare instances of missing subtle details',
      'May occasionally misinterpret nuanced information'
    ];
    feedback.improvements = [
      'Focus on maintaining consistent performance',
      'Continue exposure to complex academic content',
      'Practice with specialized technical vocabulary'
    ];
  } else if (band >= 7.0) {
    feedback.analysis = `Good ${testType} skills at an advanced level with occasional limitations.`;
    feedback.strengths = [
      'Strong overall comprehension',
      'Good understanding of complex information',
      'Ability to identify most key details'
    ];
    feedback.weaknesses = [
      'Occasional difficulty with complex content',
      'Sometimes misses subtle details',
      'May struggle with unfamiliar topics'
    ];
    feedback.improvements = [
      'Practice with more complex academic content',
      'Focus on identifying subtle details',
      'Expand vocabulary in specialized areas'
    ];
  } else if (band >= 6.0) {
    feedback.analysis = `Competent ${testType} skills with some limitations.`;
    feedback.strengths = [
      'Good general comprehension',
      'Able to understand main ideas',
      'Can follow straightforward information'
    ];
    feedback.weaknesses = [
      'Difficulty with complex content',
      'Often misses important details',
      'Limited understanding of specialized vocabulary'
    ];
    feedback.improvements = [
      'Practice with academic content',
      'Focus on identifying specific details',
      'Expand academic vocabulary'
    ];
  } else if (band >= 5.0) {
    feedback.analysis = `Modest ${testType} skills with noticeable limitations.`;
    feedback.strengths = [
      'Can understand simple information',
      'Able to identify basic main ideas',
      'Follows straightforward content'
    ];
    feedback.weaknesses = [
      'Significant difficulty with complex content',
      'Misses many important details',
      'Limited vocabulary affects comprehension'
    ];
    feedback.improvements = [
      'Practice with intermediate-level content',
      'Focus on identifying main ideas and supporting details',
      'Build general vocabulary'
    ];
  } else {
    feedback.analysis = `Limited ${testType} skills requiring substantial improvement.`;
    feedback.strengths = [
      'Can understand very basic information',
      'Recognizes some common words and phrases',
      'Can follow simple, concrete information'
    ];
    feedback.weaknesses = [
      'Major difficulty understanding most content',
      'Very limited vocabulary',
      'Struggles with connecting ideas'
    ];
    feedback.improvements = [
      'Focus on building basic vocabulary',
      'Practice with simple, everyday content',
      'Develop basic comprehension strategies'
    ];
  }

  return feedback;
}

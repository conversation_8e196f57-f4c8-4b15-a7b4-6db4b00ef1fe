import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { createReadingEntryWithRawScore, updateReadingEntryWithRawScore } from '@/lib/reading-entry-utils';
import { createListeningEntryWithRawScore, updateListeningEntryWithRawScore } from '@/lib/listening-entry-utils';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { entryId, componentType, studentId, materialId, raw_score } = body;

    console.log('Processing simplified component:', { componentType, entryId, studentId, materialId, raw_score });

    if (!componentType) {
      return NextResponse.json({ error: 'Component type is required' }, { status: 400 });
    }

    if (!studentId) {
      return NextResponse.json({ error: 'Student ID is required' }, { status: 400 });
    }

    // Material ID is now optional

    if (!raw_score) {
      return NextResponse.json({ error: 'Raw score is required' }, { status: 400 });
    }

    let result;

    // Process based on component type
    if (componentType === 'reading') {
      if (entryId) {
        // Update existing entry
        result = await updateReadingEntryWithRawScore(entryId, raw_score);
      } else {
        // Create new entry
        result = await createReadingEntryWithRawScore({
          studentId,
          materialTitle: 'Global Reading Test',
          raw_score
        });
      }
    } else if (componentType === 'listening') {
      if (entryId) {
        // Update existing entry
        result = await updateListeningEntryWithRawScore(entryId, raw_score);
      } else {
        // Create new entry
        result = await createListeningEntryWithRawScore({
          studentId,
          materialTitle: 'Global Listening Test',
          raw_score
        });
      }
    } else {
      return NextResponse.json({ error: 'Invalid component type' }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      entryId: result.id,
      band: result.band,
      strengths: result.strengths,
      weaknesses: result.weaknesses,
      improvementSuggestions: result.improvementSuggestions
    });
  } catch (error) {
    console.error('Error processing simplified component:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

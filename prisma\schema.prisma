// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  output        = "../src/generated/prisma"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Student model to store student information
model Student {
  id        String   @id @default(uuid())
  name      String?
  email     String?  @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  writingEntries   WritingEntry[]
  speakingEntries  SpeakingEntry[]
  readingEntries   ReadingEntry[]
  listeningEntries ListeningEntry[]
  reports          Report[]
}

// WritingMaterial model to store writing test materials
model WritingMaterial {
  id          String   @id @default(uuid())
  title       String
  taskType    String   // 'task1', 'task2', 'both'
  taskQuestion String?  @db.Text
  taskImage   String?  // URL to image for Task 1
  sampleAnswer String?  @db.Text
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  writingEntries WritingEntry[]
}

// WritingEntry model to store writing test results
model WritingEntry {
  id          String   @id @default(uuid())
  studentId   String
  materialId  String?
  taskType    String   // 'task1', 'task2', 'both'
  essayText   String?  @db.Text  // Making this optional since we might start with just an image
  taskQuestion String?  @db.Text
  imageUrl    String?  // URL to the handwritten essay image
  extractedWithClaude Boolean? // Whether the text was extracted with Claude 3.5

  // Result data
  band        Float?
  criteriaScores Json?
  feedback    String?  @db.Text
  strengths   Json?
  weaknesses  Json?
  improvementSuggestions Json?

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  student     Student  @relation(fields: [studentId], references: [id])
  material    WritingMaterial? @relation(fields: [materialId], references: [id])
}

// SpeakingEntry model to store speaking test results
model SpeakingEntry {
  id           String   @id @default(uuid())
  studentId    String
  audioUrl     String?
  transcription String?  @db.Text
  partNumber   Int?     // 1, 2, 3, or null for full test
  examinerContent String? @db.Text
  isSimulated  Boolean? @default(false) // Flag to indicate if the transcript is simulated

  // Result data
  band         Float?
  criteriaScores Json?
  feedback     String? @db.Text
  strengths    Json?
  weaknesses   Json?
  improvementSuggestions Json?

  // Metadata
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  student      Student  @relation(fields: [studentId], references: [id])
}

// ReadingMaterial model to store reading test materials
model ReadingMaterial {
  id          String   @id @default(uuid())
  title       String
  passage     String   @db.Text
  questions   Json
  answers     Json
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // No longer has relations to ReadingEntry
}

// ReadingEntry model to store reading test results
model ReadingEntry {
  id          String   @id @default(uuid())
  studentId   String
  materialId  String?  // Made optional and removed foreign key constraint
  answers     Json
  imageUrl    String?
  extractedText String? @db.Text  // Text extracted from the image
  extractedWithClaude Boolean? // Whether the text was extracted with Claude 3.5

  // Result data
  score       Int?
  band        Float?
  correctAnswers Int?
  totalQuestions Int?
  mistakes    Json?
  strengths   Json?
  weaknesses  Json?
  improvementSuggestions Json?
  metadata    Json?    // For storing additional data like correctAnswersText

  // New simplified fields
  raw_score   String?  // Raw score in format "30/40"
  materialTitle String? // Added to store material title without foreign key

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  student     Student  @relation(fields: [studentId], references: [id])
}

// ListeningMaterial model to store listening test materials
model ListeningMaterial {
  id          String   @id @default(uuid())
  title       String
  audioUrl    String
  transcript  String?  @db.Text
  section     Int?     // 1, 2, 3, or 4
  questions   Json
  answers     Json
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // No longer has relations to ListeningEntry
}

// ListeningEntry model to store listening test results
model ListeningEntry {
  id          String   @id @default(uuid())
  studentId   String
  materialId  String?  // Made optional and removed foreign key constraint
  answers     Json
  imageUrl    String?
  extractedText String? @db.Text  // Text extracted from the image
  extractedWithClaude Boolean? // Whether the text was extracted with Claude 3.5

  // Result data
  score       Int?
  band        Float?
  correctAnswers Int?
  totalQuestions Int?
  mistakes    Json?
  strengths   Json?
  weaknesses  Json?
  improvementSuggestions Json?
  criteriaScores Json?  // Added missing field for section scores
  metadata    Json?    // For storing additional data like correctAnswersText

  // New simplified fields
  raw_score   String?  // Raw score in format "30/40"
  materialTitle String? // Added to store material title without foreign key

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  student     Student  @relation(fields: [studentId], references: [id])
}

// Report model to store comprehensive reports
model Report {
  id          String   @id @default(uuid())
  studentId   String
  candidateName String?
  testDate    DateTime?

  // Test results
  readingResult Json?
  listeningResult Json?
  writingResult Json?
  speakingResult Json?

  // Overall assessment
  overallBand Float?
  overallStrengths Json?
  overallWeaknesses Json?
  overallImprovements Json?

  // Report status
  printStatus String? // 'pending', 'printed'
  folderAssignment String?

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  student     Student  @relation(fields: [studentId], references: [id])
}

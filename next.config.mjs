/** @type {import('next').NextConfig} */
const config = {
  // Disable standalone output to avoid symlink permission issues
  // output: 'standalone',

  // Configure headers for security
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ];
  },

  // Disable static optimization for dynamic routes
  experimental: {
    // This allows dynamic routes to work properly
    serverComponentsExternalPackages: ['@prisma/client', 'prisma']
  }
};

export default config;

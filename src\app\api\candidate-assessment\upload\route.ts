import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { studentId, componentType, content } = body;

    // Validate input
    if (!studentId || !componentType || !content) {
      return NextResponse.json(
        { error: 'Student ID, component type, and content are required' },
        { status: 400 }
      );
    }

    // Check if student exists
    let student;
    try {
      student = await prisma.student.findUnique({
        where: { id: studentId },
      });

      if (!student) {
        console.log(`Student with ID ${studentId} not found, creating a new student record`);

        // Create the student if it doesn't exist
        student = await prisma.student.create({
          data: {
            id: studentId,
            name: `Student ${studentId}`,
          },
        });

        console.log(`Created new student with ID ${student.id}`);
      }
    } catch (studentError) {
      console.error('Error finding or creating student:', studentError);
      return NextResponse.json(
        { error: 'Failed to find or create student', details: studentError instanceof Error ? studentError.message : 'Unknown error' },
        { status: 500 }
      );
    }

    let result;

    // Handle different component types
    switch (componentType) {
      case 'listening':
        // Create listening entry
        result = await prisma.listeningEntry.create({
          data: {
            studentId,
            materialId: content.materialId,
            answers: content.answers,
            imageUrl: content.imageUrl,
          },
        });
        break;

      case 'reading':
        // Create reading entry
        result = await prisma.readingEntry.create({
          data: {
            studentId,
            materialId: content.materialId,
            answers: content.answers,
            imageUrl: content.imageUrl,
          },
        });
        break;

      case 'writing_task1':
        // Ensure we have task questions
        let task1Questions = content.taskQuestions || content.taskQuestion || '';
        if (!task1Questions || task1Questions.trim() === '') {
          task1Questions = 'Default Task 1 question: Describe the chart or graph.';
        }

        console.log('Creating writing task 1 entry with data:', {
          studentId,
          taskQuestion: task1Questions,
          hasEssayText: !!(content.essayText)
        });

        try {
          // First, check if we need to create a default writing material
          let writingMaterial = await prisma.writingMaterial.findFirst({
            where: { title: 'Default Writing Task 1 Material' }
          });

          if (!writingMaterial) {
            // Create a default material if none exists
            writingMaterial = await prisma.writingMaterial.create({
              data: {
                title: 'Default Writing Task 1 Material',
                taskType: 'task1',
                taskQuestion: task1Questions
              }
            });
            console.log('Created default writing task 1 material with ID:', writingMaterial.id);
          }

          // Create writing entry for Task 1
          result = await prisma.writingEntry.create({
            data: {
              studentId,
              materialId: writingMaterial.id, // Use the default material ID
              taskType: 'task1',
              essayText: content.essayText || 'Pending AI analysis',
              taskQuestion: task1Questions,
            },
          });

          console.log('Writing task 1 entry created successfully with ID:', result.id);
        } catch (prismaError) {
          console.error('Prisma error creating writing task 1 entry:', prismaError);

          // Try a fallback approach with minimal data
          try {
            console.log('Attempting fallback approach for writing task 1 entry creation');
            result = await prisma.writingEntry.create({
              data: {
                studentId,
                taskType: 'task1',
                essayText: 'Pending analysis', // Minimal required data
              },
            });
            console.log('Fallback approach succeeded with ID:', result.id);
          } catch (fallbackError) {
            console.error('Fallback approach also failed:', fallbackError);
            throw prismaError; // Re-throw the original error
          }
        }
        break;

      case 'writing_task2':
        // Ensure we have task questions
        let task2Questions = content.taskQuestions || content.taskQuestion || '';
        if (!task2Questions || task2Questions.trim() === '') {
          task2Questions = 'Default Task 2 question: Write an essay on the given topic.';
        }

        console.log('Creating writing task 2 entry with data:', {
          studentId,
          taskQuestion: task2Questions,
          hasEssayText: !!(content.essayText)
        });

        try {
          // First, check if we need to create a default writing material
          let writingMaterial = await prisma.writingMaterial.findFirst({
            where: { title: 'Default Writing Task 2 Material' }
          });

          if (!writingMaterial) {
            // Create a default material if none exists
            writingMaterial = await prisma.writingMaterial.create({
              data: {
                title: 'Default Writing Task 2 Material',
                taskType: 'task2',
                taskQuestion: task2Questions
              }
            });
            console.log('Created default writing task 2 material with ID:', writingMaterial.id);
          }

          // Create writing entry for Task 2
          result = await prisma.writingEntry.create({
            data: {
              studentId,
              materialId: writingMaterial.id, // Use the default material ID
              taskType: 'task2',
              essayText: content.essayText || 'Pending AI analysis',
              taskQuestion: task2Questions,
            },
          });

          console.log('Writing task 2 entry created successfully with ID:', result.id);
        } catch (prismaError) {
          console.error('Prisma error creating writing task 2 entry:', prismaError);

          // Try a fallback approach with minimal data
          try {
            console.log('Attempting fallback approach for writing task 2 entry creation');
            result = await prisma.writingEntry.create({
              data: {
                studentId,
                taskType: 'task2',
                essayText: 'Pending analysis', // Minimal required data
              },
            });
            console.log('Fallback approach succeeded with ID:', result.id);
          } catch (fallbackError) {
            console.error('Fallback approach also failed:', fallbackError);
            throw prismaError; // Re-throw the original error
          }
        }
        break;

      case 'speaking':
        // Create speaking entry
        result = await prisma.speakingEntry.create({
          data: {
            studentId,
            audioUrl: content.audioUrl,
            transcription: content.transcription,
            partNumber: content.partNumber,
            examinerContent: content.examinerContent,
          },
        });
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid component type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      id: result.id,
      status: 'success'
    });
  } catch (error) {
    console.error('Error uploading component:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to upload component' },
      { status: 500 }
    );
  }
}

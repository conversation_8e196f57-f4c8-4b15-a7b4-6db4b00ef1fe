'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  CircularProgress,
  Snackbar,
  Alert,
  Chip
} from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon, Edit as EditIcon } from '@mui/icons-material';
import ScoreInput from '@/components/ScoreInput';

// Interface for reading material
interface ReadingMaterial {
  id: string;
  title: string;
}

// Interface for simplified reading entry
interface SimplifiedReadingEntry {
  id?: string;
  studentId: string;
  materialId: string;
  materialTitle?: string;
  raw_score: string;
  band?: number;
  status: 'pending' | 'processing' | 'completed' | 'error';
  strengths?: string[];
  weaknesses?: string[];
  improvementSuggestions?: string[];
  error?: string;
}

export default function SimplifiedReadingCheckerPage() {
  // State for entries
  const [entries, setEntries] = useState<SimplifiedReadingEntry[]>([]);

  // State for materials
  const [materials, setMaterials] = useState<ReadingMaterial[]>([]);

  // State for loading
  const [loading, setLoading] = useState(false);

  // State for error
  const [error, setError] = useState<string | null>(null);

  // State for global student ID
  const [globalStudentId, setGlobalStudentId] = useState('');

  // State for add entry dialog
  const [showAddDialog, setShowAddDialog] = useState(false);

  // State for new entry
  const [newEntry, setNewEntry] = useState<{
    studentId: string;
    materialId: string;
    raw_score: string;
    band?: number;
  }>({
    studentId: '',
    materialId: '',
    raw_score: '',
  });

  // State for edit entry dialog
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editEntry, setEditEntry] = useState<SimplifiedReadingEntry | null>(null);

  // State for delete confirmation dialog
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteEntryId, setDeleteEntryId] = useState<string | null>(null);

  // State for snackbar
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning'
  });

  // State for detail dialog
  const [showDetailDialog, setShowDetailDialog] = useState(false);
  const [detailEntry, setDetailEntry] = useState<SimplifiedReadingEntry | null>(null);

  // Fetch materials on component mount
  useEffect(() => {
    const fetchMaterials = async () => {
      try {
        const response = await fetch('/api/reading-materials');
        if (!response.ok) {
          throw new Error('Failed to fetch reading materials');
        }
        const data = await response.json();
        setMaterials(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      }
    };

    fetchMaterials();
  }, []);

  // Handle add entry
  const handleAddEntry = async () => {
    try {
      setLoading(true);

      // Use global student ID if not provided
      const studentId = newEntry.studentId || globalStudentId;

      if (!studentId) {
        throw new Error('Student ID is required');
      }

      if (!newEntry.materialId) {
        throw new Error('Material is required');
      }

      if (!newEntry.raw_score) {
        throw new Error('Raw score is required');
      }

      // Create entry object
      const entry: SimplifiedReadingEntry = {
        studentId,
        materialId: newEntry.materialId,
        raw_score: newEntry.raw_score,
        status: 'pending'
      };

      // Add material title for display
      const material = materials.find(m => m.id === entry.materialId);
      if (material) {
        entry.materialTitle = material.title;
      }

      // Send to API
      const response = await fetch('/api/simplified-reading-checker', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ entries: [entry] }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add entry');
      }

      const data = await response.json();

      // Update entries list with the new entry
      if (data.results && data.results.length > 0) {
        const result = data.results[0];

        // Add material title for display
        const material = materials.find(m => m.id === result.materialId);
        if (material) {
          result.materialTitle = material.title;
        }

        setEntries(prev => [...prev, result]);

        // Show success message
        setSnackbar({
          open: true,
          message: 'Entry added successfully',
          severity: 'success'
        });

        // Reset new entry form and close dialog
        setNewEntry({
          studentId: '',
          materialId: '',
          raw_score: '',
        });
        setShowAddDialog(false);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'An unexpected error occurred',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle edit entry
  const handleEditEntry = async () => {
    if (!editEntry) return;

    try {
      setLoading(true);

      // Validate required fields
      if (!editEntry.studentId) {
        throw new Error('Student ID is required');
      }

      if (!editEntry.materialId) {
        throw new Error('Material is required');
      }

      if (!editEntry.raw_score) {
        throw new Error('Raw score is required');
      }

      // Send to API
      const response = await fetch('/api/simplified-reading-checker', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ entries: [editEntry] }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update entry');
      }

      const data = await response.json();

      // Update entries list with the updated entry
      if (data.results && data.results.length > 0) {
        const result = data.results[0];

        // Add material title for display
        const material = materials.find(m => m.id === result.materialId);
        if (material) {
          result.materialTitle = material.title;
        }

        setEntries(prev => prev.map(e => e.id === result.id ? result : e));

        // Show success message
        setSnackbar({
          open: true,
          message: 'Entry updated successfully',
          severity: 'success'
        });

        // Reset edit entry form and close dialog
        setEditEntry(null);
        setShowEditDialog(false);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'An unexpected error occurred',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle delete entry
  const handleDeleteEntry = async () => {
    if (!deleteEntryId) return;

    try {
      setLoading(true);

      // Remove entry from state
      setEntries(prev => prev.filter(e => e.id !== deleteEntryId));

      // Show success message
      setSnackbar({
        open: true,
        message: 'Entry deleted successfully',
        severity: 'success'
      });

      // Reset delete entry ID and close dialog
      setDeleteEntryId(null);
      setShowDeleteDialog(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'An unexpected error occurred',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle view details
  const handleViewDetails = (entry: SimplifiedReadingEntry) => {
    setDetailEntry(entry);
    setShowDetailDialog(true);
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Simplified Reading Checker
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => setShowAddDialog(true)}
        >
          Add Entry
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Global Student ID"
                value={globalStudentId}
                onChange={(e) => setGlobalStudentId(e.target.value)}
                helperText="This ID will be used for all entries without a specific student ID"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        <TableContainer sx={{ maxHeight: 440 }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell>Student ID</TableCell>
                <TableCell>Material</TableCell>
                <TableCell>Raw Score</TableCell>
                <TableCell>Band Score</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {entries.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    No entries found. Add an entry to get started.
                  </TableCell>
                </TableRow>
              ) : (
                entries.map((entry) => (
                  <TableRow key={entry.id || `temp-${entry.studentId}-${entry.materialId}`}>
                    <TableCell>{entry.studentId}</TableCell>
                    <TableCell>{entry.materialTitle || entry.materialId}</TableCell>
                    <TableCell>{entry.raw_score}</TableCell>
                    <TableCell>
                      {entry.band !== undefined ? (
                        <Chip
                          label={entry.band.toFixed(1)}
                          color={
                            entry.band >= 7 ? 'success' :
                            entry.band >= 5.5 ? 'primary' :
                            'warning'
                          }
                        />
                      ) : '-'}
                    </TableCell>
                    <TableCell>
                      {entry.status === 'pending' && <Chip label="Pending" color="default" />}
                      {entry.status === 'processing' && <Chip label="Processing" color="info" />}
                      {entry.status === 'completed' && <Chip label="Completed" color="success" />}
                      {entry.status === 'error' && (
                        <Chip
                          label="Error"
                          color="error"
                          title={entry.error}
                        />
                      )}
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" spacing={1}>
                        <Button
                          size="small"
                          onClick={() => {
                            setEditEntry(entry);
                            setShowEditDialog(true);
                          }}
                          startIcon={<EditIcon />}
                        >
                          Edit
                        </Button>
                        <Button
                          size="small"
                          color="error"
                          onClick={() => {
                            setDeleteEntryId(entry.id || null);
                            setShowDeleteDialog(true);
                          }}
                          startIcon={<DeleteIcon />}
                        >
                          Delete
                        </Button>
                        {entry.status === 'completed' && (
                          <Button
                            size="small"
                            color="info"
                            onClick={() => handleViewDetails(entry)}
                          >
                            Details
                          </Button>
                        )}
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Add Entry Dialog */}
      <Dialog open={showAddDialog} onClose={() => setShowAddDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add Reading Entry</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              fullWidth
              label="Student ID"
              value={newEntry.studentId}
              onChange={(e) => setNewEntry({ ...newEntry, studentId: e.target.value })}
              helperText={globalStudentId ? `Using global ID: ${globalStudentId} if left empty` : 'Leave empty to use global ID'}
            />
            <FormControl fullWidth>
              <InputLabel id="material-select-label">Reading Material</InputLabel>
              <Select
                labelId="material-select-label"
                value={newEntry.materialId}
                label="Reading Material"
                onChange={(e) => setNewEntry({ ...newEntry, materialId: e.target.value })}
              >
                {materials.map((material) => (
                  <MenuItem key={material.id} value={material.id}>
                    {material.title}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <ScoreInput
              type="reading"
              initialValue={newEntry.raw_score}
              onChange={(value, band) => setNewEntry({ ...newEntry, raw_score: value, band })}
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAddDialog(false)}>Cancel</Button>
          <Button
            onClick={handleAddEntry}
            variant="contained"
            color="primary"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Add Entry'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Entry Dialog */}
      <Dialog open={showEditDialog} onClose={() => setShowEditDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Edit Reading Entry</DialogTitle>
        <DialogContent>
          {editEntry && (
            <Stack spacing={3} sx={{ mt: 1 }}>
              <TextField
                fullWidth
                label="Student ID"
                value={editEntry.studentId}
                onChange={(e) => setEditEntry({ ...editEntry, studentId: e.target.value })}
              />
              <FormControl fullWidth>
                <InputLabel id="edit-material-select-label">Reading Material</InputLabel>
                <Select
                  labelId="edit-material-select-label"
                  value={editEntry.materialId}
                  label="Reading Material"
                  onChange={(e) => setEditEntry({ ...editEntry, materialId: e.target.value })}
                >
                  {materials.map((material) => (
                    <MenuItem key={material.id} value={material.id}>
                      {material.title}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <ScoreInput
                type="reading"
                initialValue={editEntry.raw_score}
                onChange={(value, band) => setEditEntry({ ...editEntry, raw_score: value, band })}
              />
            </Stack>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowEditDialog(false)}>Cancel</Button>
          <Button
            onClick={handleEditEntry}
            variant="contained"
            color="primary"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Update Entry'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onClose={() => setShowDeleteDialog(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>Are you sure you want to delete this entry?</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDeleteDialog(false)}>Cancel</Button>
          <Button
            onClick={handleDeleteEntry}
            variant="contained"
            color="error"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Detail Dialog */}
      <Dialog open={showDetailDialog} onClose={() => setShowDetailDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Entry Details</DialogTitle>
        <DialogContent dividers>
          {detailEntry && (
            <Stack spacing={3}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Student ID</Typography>
                  <Typography variant="body1">{detailEntry.studentId}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Material</Typography>
                  <Typography variant="body1">{detailEntry.materialTitle || detailEntry.materialId}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Raw Score</Typography>
                  <Typography variant="body1">{detailEntry.raw_score}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Band Score</Typography>
                  <Typography variant="body1" fontWeight="bold" color="primary">
                    {detailEntry.band !== undefined ? detailEntry.band.toFixed(1) : '-'}
                  </Typography>
                </Grid>
              </Grid>

              <Divider />

              <Typography variant="h6">Strengths</Typography>
              {detailEntry.strengths && detailEntry.strengths.length > 0 ? (
                <ul>
                  {detailEntry.strengths.map((strength, index) => (
                    <li key={index}>{strength}</li>
                  ))}
                </ul>
              ) : (
                <Typography variant="body2" color="text.secondary">No strengths available</Typography>
              )}

              <Typography variant="h6">Weaknesses</Typography>
              {detailEntry.weaknesses && detailEntry.weaknesses.length > 0 ? (
                <ul>
                  {detailEntry.weaknesses.map((weakness, index) => (
                    <li key={index}>{weakness}</li>
                  ))}
                </ul>
              ) : (
                <Typography variant="body2" color="text.secondary">No weaknesses available</Typography>
              )}

              <Typography variant="h6">Improvement Suggestions</Typography>
              {detailEntry.improvementSuggestions && detailEntry.improvementSuggestions.length > 0 ? (
                <ul>
                  {detailEntry.improvementSuggestions.map((suggestion, index) => (
                    <li key={index}>{suggestion}</li>
                  ))}
                </ul>
              ) : (
                <Typography variant="body2" color="text.secondary">No improvement suggestions available</Typography>
              )}
            </Stack>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDetailDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
}

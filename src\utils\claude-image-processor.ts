/**
 * Utility for processing images with Claude 3.5 Sonnet
 */
import Anthropic from '@anthropic-ai/sdk';

// Get API key from environment variables (will be accessed in API routes)
const ANTHROPIC_API_KEY = process.env.ANTHROPIC_API_KEY;

/**
 * Extract text from an image using Claude 3.5 Sonnet
 * @param imageFile The image file to process
 * @param taskType The type of task (Task 1, Task 2, listening, reading, etc.)
 * @param options Additional options for processing
 * @returns The extracted text
 */
export async function extractTextFromImage(
  imageFile: File | Blob,
  taskType: string,
  options: {
    pageNumber?: string;
    totalPages?: string;
    anthropicApiKey?: string;
  } = {}
): Promise<string> {
  // Use provided API key or fallback to environment variable
  const apiKey = options.anthropicApiKey || ANTHROPIC_API_KEY;

  if (!apiKey) {
    throw new Error('ANTHROPIC_API_KEY is not set');
  }

  // Convert file to base64
  const bytes = await imageFile.arrayBuffer();
  const buffer = Buffer.from(bytes);
  const base64Image = buffer.toString('base64');

  // Determine media type
  let mediaType: "image/jpeg" | "image/png" | "image/gif" | "image/webp" = "image/jpeg";
  if (imageFile.type && ['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(imageFile.type)) {
    mediaType = imageFile.type as any;
  }

  // Initialize Anthropic client
  const anthropic = new Anthropic({
    apiKey,
  });

  // Create prompt based on task type
  let systemPrompt = "You are an expert OCR system specialized in extracting handwritten text from IELTS answer sheets. Your primary goal is to accurately transcribe the handwritten content exactly as written, preserving all original words, spelling, grammar, and paragraph structure. Pay careful attention to text written on grid/lined paper. ";

  // Add page context if this is part of a multi-page document
  const pageNumber = options.pageNumber || '1';
  const totalPages = options.totalPages || '1';

  if (parseInt(totalPages) > 1) {
    systemPrompt += `This is page ${pageNumber} of ${totalPages} of the same document. `;

    if (pageNumber === '1') {
      systemPrompt += "This is the first page. Focus on capturing the introduction and beginning. ";
    } else if (pageNumber === totalPages) {
      systemPrompt += "This is the last page. Focus on capturing the conclusion and final thoughts. ";
    } else {
      systemPrompt += "This is a middle page. Pay attention to how this content connects with previous and following pages. ";
    }

    // Add instructions for handling page transitions
    systemPrompt += "Be aware that text may continue from the previous page or to the next page. "
    + "If a sentence appears to be cut off at the beginning or end of the page, indicate this with [...] at that point. "
    + "This will help when combining multiple pages into a single coherent document.";
  }

  // Customize prompt based on task type
  if (taskType === 'Task 1' || taskType === 'writing_task1') {
    systemPrompt += "This is an IELTS Writing Task 1, which typically describes graphs, charts, tables, or processes. Focus on preserving numeric data, labels, and descriptive language. Be extremely precise with numbers, units, and data references. Return only the extracted text without any additional commentary or corrections.";
  } else if (taskType === 'Task 2' || taskType === 'writing_task2') {
    systemPrompt += "This is an IELTS Writing Task 2, which is an essay on a given topic. Focus on preserving the exact essay structure, arguments, and transitions as written by the student. Do not correct grammar or spelling errors. Return only the extracted text without any additional commentary or improvements.";
  } else if (taskType === 'listening') {
    systemPrompt += "This is an IELTS Listening answer sheet. Focus on extracting the answers which are typically short responses, often multi-word phrases (like 'amber beads', 'small green car', etc.) or single words. Pay special attention to spelling, as it's critical for scoring. Keep multi-word answers together rather than splitting them into individual words. Answers may be numbered or in a table format. Return only the extracted text without any additional commentary.";
  } else if (taskType === 'reading') {
    systemPrompt += "This is an IELTS Reading answer sheet. Focus on extracting the answers which may be short responses, multi-word phrases (like 'amber beads', 'small green car', etc.), single words, or letters (for multiple choice). Pay special attention to spelling and letter case, as it's critical for scoring. Keep multi-word answers together rather than splitting them into individual words. Answers may be numbered or in a table format. Return only the extracted text without any additional commentary.";
  } else {
    systemPrompt += "This is an IELTS answer sheet. Focus on accurately transcribing all written content. Return only the extracted text without any additional commentary.";
  }

  systemPrompt += " If parts of the text are unclear or illegible, indicate this with [illegible] rather than guessing. Do not attempt to improve or correct the writing in any way."

  // Make request to Claude
  const message = await anthropic.messages.create({
    model: "claude-3-5-sonnet-20241022",
    max_tokens: 4000,
    temperature: 0.0,
    system: systemPrompt,
    messages: [
      {
        role: "user",
        content: [
          {
            type: "image",
            source: {
              type: "base64",
              media_type: mediaType,
              data: base64Image
            }
          },
          {
            type: "text",
            text: `Extract the handwritten text from this image of an IELTS ${taskType} answer sheet. ${parseInt(totalPages) > 1 ? `This is page ${pageNumber} of ${totalPages} of the same document.` : ''} This is handwritten on grid/lined paper. Transcribe exactly what is written without making corrections or improvements. Preserve paragraph breaks and formatting. ${parseInt(totalPages) > 1 ? 'If text appears to be cut off at the beginning or end of the page, indicate this with [...] at that point.' : ''} ${(taskType === 'listening' || taskType === 'reading') ? 'IMPORTANT: Keep multi-word answers together (like "amber beads", "small green car") rather than splitting them into individual words.' : ''} Return only the extracted text without any commentary.`
          }
        ]
      }
    ]
  });

  // Extract the text from the response
  const extractedText = message.content[0].type === 'text'
    ? message.content[0].text
    : 'No text could be extracted';

  return extractedText;
}

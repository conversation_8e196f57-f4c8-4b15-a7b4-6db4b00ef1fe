{"name": "material-kit-react", "version": "4.0.0", "author": "<PERSON><PERSON>", "licence": "MIT", "homepage": "https://devias.io", "private": false, "scripts": {"dev": "next dev", "build": "node prisma-setup.js && next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit", "format:write": "prettier --write \"**/*.{js,jsx,mjs,ts,tsx,mdx}\" --cache", "format:check": "prettier --check \"**/*.{js,jsx,mjs,ts,tsx,mdx}\" --cache", "db:clear": "node scripts/clear-database.js", "db:verify": "node scripts/verify-schema.js", "db:reset": "npx prisma db push --force-reset && node scripts/verify-schema.js", "db:sync": "npx prisma db push", "render-build": "pnpm install --no-frozen-lockfile && pnpm run build"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@emotion/cache": "11.11.0", "@emotion/react": "11.11.4", "@emotion/server": "11.11.0", "@emotion/styled": "11.11.5", "@fontsource/inter": "5.0.18", "@fontsource/plus-jakarta-sans": "5.0.20", "@fontsource/roboto-mono": "5.0.18", "@hookform/resolvers": "3.6.0", "@mui/icons-material": "5.15.20", "@mui/lab": "5.0.0-alpha.170", "@mui/material": "5.15.20", "@mui/system": "5.15.20", "@mui/utils": "5.15.20", "@mui/x-date-pickers": "7.7.1", "@phosphor-icons/react": "2.1.6", "@prisma/client": "^6.6.0", "@types/papaparse": "^5.3.15", "@types/uuid": "^10.0.0", "apexcharts": "3.49.2", "dayjs": "1.11.11", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jszip": "^3.10.1", "next": "14.2.4", "openai": "^4.87.3", "papaparse": "^5.5.2", "papaya": "^3.0.0", "pdfjs-dist": "^5.0.375", "react": "18.3.1", "react-apexcharts": "1.4.1", "react-dom": "18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "7.52.0", "react-pdf": "^9.2.1", "tesseract.js": "^6.0.0", "uuid": "^11.1.0", "zod": "3.23.8"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.2.1", "@testing-library/jest-dom": "6.4.6", "@testing-library/react": "16.0.0", "@types/jest": "29.5.12", "@types/mapbox-gl": "3.1.0", "@types/node": "20.14.9", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@types/react-syntax-highlighter": "15.5.13", "@vercel/style-guide": "6.0.0", "eslint": "8.57.0", "eslint-config-next": "14.2.4", "eslint-config-prettier": "9.1.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "prettier": "3.3.2", "prisma": "^6.6.0", "ts-node": "^10.9.2", "typescript": "5.5.2"}}
services:
  - type: web
    name: testhub
    env: node
    buildCommand: pnpm install --no-frozen-lockfile && pnpm run build
    startCommand: pnpm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: ANTHROPIC_API_KEY
        sync: false
      - key: OPENAI_API_KEY
        sync: false
      - key: NEXT_PUBLIC_SITE_URL
        fromService:
          name: testhub
          type: web
          property: url
      - key: NEXT_PUBLIC_LOG_LEVEL
        value: ERROR

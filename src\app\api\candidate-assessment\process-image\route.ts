import { NextRequest, NextResponse } from 'next/server';
import { extractTextFromImage } from '@/utils/claude-image-processor';
import { prisma } from '@/lib/db';

// Helper function to parse extracted text into answers
function parseAnswersFromText(text: string): string {
  console.log('Parsing answers from text:', text);

  // First, split by common separators (newlines, spaces)
  const lines = text.split(/[\n\r]+/);

  // Process each line to extract answers
  const answers: string[] = [];

  // Try different parsing strategies

  // Strategy 1: Look for numbered answers with letters or words (e.g., "19. D", "20 A", "21) amber beads")
  for (const line of lines) {
    // Skip empty lines
    if (!line.trim()) continue;

    // First, try to find numbered answers with multiple words (e.g., "19. amber beads")
    const numberedMultiWordMatch = line.match(/(\d+)[\s.)]+(.+)/);
    if (numberedMultiWordMatch) {
      const [, number, answer] = numberedMultiWordMatch;
      const questionNumber = parseInt(number, 10);

      // Make sure we have a valid question number and answer
      if (!isNaN(questionNumber) && answer) {
        // Ensure we have enough slots in the array
        while (answers.length < questionNumber) {
          answers.push('');
        }

        // Set the answer at the correct position (adjusting for 0-based index)
        answers[questionNumber - 1] = answer.trim();
        console.log(`Found multi-word answer for question ${questionNumber}: ${answer.trim()}`);
        continue; // Skip to next line after finding a multi-word answer
      }
    }

    // If no multi-word match, look for patterns like "19. D" or "20 A" or "21) B"
    const numberLetterMatch = line.match(/(\d+)[\s.)]+([\w]+)/);
    if (numberLetterMatch) {
      const [, number, answer] = numberLetterMatch;
      const questionNumber = parseInt(number, 10);

      // Make sure we have a valid question number and answer
      if (!isNaN(questionNumber) && answer) {
        // Ensure we have enough slots in the array
        while (answers.length < questionNumber) {
          answers.push('');
        }

        // Set the answer at the correct position (adjusting for 0-based index)
        answers[questionNumber - 1] = answer.trim();
        console.log(`Found answer for question ${questionNumber}: ${answer.trim()}`);
      }
    }
  }

  // Strategy 2: Look for standalone letters that might be answers
  if (answers.length === 0) {
    console.log('Trying alternative parsing strategy...');
    for (const line of lines) {
      // Skip empty lines
      if (!line.trim()) continue;

      // Look for standalone letters or words that might be answers
      // First try to find numbered answers with multiple words (e.g., "19. small green car")
      const numberedMultiWordMatch = line.match(/(\d+)[\s.)]+(.+)/);
      if (numberedMultiWordMatch) {
        const [, number, answer] = numberedMultiWordMatch;
        const questionNumber = parseInt(number, 10);

        // Make sure we have a valid question number and answer
        if (!isNaN(questionNumber) && answer) {
          // Check if the answer has up to 4 words
          const words = answer.trim().split(/\s+/);
          if (words.length <= 4) {
            // Ensure we have enough slots in the array
            while (answers.length < questionNumber) {
              answers.push('');
            }

            // Set the answer at the correct position (adjusting for 0-based index)
            answers[questionNumber - 1] = answer.trim();
            console.log(`Found multi-word answer for question ${questionNumber}: ${answer.trim()}`);
            continue; // Skip to next line after finding a multi-word answer
          }
        }
      }

      // If no numbered multi-word answer found, look for standalone words and phrases

      // First, try to find two-word phrases that are likely to be answers
      const twoWordPhrases = line.match(/\b([A-Za-z]+\s+[A-Za-z]+)\b/g);
      if (twoWordPhrases) {
        for (const phrase of twoWordPhrases) {
          answers.push(phrase.trim());
          console.log(`Found two-word phrase: ${phrase.trim()}`);
        }
      }

      // Then look for other possible answers (single words or longer phrases)
      const possibleAnswers = line.match(/\b([A-Za-z]+(?:\s+[A-Za-z]+){0,3})\b/g);
      if (possibleAnswers) {
        for (const answer of possibleAnswers) {
          // Allow up to 4 words per answer
          const wordCount = answer.trim().split(/\s+/).length;
          // Skip single words if we already have multi-word answers
          if (wordCount > 1 && wordCount <= 4) {
            answers.push(answer.trim());
            console.log(`Found multi-word answer: ${answer.trim()}`);
          }
          // Only add single words if we don't have multi-word answers yet
          else if (wordCount === 1 && twoWordPhrases === null) {
            answers.push(answer.trim());
            console.log(`Found single-word answer: ${answer.trim()}`);
          }
        }
      }
    }
  }

  // Join answers with commas
  const result = answers.join(', ');
  console.log('Parsed answers:', result);
  return result;
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('image') as File;
    const componentType = formData.get('componentType') as string || 'unknown';
    const entryId = formData.get('entryId') as string;
    const studentId = formData.get('studentId') as string;

    if (!file) {
      return NextResponse.json({ error: 'No image file provided' }, { status: 400 });
    }

    // Allow temporary entry IDs for initial processing during upload
    if (!entryId) {
      return NextResponse.json({ error: 'Entry ID is required' }, { status: 400 });
    }

    // If this is a temporary processing request (during upload), skip database updates
    const isTemporaryProcessing = entryId === 'temp';

    // Check if file is an image
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({ error: 'File must be an image' }, { status: 400 });
    }

    console.log(`Processing ${componentType} image for student ${studentId}, entry ${entryId}`);

    // Use the utility function to extract text from the image
    const extractedText = await extractTextFromImage(file, componentType);

    console.log(`Text extracted successfully, length: ${extractedText.length} characters`);

    // Parse answers from the extracted text for reading and listening components
    let parsedAnswers = '';
    if (componentType === 'reading' || componentType === 'listening') {
      parsedAnswers = parseAnswersFromText(extractedText);
      console.log(`Parsed answers: ${parsedAnswers}`);
    }

    // Update the database entry with the extracted text (skip if temporary processing)
    let result;

    if (!isTemporaryProcessing) {
      switch (componentType) {
        case 'writing_task1':
        case 'writing_task2':
          // Get the existing entry first to check if we already have text
          const existingEntry = await prisma.writingEntry.findUnique({
            where: { id: entryId }
          });

          // If we already have text, append the new text
          let updatedText = extractedText;
          if (existingEntry?.essayText && existingEntry.essayText !== 'Pending AI analysis') {
            updatedText = `${existingEntry.essayText}\n\n${extractedText}`;
          }

          // Update writing entry
          result = await prisma.writingEntry.update({
            where: { id: entryId },
            data: {
              essayText: updatedText,
              extractedWithClaude: true
            }
          });
          break;

        case 'listening':
          // Update listening entry
          result = await prisma.listeningEntry.update({
            where: { id: entryId },
            data: {
              extractedText: extractedText,
              extractedWithClaude: true,
              answers: parsedAnswers.split(',').map(a => a.trim())
            }
          });
          break;

        case 'reading':
          // Update reading entry
          result = await prisma.readingEntry.update({
            where: { id: entryId },
            data: {
              extractedText: extractedText,
              extractedWithClaude: true,
              answers: parsedAnswers.split(',').map(a => a.trim())
            }
          });
          break;

        default:
          return NextResponse.json({ error: 'Invalid component type' }, { status: 400 });
      }
    }

    return NextResponse.json({
      success: true,
      text: extractedText,
      parsedAnswers: parsedAnswers,
      componentType: componentType,
      entryId: entryId
    });

  } catch (error) {
    console.error('Error processing image:', error);

    // Special handling for auth errors
    if (error instanceof Error && error.message.includes('401')) {
      return NextResponse.json(
        { error: 'API key is invalid or expired', details: error.message },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to process image', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

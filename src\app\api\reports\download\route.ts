import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { generateCandidateReport } from '@/utils/pdf-generator';

// GET /api/reports/download?id=reportId
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const reportId = url.searchParams.get('id');
    const studentId = url.searchParams.get('studentId');

    if (!reportId && !studentId) {
      return NextResponse.json(
        { error: 'Report ID or Student ID is required' },
        { status: 400 }
      );
    }

    // Get the report
    let report;
    if (reportId) {
      report = await prisma.report.findUnique({
        where: { id: reportId },
        include: {
          student: true,
        },
      });
    } else if (studentId) {
      // Get the latest report for the student
      report = await prisma.report.findFirst({
        where: { studentId },
        orderBy: { createdAt: 'desc' },
        include: {
          student: true,
        },
      });
    }

    if (!report) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    // Get all the components for this student
    const listeningEntry = await prisma.listeningEntry.findFirst({
      where: { studentId: report.studentId },
      orderBy: { createdAt: 'desc' },
    });

    const readingEntry = await prisma.readingEntry.findFirst({
      where: { studentId: report.studentId },
      orderBy: { createdAt: 'desc' },
    });

    const writingTask1Entry = await prisma.writingEntry.findFirst({
      where: {
        studentId: report.studentId,
        taskType: 'task1'
      },
      orderBy: { createdAt: 'desc' },
    });

    const writingTask2Entry = await prisma.writingEntry.findFirst({
      where: {
        studentId: report.studentId,
        taskType: 'task2'
      },
      orderBy: { createdAt: 'desc' },
    });

    const speakingEntry = await prisma.speakingEntry.findFirst({
      where: { studentId: report.studentId },
      orderBy: { createdAt: 'desc' },
    });

    // Use type assertions for entries to handle missing fields
    const listeningEntryAny = listeningEntry as any;
    const readingEntryAny = readingEntry as any;
    const writingTask1EntryAny = writingTask1Entry as any;
    const writingTask2EntryAny = writingTask2Entry as any;
    const speakingEntryAny = speakingEntry as any;

    // Create a candidate object for the PDF generator
    const candidate = {
      id: report.id,
      studentId: report.studentId,
      name: report.candidateName || report.studentId,
      components: [
        {
          id: listeningEntry?.id || 'listening',
          name: 'Listening',
          status: listeningEntry ? 'processed' : 'pending',
          type: 'listening',
          entryId: listeningEntry?.id || null,
          band: listeningEntry?.band || null,
          criteriaScores: null, // criteriaScores field doesn't exist in the database yet
          strengths: listeningEntry?.strengths || null,
          weaknesses: listeningEntry?.weaknesses || null,
          improvementSuggestions: listeningEntry?.improvementSuggestions || null,
        },
        {
          id: readingEntry?.id || 'reading',
          name: 'Reading',
          status: readingEntry ? 'processed' : 'pending',
          type: 'reading',
          entryId: readingEntry?.id || null,
          band: readingEntry?.band || null,
          criteriaScores: null, // criteriaScores field doesn't exist in the database yet
          strengths: readingEntry?.strengths || null,
          weaknesses: readingEntry?.weaknesses || null,
          improvementSuggestions: readingEntry?.improvementSuggestions || null,
        },
        {
          id: writingTask1Entry?.id || 'writing_task1',
          name: 'Writing Task 1',
          status: writingTask1Entry ? 'processed' : 'pending',
          type: 'writing_task1',
          entryId: writingTask1Entry?.id || null,
          band: writingTask1Entry?.band || null,
          criteriaScores: writingTask1EntryAny?.criteriaScores || null,
          strengths: writingTask1Entry?.strengths || null,
          weaknesses: writingTask1Entry?.weaknesses || null,
          improvementSuggestions: writingTask1Entry?.improvementSuggestions || null,
        },
        {
          id: writingTask2Entry?.id || 'writing_task2',
          name: 'Writing Task 2',
          status: writingTask2Entry ? 'processed' : 'pending',
          type: 'writing_task2',
          entryId: writingTask2Entry?.id || null,
          band: writingTask2Entry?.band || null,
          criteriaScores: writingTask2EntryAny?.criteriaScores || null,
          strengths: writingTask2Entry?.strengths || null,
          weaknesses: writingTask2Entry?.weaknesses || null,
          improvementSuggestions: writingTask2Entry?.improvementSuggestions || null,
        },
        {
          id: speakingEntry?.id || 'speaking',
          name: 'Speaking',
          status: speakingEntry ? 'processed' : 'pending',
          type: 'speaking',
          entryId: speakingEntry?.id || null,
          band: speakingEntry?.band || null,
          criteriaScores: speakingEntryAny?.criteriaScores || null,
          strengths: speakingEntry?.strengths || null,
          weaknesses: speakingEntry?.weaknesses || null,
          improvementSuggestions: speakingEntry?.improvementSuggestions || null,
        }
      ],
      reportGenerated: true,
      reportId: report.id,
      overallBand: report.overallBand,
    };

    // Extract band scores from database entries if available
    candidate.components.forEach(comp => {
      if (comp.type === 'listening' && listeningEntry?.band) {
        comp.band = listeningEntry.band;
      } else if (comp.type === 'reading' && readingEntry?.band) {
        comp.band = readingEntry.band;
      } else if (comp.type === 'writing_task1' && writingTask1Entry?.band) {
        comp.band = writingTask1Entry.band;
      } else if (comp.type === 'writing_task2' && writingTask2Entry?.band) {
        comp.band = writingTask2Entry.band;
      } else if (comp.type === 'speaking' && speakingEntry?.band) {
        comp.band = speakingEntry.band;
      }
    });

    // Set overall band score if available
    if (report.overallBand) {
      candidate.overallBand = report.overallBand;
    }

    // Generate the PDF
    const pdfResult = generateCandidateReport(candidate as any);

    // Extract the base64 data from the data URI
    const base64Data = pdfResult.pdfBase64.split(',')[1];
    const binaryData = Buffer.from(base64Data, 'base64');

    // Return the PDF as a downloadable file
    return new NextResponse(binaryData, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${pdfResult.fileName}"`,
      },
    });
  } catch (error) {
    console.error('Error generating PDF report:', error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    const errorStack = error instanceof Error ? error.stack : undefined;

    // Log detailed error information
    console.error('PDF generation error details:', {
      message: errorMessage,
      stack: errorStack,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json(
      {
        error: 'An unexpected error occurred while generating the PDF report',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

'use client';

import { useState, ChangeEvent } from 'react';
import {
  Box,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Grid,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  Stack,
  Tooltip,
  Alert,
  Dialog,
  DialogContent,
  DialogActions,
  DialogTitle,
  Chip,
  Tabs,
  Tab,
} from '@mui/material';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import {
  Plus as PlusIcon,
  Trash as TrashIcon,
  File as FileIcon,
  ArrowsClockwise as RefreshIcon,
  FileArrowDown as DownloadIcon,
  FileAudio,
  Play,
  Pause,
  FilePdf,
} from '@phosphor-icons/react';
import Papa from 'papaparse';
import { jsPDF } from 'jspdf';
import autoTable, { RowInput } from 'jspdf-autotable'; // Import RowInput
// @ts-ignore - jsPDF has some TS issues but works fine
import 'jspdf-autotable';

// Define colors for PDF styling
const pdfColors = {
  primary: [25, 118, 210],    // Material UI Blue 500
  success: [46, 125, 50],     // Material UI Green 700
  error: [211, 47, 47],       // Material UI Red 600
  warning: [237, 108, 2],     // Material UI Orange 700
  textPrimary: [33, 33, 33],  // Almost black
  textSecondary: [97, 97, 97], // Dark Gray
  backgroundLight: [245, 245, 245], // Light Gray
  white: [255, 255, 255],
  headerBlue: [100, 181, 246] // Light blue similar to reference
};

// Model selection removed, backend uses gpt-4o-mini
// type ModelType = 'gpt-4o' | 'gpt-4-turbo'; // Removed type

interface SpeakingEntry {
  id: string;
  transcript: string; // Restored missing property
  examinerContent?: string;
  taskType?: string;
  partNumber?: number;
  audioURL?: string;
  result?: any;
  isProcessing?: boolean;
  error?: string;
}

export default function BulkDetailedSpeakingCheckerPage() {
  const [entries, setEntries] = useState<SpeakingEntry[]>([
    {
      id: `entry-${Date.now()}`,
      transcript: '',
      examinerContent: '',
      taskType: 'mock_test',
      partNumber: 0
    }
  ]);
  // const [selectedModel, setSelectedModel] = useState<ModelType>('gpt-4o'); // Removed state
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);
  const [playingId, setPlayingId] = useState<string | null>(null);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [currentDetailEntry, setCurrentDetailEntry] = useState<SpeakingEntry | null>(null);
  const [detailTabValue, setDetailTabValue] = useState(0);
  const [exportMenuAnchorEl, setExportMenuAnchorEl] = useState<null | HTMLElement>(null);

  const handleDetailTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setDetailTabValue(newValue);
  };

  // Removed handleModelChange function as model selection is removed
  // const handleModelChange = ( ... ) => { ... };

  const handleAddEntry = () => {
    setEntries([
      ...entries,
      {
        id: `entry-${Date.now()}`,
        transcript: '',
        examinerContent: '',
        taskType: 'mock_test',
        partNumber: 0
      }
    ]);
  };

  const handleRemoveEntry = (id: string) => {
    if (entries.length > 1) {
      setEntries(entries.filter(entry => entry.id !== id));
    }
  };

  const handleInputChange = (id: string, field: keyof SpeakingEntry, value: string | number) => {
    setEntries(
      entries.map(entry =>
        entry.id === id ? { ...entry, [field]: value } : entry
      )
    );
  };

  // Helper function to check if file format is supported by OpenAI Whisper API
  const isValidAudioFormat = (file: File): boolean => {
    // Supported formats by OpenAI Whisper API
    const supportedFormats = ['flac', 'mp3', 'mp4', 'mpeg', 'mpga', 'ogg', 'wav', 'webm'];

    // Extract extension from filename
    const extension = file.name.split('.').pop()?.toLowerCase();
    if (!extension) return false;

    return supportedFormats.includes(extension);
  };

  const handleAudioUpload = async (id: string, event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file format before uploading
      if (!isValidAudioFormat(file)) {
        setError(`Invalid file format: ${file.name}. Supported formats: flac, mp3, mp4, mpeg, mpga, ogg, wav, webm`);
        return;
      }

      const url = URL.createObjectURL(file);

      // Update the entry to show it's processing
      setEntries(
        entries.map(entry =>
          entry.id === id ? {
            ...entry,
            audioURL: url,
            isProcessing: true,
            error: undefined // Clear any previous errors
          } : entry
        )
      );

      try {
        // Create form data for the API request
        const formData = new FormData();
        formData.append('audio', file);
        formData.append('model', 'whisper-1');

        // Call our audio transcription API
        const response = await fetch('/api/audio-transcription', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to transcribe audio');
        }

        const data = await response.json();

        // Extract candidate content from the transcript
        const transcriptLines = data.transcript.split('\n');
        let candidateContent = '';
        let examinerContent = '';

        // Process line by line
        for (let i = 0; i < transcriptLines.length; i++) {
          const line = transcriptLines[i];
          if (line.startsWith('Examiner:')) {
            examinerContent += line.replace('Examiner:', '').trim() + '\n';
          } else if (line.startsWith('Candidate:')) {
            candidateContent += line.replace('Candidate:', '').trim() + '\n';
          }
        }

        // Clear any global error messages
        setError(null);

        // Update the entry with the transcript and processed data
        setEntries(
          entries.map(entry =>
            entry.id === id ? {
              ...entry,
              audioURL: url,
              transcript: candidateContent.trim() || data.transcript,
              examinerContent: examinerContent.trim(),
              isProcessing: false,
              error: undefined // Clear any errors
            } : entry
          )
        );
      } catch (error) {
        console.error('Error transcribing audio:', error);

        const errorMessage = error instanceof Error ? error.message : 'Failed to transcribe audio';

        // Check if it's a file format error
        if (errorMessage.includes('format') || errorMessage.includes('Unrecognized')) {
          setError(`Invalid file format: ${file.name}. Supported formats: flac, mp3, mp4, mpeg, mpga, ogg, wav, webm`);
        }

        // Update the entry to show it's no longer processing and set a placeholder
        setEntries(
          entries.map(entry =>
            entry.id === id ? {
              ...entry,
              audioURL: url,
              transcript: entry.transcript || "Transcription failed. Please enter the transcript manually.",
              isProcessing: false,
              error: errorMessage
            } : entry
          )
        );
      }
    }
  };

  const handlePlayPause = (id: string, url: string) => {
    if (playingId === id && currentAudio) {
      // Pause the current audio
      currentAudio.pause();
      setPlayingId(null);
    } else {
      // Stop any currently playing audio
      if (currentAudio) {
        currentAudio.pause();
      }

      // Play the new audio
      const audio = new Audio(url);
      audio.play();
      audio.onended = () => {
        setPlayingId(null);
      };
      setCurrentAudio(audio);
      setPlayingId(id);
    }
  };

  const handleCheck = async () => {
    // Validate entries
    const emptyEntries = entries.filter(entry => !entry.transcript.trim());
    if (emptyEntries.length > 0) {
      setError('All entries must have a transcript');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Mark all entries as processing
      setEntries(entries.map(entry => ({ ...entry, isProcessing: true, error: undefined, result: undefined })));

      const response = await fetch('/api/bulk-detailed-speaking-checker', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          entries: entries.map(({ transcript, examinerContent, taskType, partNumber }) => ({
            transcript,
            examinerContent,
            taskType,
            partNumber
          }))
          // model: selectedModel // Removed model from payload
        })
      });

      if (!response.ok) {
        let errorMsg = `Failed to process bulk speaking check (Status: ${response.status})`; // Default message with status
        const contentType = response.headers.get('content-type');
        try {
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json(); // Try parsing JSON
            errorMsg = errorData.error || JSON.stringify(errorData); // Use error field or stringify
          } else {
            const textError = await response.text(); // Read as text if not JSON
             // Use the first 100 chars to avoid overly long messages
            errorMsg = textError.length > 100 ? textError.substring(0, 100) + '...' : textError;
          }
        } catch (parseError) {
          console.error("Failed to parse error response:", parseError);
          // Attempt to read as text if JSON parsing failed
          try {
              const textError = await response.text();
              errorMsg = textError.length > 100 ? textError.substring(0, 100) + '...' : textError;
          } catch (textError) {
              console.error("Failed to read error response as text:", textError);
              // Stick with the default message if all else fails
          }
        }
        throw new Error(errorMsg); // Throw the extracted/default error message
      }

      const data = await response.json();

      // Update entries with results
      setEntries(entries.map((entry, index) => {
        const result = data.results[index];

        if (result.error) {
          return {
            ...entry,
            isProcessing: false,
            error: result.error,
            result: undefined
          };
        }

        try {
          return {
            ...entry,
            isProcessing: false,
            result: result,
            error: undefined
          };
        } catch (e) {
          console.error('Error parsing result:', e);
          return {
            ...entry,
            isProcessing: false,
            error: 'Failed to parse feedback from API response',
            result: undefined
          };
        }
      }));
    } catch (err) {
      console.error('Error in bulk check:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');

      // Mark all entries as not processing
      setEntries(entries.map(entry => ({ ...entry, isProcessing: false })));
    } finally {
      setIsProcessing(false);
    }
  };

  const handleImportCSV = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      Papa.parse(file, {
        header: true,
        complete: (results) => {
          if (results.data && Array.isArray(results.data) && results.data.length > 0) {
            const importedEntries = results.data
              .filter((row: any) => row.transcript) // Only import rows with transcript
              .map((row: any) => ({
                id: `entry-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                transcript: row.transcript || '',
                examinerContent: row.examinerContent || '',
                taskType: row.taskType || 'mock_test',
                partNumber: row.partNumber ? parseInt(row.partNumber) : 0
              }));

            if (importedEntries.length > 0) {
              setEntries(importedEntries);
            } else {
              setError('No valid entries found in CSV');
            }
          } else {
            setError('Invalid CSV format');
          }
        },
        error: (error) => {
          console.error('CSV parse error:', error);
          setError('Failed to parse CSV file');
        }
      });
    }
  };

  const handleExportCSV = () => {
    const exportData = entries.map(entry => ({
      transcript: entry.transcript,
      examinerContent: entry.examinerContent || '',
      taskType: entry.taskType || 'mock_test',
      partNumber: entry.partNumber || 0,
      overallScore: entry.result?.overallScore || '',
      fluencyScore: entry.result?.criteria?.find((c: any) => c.name === 'Fluency and Coherence')?.score || '',
      lexicalScore: entry.result?.criteria?.find((c: any) => c.name === 'Lexical Resource')?.score || '',
      grammarScore: entry.result?.criteria?.find((c: any) => c.name === 'Grammatical Range and Accuracy')?.score || '',
      pronunciationScore: entry.result?.criteria?.find((c: any) => c.name === 'Pronunciation')?.score || '',
    }));

    const csv = Papa.unparse(exportData);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `detailed-speaking-checker-results-${new Date().toISOString().slice(0, 10)}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleShowDetails = (entry: SpeakingEntry) => {
    setCurrentDetailEntry(entry);
    setDetailDialogOpen(true);
    setDetailTabValue(0);
  };

  const handleCloseDetails = () => {
    setDetailDialogOpen(false);
    setCurrentDetailEntry(null);
  };

  const handleOpenExportMenu = (event: React.MouseEvent<HTMLElement>) => {
    setExportMenuAnchorEl(event.currentTarget);
  };

  const handleCloseExportMenu = () => {
    setExportMenuAnchorEl(null);
  };


    // Define the actual PDF export logic with detailed analysis
    const exportAsPdf = (entryId?: string) => {
      const entriesToExport = entryId
        ? entries.filter(entry => entry.id === entryId && entry.result)
        : entries.filter(entry => entry.result);

      if (entriesToExport.length === 0) {
        setError("No results available to export for the selected entry/entries.");
        handleCloseExportMenu();
        return;
      }

      const doc = new jsPDF();
      const pageHeight = doc.internal.pageSize.height;
      const pageWidth = doc.internal.pageSize.width;
      const margin = 15;
      let currentY = margin; // Start Y position

       // Helper function to check for page overflow and add new page
       const checkPageBreak = (yPos: number, requiredHeight: number = 20): number => {
        if (yPos + requiredHeight > pageHeight - margin) {
          doc.addPage();
          return margin; // Reset Y to top margin
        }
        return yPos;
      };

      // --- Helper function to calculate list height ---
      const calculateListHeight = (items: string[]) => {
        let listHeight = 0;
        if (items && items.length > 0) {
          listHeight += 5; // Title space
          items.forEach(item => {
            const itemLines = doc.splitTextToSize(`• ${item}`, pageWidth - (margin * 4) - 10);
            listHeight += itemLines.length * 4; // Item space
          });
          listHeight += 4; // Spacing after list
        }
        return listHeight;
      };

      entriesToExport.forEach((entry, index) => {
        if (index > 0) {
          doc.addPage();
          currentY = margin;
        }


              // --- Header for the Entry ---
              doc.setFillColor(pdfColors.headerBlue[0], pdfColors.headerBlue[1], pdfColors.headerBlue[2]); // Pass R, G, B individually
              doc.rect(0, currentY - margin, pageWidth, 20, 'F'); // Background rectangle
              doc.setFontSize(16);
              doc.setFont('helvetica', 'bold');
              doc.setTextColor(pdfColors.white[0], pdfColors.white[1], pdfColors.white[2]); // Pass R, G, B individually
              doc.text(`Detailed Speaking Assessment - Entry ${index + 1}`, pageWidth / 2, currentY + 2, { align: 'center' });
              currentY += 15; // Adjust Y after header

              // --- Overall Score ---
              currentY = checkPageBreak(currentY, 15);
              doc.setFontSize(12);
              doc.setFont('helvetica', 'bold');
              doc.setTextColor(pdfColors.textPrimary[0], pdfColors.textPrimary[1], pdfColors.textPrimary[2]); // Pass R, G, B individually
              doc.text(`Overall Score:`, margin, currentY);

              // Score Chip
              const score = entry.result.overallScore;
              let scoreColor = pdfColors.primary; // Default
              if (score >= 7) scoreColor = pdfColors.success;
              else if (score < 5) scoreColor = pdfColors.error;
              else if (score < 7) scoreColor = pdfColors.warning; // Use warning for mid-range

              const scoreText = score.toFixed(1);
              const scoreTextWidth = doc.getTextWidth(scoreText);
              const scoreChipWidth = scoreTextWidth + 10; // Add padding
              const scoreChipX = pageWidth - margin - scoreChipWidth; // Position on the right
              doc.setFillColor(scoreColor[0], scoreColor[1], scoreColor[2]); // Pass R, G, B individually
              doc.roundedRect(scoreChipX, currentY - 8, scoreChipWidth, 10, 3, 3, 'F'); // Draw rounded rect
              doc.setTextColor(pdfColors.white[0], pdfColors.white[1], pdfColors.white[2]); // Pass R, G, B individually
              doc.setFontSize(10);
              doc.text(scoreText, scoreChipX + scoreChipWidth / 2, currentY - 1.5, { align: 'center' });
        currentY += 10; // Adjust Y after score line

        // --- Criteria Scores ---
        currentY = checkPageBreak(currentY, 40);
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.text('Criteria Scores', margin, currentY);
        currentY += 8;

        entry.result.criteria.forEach((criterion: any) => {
          let startYCriterion = currentY; // Remember Y before drawing card content
          let contentY = startYCriterion; // Y position for content inside the card

          contentY = checkPageBreak(contentY, 30); // Check space before starting card content
          if (contentY === margin) { // If page break happened, reset startYCriterion
            startYCriterion = margin;
          }

          // --- Calculate content height first ---
          let tempY = contentY; // Use a temporary Y to calculate height without drawing
          tempY += 8; // Space for title/score line
          const feedbackLines = doc.splitTextToSize(criterion.feedback || 'N/A', pageWidth - (margin * 3) - 10);
          tempY += feedbackLines.length * 4 + 6; // Space for feedback

          // Calculate height needed for the table
          const strengths = criterion.strengths || [];
          const weaknesses = criterion.weaknesses || [];
          const improvements = criterion.improvements || [];
          const maxRows = Math.max(strengths.length, weaknesses.length, improvements.length);
          if (maxRows > 0) {
              // Estimate table height (header + rows + padding) - this is approximate
              tempY += 10 + (maxRows * 8);
          }

          const cardHeight = tempY - contentY + 5; // Calculate height + padding

          // --- Draw background rectangle ---
          doc.setFillColor(pdfColors.backgroundLight[0], pdfColors.backgroundLight[1], pdfColors.backgroundLight[2]);
          doc.roundedRect(margin, startYCriterion - 5, pageWidth - (margin * 2), cardHeight, 3, 3, 'F');

          // --- Draw content on top of background ---
          // Criterion Title
          doc.setFontSize(11);
          doc.setFont('helvetica', 'bold');
          doc.setTextColor(pdfColors.textPrimary[0], pdfColors.textPrimary[1], pdfColors.textPrimary[2]);
          doc.text(`${criterion.name}:`, margin + 5, contentY); // Title on left

          // Score Chip for Criterion
          const critScore = criterion.score;
          let critScoreColor = pdfColors.primary;
          if (critScore >= 7) critScoreColor = pdfColors.success;
          else if (critScore < 5) critScoreColor = pdfColors.error;
          else if (critScore < 7) critScoreColor = pdfColors.warning;

          const critScoreText = critScore.toFixed(1);
          const critScoreTextWidth = doc.getTextWidth(critScoreText);
          const critScoreChipWidth = critScoreTextWidth + 10;
          const critScoreChipX = pageWidth - margin - critScoreChipWidth - 5; // Position right, inside card
          doc.setFillColor(critScoreColor[0], critScoreColor[1], critScoreColor[2]);
          doc.roundedRect(critScoreChipX, contentY - 8, critScoreChipWidth, 10, 3, 3, 'F');
          doc.setTextColor(pdfColors.white[0], pdfColors.white[1], pdfColors.white[2]);
          doc.setFontSize(10);
          doc.text(critScoreText, critScoreChipX + critScoreChipWidth / 2, contentY - 1.5, { align: 'center' });

          contentY += 8; // Space after title/score line

          // Feedback Text
          doc.setFontSize(10);
          doc.setFont('helvetica', 'normal');
          doc.setTextColor(pdfColors.textSecondary[0], pdfColors.textSecondary[1], pdfColors.textSecondary[2]); // Use secondary text color
          contentY = checkPageBreak(contentY, feedbackLines.length * 4); // Check page break before drawing text
          doc.text(feedbackLines, margin + 5, contentY);
          contentY += feedbackLines.length * 4 + 6; // Add spacing

          // --- Draw Strengths/Weaknesses/Improvements Table ---
          const tableBody = [];
          for (let i = 0; i < maxRows; i++) {
            tableBody.push([
              strengths[i] ? `• ${strengths[i]}` : '',
              weaknesses[i] ? `• ${weaknesses[i]}` : '',
              improvements[i] ? `• ${improvements[i]}` : '',
            ]);
          }

          if (maxRows > 0) {
            contentY = checkPageBreak(contentY, 10); // Check space before table
            autoTable(doc, {
              head: [['Strengths', 'Weaknesses', 'Improvements']],
              body: tableBody,
              startY: contentY,
              theme: 'plain', // No borders
              styles: {
                fontSize: 9,
                cellPadding: { top: 1, right: 2, bottom: 1, left: 2 },
                textColor: [pdfColors.textSecondary[0], pdfColors.textSecondary[1], pdfColors.textSecondary[2]], // Pass as tuple
              },
              headStyles: {
                fontSize: 10,
                fontStyle: 'bold',
                textColor: [pdfColors.white[0], pdfColors.white[1], pdfColors.white[2]], // Pass as tuple
                // fillColor is set dynamically below
              },
              columnStyles: {
                0: { cellWidth: (pageWidth - margin * 2 - 10) / 3 }, // Adjust width distribution
                1: { cellWidth: (pageWidth - margin * 2 - 10) / 3 },
                2: { cellWidth: (pageWidth - margin * 2 - 10) / 3 },
              },
              margin: { left: margin + 5, right: margin + 5 }, // Indent table slightly
              didParseCell: (data) => {
                // Style header cells with specific colors
                if (data.section === 'head') {
                  if (data.column.index === 0) data.cell.styles.fillColor = [pdfColors.success[0], pdfColors.success[1], pdfColors.success[2]]; // Pass as tuple
                  if (data.column.index === 1) data.cell.styles.fillColor = [pdfColors.error[0], pdfColors.error[1], pdfColors.error[2]]; // Pass as tuple
                  if (data.column.index === 2) data.cell.styles.fillColor = [pdfColors.primary[0], pdfColors.primary[1], pdfColors.primary[2]]; // Pass as tuple
                }
              },
            });
            contentY = (doc as any).lastAutoTable.finalY + 4; // Update Y after table
          }

          // Update main Y position after drawing everything for this card
          // Now use the final contentY which includes the table height
          currentY = contentY + 5;
        });

          // --- Question Analysis ---
          currentY = checkPageBreak(currentY, 30);
          doc.setFontSize(14);
          doc.setFont('helvetica', 'bold');
          doc.setTextColor(pdfColors.textPrimary[0], pdfColors.textPrimary[1], pdfColors.textPrimary[2]);
          doc.text('Question Analysis', margin, currentY);
          currentY += 10; // Space after section title

          entry.result.questionAnalysis.forEach((qa: any, qIdx: number) => {
            currentY = checkPageBreak(currentY, 30); // Check space before starting table

            // --- Prepare Data for Question Table ---
            const questionText = qa.question || 'N/A';
            const responseText = qa.response || 'N/A';
            const strengths = qa.strengths || [];
            const mistakes = qa.mistakes || []; // Renamed from weaknesses in this context
            const improvements = qa.improvements || [];
            const maxRows = Math.max(strengths.length, mistakes.length, improvements.length);

            const qaTableBody: RowInput[] = []; // Explicitly type the array
            // Add Q & A rows first
            qaTableBody.push([
              { content: `Q${qIdx + 1}:`, styles: { fontStyle: 'bold' as const, halign: 'right' as const, cellWidth: 20 } }, // Use 'right' as const
              { content: questionText, colSpan: 3, styles: { cellWidth: 'auto' as const} } // Use 'auto' as const
            ]);
            qaTableBody.push([
              { content: 'A:', styles: { fontStyle: 'bold' as const, halign: 'right' as const, cellWidth: 20 } }, // Use 'right' as const
              { content: responseText, colSpan: 3, styles: { cellWidth: 'auto' as const} } // Use 'auto' as const
            ]);

            // Add separator/header row for lists
            if (maxRows > 0) {
              qaTableBody.push([
                { content: '', styles: { cellWidth: 20 } }, // Empty cell under Q/A label
                { content: 'Strengths', styles: { fontStyle: 'bold' as const, fillColor: [pdfColors.success[0], pdfColors.success[1], pdfColors.success[2]], textColor: [pdfColors.white[0], pdfColors.white[1], pdfColors.white[2]] } }, // Use 'bold' as const
                { content: 'Mistakes', styles: { fontStyle: 'bold' as const, fillColor: [pdfColors.error[0], pdfColors.error[1], pdfColors.error[2]], textColor: [pdfColors.white[0], pdfColors.white[1], pdfColors.white[2]] } }, // Use 'bold' as const
                { content: 'Improvements', styles: { fontStyle: 'bold' as const, fillColor: [pdfColors.primary[0], pdfColors.primary[1], pdfColors.primary[2]], textColor: [pdfColors.white[0], pdfColors.white[1], pdfColors.white[2]] } } // Use 'bold' as const
              ]);
            }

            // Add list items
            for (let i = 0; i < maxRows; i++) {
              qaTableBody.push([
                { content: '', styles: { cellWidth: 20 } }, // Empty cell under Q/A label
                { content: strengths[i] ? `• ${strengths[i]}` : '' }, // Wrap in { content: ... }
                { content: mistakes[i] ? `• ${mistakes[i]}` : '' }, // Wrap in { content: ... }
                { content: improvements[i] ? `• ${improvements[i]}` : '' }, // Wrap in { content: ... }
              ]);
            }

            // --- Draw Table ---
            autoTable(doc, {
              body: qaTableBody,
              startY: currentY,
              theme: 'grid', // Use grid theme for structure
              styles: {
                fontSize: 9,
                cellPadding: 2,
                textColor: [pdfColors.textSecondary[0], pdfColors.textSecondary[1], pdfColors.textSecondary[2]],
                valign: 'top',
              },
              columnStyles: {
                0: { cellWidth: 20 }, // Fixed width for Q/A label column
                // Remaining columns share space
              },
              margin: { left: margin, right: margin }, // Use full page width margin
              tableWidth: 'auto',
              didParseCell: (data) => {
                // Custom styling if needed, e.g., for Q/A rows
                if (data.row.index === 0 || data.row.index === 1) {
                  // Maybe make Q/A text slightly larger or different color?
                }
                // Header row colors are set directly in body data
              },
            });
            currentY = (doc as any).lastAutoTable.finalY + 10; // Update Y after table + spacing
          });

        // --- Speech Analysis ---
        currentY = checkPageBreak(currentY, 30); // Space for section title
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.setTextColor(pdfColors.textPrimary[0], pdfColors.textPrimary[1], pdfColors.textPrimary[2]);
        doc.text('Speech Analysis', margin, currentY);
        currentY += 10;

        // --- Error Summary Table ---
        currentY = checkPageBreak(currentY, 25); // Space for table
        const summary = entry.result.speechAnalysis;
        autoTable(doc, {
            body: [
              [
                { content: `Total Errors\n${summary.totalErrors}`, styles: { halign: 'center' as const, fontSize: 10 } },
                { content: `Error Frequency\n${summary.errorFrequency}`, styles: { halign: 'center' as const, fontSize: 10 } },
                { content: `Most Frequent Error\n${summary.mostFrequentErrorType}`, styles: { halign: 'center' as const, fontSize: 10 } },
              ]
            ],
            startY: currentY,
            theme: 'grid',
            styles: { cellPadding: 3, lineWidth: 0.1, lineColor: [200, 200, 200], valign: 'middle' as const, textColor: [pdfColors.textPrimary[0], pdfColors.textPrimary[1], pdfColors.textPrimary[2]] },
            columnStyles: { 0: { cellWidth: (pageWidth - margin * 2) / 3 }, 1: { cellWidth: (pageWidth - margin * 2) / 3 }, 2: { cellWidth: (pageWidth - margin * 2) / 3 } },
            margin: { left: margin, right: margin },
        });
        currentY = (doc as any).lastAutoTable.finalY + 10; // Update Y after table

        // --- Speech Strengths (Simulated Chips) ---
        if (entry.result.speechAnalysis.speechStrengths?.length > 0) {
            currentY = checkPageBreak(currentY, 10);
            doc.setFontSize(11);
            doc.setFont('helvetica', 'bold');
            doc.setTextColor(pdfColors.success[0], pdfColors.success[1], pdfColors.success[2]); // Success color title
            doc.text('Speech Strengths:', margin, currentY);
            currentY += 6;
            doc.setFontSize(9); // Smaller font for chips
            doc.setFont('helvetica', 'normal');
            doc.setTextColor(pdfColors.success[0], pdfColors.success[1], pdfColors.success[2]); // Green text for items
            doc.setDrawColor(pdfColors.success[0], pdfColors.success[1], pdfColors.success[2]); // Green border
            doc.setLineWidth(0.2);

            let chipX = margin + 5; // Start chips closer to margin
            let chipContentY = currentY; // Y position for the current line of chips
            const chipPaddingX = 3;
            const chipPaddingY = 1.5;
            const chipHeight = 7; // Fixed height for chip simulation
            const chipSpacing = 3;

            entry.result.speechAnalysis.speechStrengths.forEach((strength: string) => {
                const textWidth = doc.getTextWidth(strength);
                const chipWidth = textWidth + chipPaddingX * 2;

                // Check if chip fits on current line
                if (chipX + chipWidth > pageWidth - margin) {
                    chipContentY += chipHeight + chipSpacing; // Move to next line
                    chipX = margin + 5; // Reset X
                    chipContentY = checkPageBreak(chipContentY, chipHeight + chipSpacing); // Check page break for new line
                }

                // Draw rounded border
                doc.roundedRect(chipX, chipContentY - chipHeight + chipPaddingY, chipWidth, chipHeight, 3, 3, 'S'); // Stroke

                // Draw text inside border
                doc.text(strength, chipX + chipPaddingX, chipContentY);

                chipX += chipWidth + chipSpacing; // Move X for next chip
            });
            currentY = chipContentY + chipHeight + 5; // Move Y below the last line of chips + spacing
            doc.setTextColor(pdfColors.textSecondary[0], pdfColors.textSecondary[1], pdfColors.textSecondary[2]); // Reset text color
        }

          // --- Vocabulary Analysis ---
          currentY = checkPageBreak(currentY, 30); // Space for section title
          doc.setFontSize(14);
          doc.setFont('helvetica', 'bold');
          doc.setTextColor(pdfColors.textPrimary[0], pdfColors.textPrimary[1], pdfColors.textPrimary[2]);
          doc.text('Vocabulary Analysis', margin, currentY);
          currentY += 10;

          // --- Vocabulary Level Table --- (Simplified version)
          currentY = checkPageBreak(currentY, 25); // Space for table
          const level = entry.result.vocabularyAnalysis.vocabularyLevel.distribution;
          autoTable(doc, {
            body: [
              [
                // Apply styling directly to cell content/styles
                { content: `Basic\n${level.basic}`, styles: { halign: 'center' as const, fontSize: 10, textColor: [pdfColors.textSecondary[0], pdfColors.textSecondary[1], pdfColors.textSecondary[2]] } },
                { content: `Intermediate\n${level.intermediate}`, styles: { halign: 'center' as const, fontSize: 10, textColor: [pdfColors.primary[0], pdfColors.primary[1], pdfColors.primary[2]] } },
                { content: `Advanced\n${level.advanced}`, styles: { halign: 'center' as const, fontSize: 10, textColor: [pdfColors.success[0], pdfColors.success[1], pdfColors.success[2]] } },
              ]
            ],
            startY: currentY,
            theme: 'grid', // Use grid for borders
            styles: {
              // fontSize: 10, // Base fontSize set in cell styles
              cellPadding: 3,
              lineWidth: 0.1,
              lineColor: [200, 200, 200], // Light gray border
              valign: 'middle' as const, // Vertically align content
            },
            columnStyles: { // Equal width columns
              0: { cellWidth: (pageWidth - margin * 2) / 3 },
              1: { cellWidth: (pageWidth - margin * 2) / 3 },
              2: { cellWidth: (pageWidth - margin * 2) / 3 },
            },
            margin: { left: margin, right: margin },
            // Removed complex didParseCell and didDrawPage hooks
          });
          currentY = (doc as any).lastAutoTable.finalY + 10; // Update Y after table

          // --- Lexical Diversity Table --- (Simplified version)
          currentY = checkPageBreak(currentY, 25); // Space for table
          const diversity = entry.result.vocabularyAnalysis.lexicalDiversity;
          autoTable(doc, {
            body: [
              [
                // Apply styling directly
                { content: `Unique Words\n${diversity.uniqueWords}`, styles: { halign: 'center' as const, fontSize: 10 } },
                { content: `Total Words\n${diversity.totalWords}`, styles: { halign: 'center' as const, fontSize: 10 } },
                { content: `Diversity Score\n${diversity.diversityScore.toFixed(2)}`, styles: { halign: 'center' as const, fontSize: 10 } },
              ]
            ],
            startY: currentY,
            theme: 'grid',
            styles: {
              // fontSize: 10, // Base fontSize set in cell styles
              cellPadding: 3,
              lineWidth: 0.1,
              lineColor: [200, 200, 200],
              textColor: [pdfColors.textPrimary[0], pdfColors.textPrimary[1], pdfColors.textPrimary[2]], // Default text color
              valign: 'middle' as const,
            },
            columnStyles: { // Equal width columns
              0: { cellWidth: (pageWidth - margin * 2) / 3 },
              1: { cellWidth: (pageWidth - margin * 2) / 3 },
              2: { cellWidth: (pageWidth - margin * 2) / 3 },
            },
            margin: { left: margin, right: margin },
            // Removed complex didParseCell and didDrawPage hooks
          });
          currentY = (doc as any).lastAutoTable.finalY + 10; // Update Y after table

          // --- Overused Words (Manual Card Layout) ---
          currentY = checkPageBreak(currentY, 15); // Space for title
          doc.setFontSize(11);
          doc.setFont('helvetica', 'bold');
          doc.setTextColor(pdfColors.warning[0], pdfColors.warning[1], pdfColors.warning[2]); // Warning color title
          doc.text('Overused Words:', margin, currentY);
          currentY += 6;

          if (entry.result.vocabularyAnalysis.overusedWords?.length > 0) {
            const cardWidth = (pageWidth - margin * 2 - 5) / 2; // 2 cards per row with 5 spacing
            let currentX = margin;
            let cardStartY = currentY;
            let maxCardHeightInRow = 0;

            entry.result.vocabularyAnalysis.overusedWords.forEach((word: any, index: number) => {
              let cardContentY = cardStartY;
              const cardIndent = currentX + 5; // Indent within current card X position
              const contentWidth = cardWidth - 10; // Content width inside card padding

              // --- Calculate Height ---
              let tempCardY = cardContentY;
              tempCardY += 5; // Top padding
              const wordNameLines = doc.splitTextToSize(word.word, contentWidth - 30); // Reserve space for chip
              tempCardY += wordNameLines.length * 4 + 2; // Word name + spacing
              tempCardY += 4 + 2; // "Better alternatives:" + spacing
              const alternativesText = word.alternatives.join(', ');
              const alternativesLines = doc.splitTextToSize(alternativesText, contentWidth);
              tempCardY += alternativesLines.length * 4; // Alternatives text
              tempCardY += 5; // Bottom padding
              const cardHeight = tempCardY - cardContentY;
              maxCardHeightInRow = Math.max(maxCardHeightInRow, cardHeight);

              // Check page break *before* drawing this card
              const checkY = cardStartY + maxCardHeightInRow; // Check based on potential max height
              if (checkY > pageHeight - margin) {
                doc.addPage();
                currentX = margin; // Reset X for new page
                cardStartY = margin; // Reset Y for new page
                maxCardHeightInRow = cardHeight; // Reset max height for this card on new page
              }
              cardContentY = cardStartY; // Reset content Y based on final cardStartY

              // --- Draw Border ---
              doc.setDrawColor(200, 200, 200); // Light gray border
              doc.setLineWidth(0.1);
              doc.roundedRect(currentX, cardStartY - 5, cardWidth, cardHeight, 3, 3, 'S'); // Stroke only

              // --- Draw Content ---
              cardContentY += 5; // Top padding

              // Word Name (Red)
              doc.setFontSize(10);
              doc.setFont('helvetica', 'bold');
              doc.setTextColor(pdfColors.error[0], pdfColors.error[1], pdfColors.error[2]);
              doc.text(wordNameLines, cardIndent, cardContentY);
              // cardContentY += wordNameLines.length * 4; // Y position managed below

              // Count Chip (Orange) - Position right within card
              const countText = `${word.count} times`;
              const countTextWidth = doc.getTextWidth(countText);
              const countChipWidth = countTextWidth + 6;
              const countChipX = currentX + cardWidth - countChipWidth - 5; // Right align inside card border
              const countChipY = cardContentY; // Align with top of word text
              doc.setFillColor(pdfColors.warning[0], pdfColors.warning[1], pdfColors.warning[2]);
              doc.roundedRect(countChipX, countChipY - 5, countChipWidth, 7, 2, 2, 'F');
              doc.setTextColor(pdfColors.white[0], pdfColors.white[1], pdfColors.white[2]);
              doc.setFontSize(8);
              doc.text(countText, countChipX + countChipWidth / 2, countChipY - 1.5, { align: 'center' });

              cardContentY += wordNameLines.length * 4 + 2; // Move Y below word name

              // "Better alternatives:" text
              doc.setFontSize(8);
              doc.setFont('helvetica', 'normal');
              doc.setTextColor(pdfColors.textSecondary[0], pdfColors.textSecondary[1], pdfColors.textSecondary[2]);
              doc.text("Better alternatives:", cardIndent, cardContentY);
              cardContentY += 4;

              // Alternatives Text (Green)
              doc.setFontSize(9);
              doc.setTextColor(pdfColors.success[0], pdfColors.success[1], pdfColors.success[2]); // Green text
              doc.text(alternativesLines, cardIndent, cardContentY);
              // cardContentY += alternativesLines.length * 4; // Y already updated by height calc

              // Move to next card position (2 columns)
              if ((index + 1) % 2 === 0 || index === entry.result.vocabularyAnalysis.overusedWords.length - 1) { // Move to next row or if it's the last item
                currentX = margin;
                cardStartY += maxCardHeightInRow + 5; // Move down by max height in row + spacing
                maxCardHeightInRow = 0; // Reset max height for new row
              } else { // Move to next column
                currentX += cardWidth + 5; // Add spacing between columns
              }
            });
            // Ensure currentY is below the last row of cards
            currentY = cardStartY; // Update main Y to the start of the next potential row
          } else {
            // Keep the "No overused words detected" text
            doc.setFontSize(9);
            doc.setFont('helvetica', 'italic');
            doc.setTextColor(pdfColors.textSecondary[0], pdfColors.textSecondary[1], pdfColors.textSecondary[2]);
            doc.text('No overused words detected.', margin + 5, currentY);
            currentY += 5;
          }
          currentY += 5; // Spacing after section

      }); // End forEach entry

      // --- Save the PDF ---
      const fileName = entryId
        ? `detailed-speaking-report-${entryId}.pdf`
        : `bulk-detailed-speaking-results-${new Date().toISOString().slice(0, 10)}.pdf`;
      doc.save(fileName);
      console.log(`Generated PDF: ${fileName}`);

      handleCloseExportMenu(); // Close menu if open
    };

    const handleExportPdf = () => {
      // This button handles the bulk export
      exportAsPdf(); // Calls the defined function without an ID
    };

    const hasResults = entries.some(entry => entry.result);
  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 8 }}>
      <Typography variant="h4" sx={{ mb: 4 }}>
        Bulk Detailed Speaking Checker
      </Typography>

      <Paper sx={{ p: 2, mb: 4 }}>
        {/* Model Selection UI Removed */}

        <Grid container spacing={2}>
          <Grid item>
            <Button
              variant="outlined"
              startIcon={<PlusIcon />}
              onClick={handleAddEntry}
            >
              Add Entry
            </Button>
          </Grid>
          <Grid item>
            <Button
              variant="outlined"
              startIcon={<FileIcon />}
              component="label"
            >
              Import CSV
              <input
                type="file"
                hidden
                accept=".csv"
                onChange={handleImportCSV}
              />
            </Button>
          </Grid>
          {hasResults && (
            <>
              <Grid item>
                <Button
                  variant="outlined"
                  startIcon={<DownloadIcon />}
                  onClick={handleExportCSV}
                >
                  Export CSV
                </Button>
              </Grid>
              <Grid item>
                <Button
                  variant="outlined"
                  startIcon={<FilePdf />}
                  onClick={handleExportPdf}
                >
                  Export PDF
                </Button>
              </Grid>
            </>
          )}
          <Grid item sx={{ ml: 'auto' }}>
            <Button
              variant="contained"
              startIcon={isProcessing ? <CircularProgress size={20} color="inherit" /> : <RefreshIcon />}
              onClick={handleCheck}
              disabled={isProcessing}
            >
              {isProcessing ? 'Processing...' : 'Check All'}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 4 }}>
          {error}
        </Alert>
      )}

      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 650 }} aria-label="speaking entries table">
          <TableHead>
            <TableRow>
              <TableCell width="5%">#</TableCell>
              <TableCell width="10%">Audio</TableCell>
              <TableCell width="35%">Transcript</TableCell>
              <TableCell width="20%">Examiner Content</TableCell>
              <TableCell width="15%">Result</TableCell>
              <TableCell width="15%">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {entries.map((entry, index) => (
              <TableRow key={entry.id}>
                <TableCell>{index + 1}</TableCell>
                <TableCell>
                  {entry.isProcessing ? (
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                      <CircularProgress size={24} />
                      <Typography variant="caption" sx={{ mt: 1 }}>
                        Transcribing...
                      </Typography>
                    </Box>
                  ) : entry.audioURL ? (
                    <Stack direction="column" spacing={1} alignItems="center">
                      <IconButton
                        onClick={() => handlePlayPause(entry.id, entry.audioURL!)}
                        color="primary"
                      >
                        {playingId === entry.id ? <Pause size={24} /> : <Play size={24} />}
                      </IconButton>
                      <Button
                        variant="outlined"
                        size="small"
                        component="label"
                        startIcon={<FileAudio size={16} />}
                      >
                        Replace
                        <input
                          type="file"
                          hidden
                          accept="audio/*"
                          onChange={(e) => handleAudioUpload(entry.id, e)}
                        />
                      </Button>
                    </Stack>
                  ) : (
                    <Button
                      variant="outlined"
                      component="label"
                      startIcon={<FileAudio />}
                      sx={{ whiteSpace: 'nowrap' }}
                    >
                      Upload Audio
                      <input
                        type="file"
                        hidden
                        accept="audio/*"
                        onChange={(e) => handleAudioUpload(entry.id, e)}
                      />
                    </Button>
                  )}
                </TableCell>
                <TableCell>
                  <TextField
                    multiline
                    rows={4}
                    fullWidth
                    variant="outlined"
                    placeholder="Enter transcript here..."
                    value={entry.transcript}
                    onChange={(e) => handleInputChange(entry.id, 'transcript', e.target.value)}
                    error={!entry.transcript.trim()}
                    helperText={!entry.transcript.trim() ? "Transcript is required" : ""}
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    multiline
                    rows={4}
                    fullWidth
                    variant="outlined"
                    placeholder="Optional: Enter examiner's questions and prompts here..."
                    value={entry.examinerContent || ''}
                    onChange={(e) => handleInputChange(entry.id, 'examinerContent', e.target.value)}
                  />
                </TableCell>
                <TableCell>
                  {entry.isProcessing ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                      <CircularProgress size={24} />
                    </Box>
                  ) : entry.error ? (
                    <Alert severity="error" sx={{ p: 1 }}>
                      {entry.error}
                    </Alert>
                  ) : entry.result ? (
                    <Stack spacing={1}>
                      <Chip
                        label={`Overall: ${entry.result.overallScore.toFixed(1)}`}
                        color={entry.result.overallScore >= 7 ? "success" : entry.result.overallScore >= 5 ? "primary" : "warning"}
                        sx={{ fontWeight: 'bold' }}
                      />
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => handleShowDetails(entry)}
                      >
                        View Details
                      </Button>
                    </Stack>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      Not processed yet
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    {entry.result && (
                      <Tooltip title="Export as PDF">
                        <IconButton
                          color="primary"
                          onClick={() => exportAsPdf(entry.id)}
                          sx={{ mr: 1 }}
                        >
                          <FilePdf size={20} />
                        </IconButton>
                      </Tooltip>
                    )}
                    <Tooltip title="Remove Entry">
                      <IconButton
                        color="error"
                        onClick={() => handleRemoveEntry(entry.id)}
                        disabled={entries.length <= 1}
                      >
                        <TrashIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Result Detail Dialog */}
      <Dialog
        open={detailDialogOpen}
        onClose={handleCloseDetails}
        fullWidth
        maxWidth="md"
      >
        {currentDetailEntry?.result && (
          <>
            <DialogTitle>
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Typography variant="h6">Detailed Speaking Assessment</Typography>
                <Chip
                  label={`Overall: ${currentDetailEntry.result.overallScore.toFixed(1)}`}
                  color={currentDetailEntry.result.overallScore >= 7 ? "success" : currentDetailEntry.result.overallScore >= 5 ? "primary" : "warning"}
                  sx={{ fontWeight: 'bold' }}
                />
              </Stack>
            </DialogTitle>
            <Tabs
              value={detailTabValue}
              onChange={handleDetailTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{ px: 2, borderBottom: 1, borderColor: 'divider' }}
            >
              <Tab label="Criteria Scores" />
              <Tab label="Question Analysis" />
              <Tab label="Speech Analysis" />
              <Tab label="Vocabulary" />
            </Tabs>
            <DialogContent dividers>
              {detailTabValue === 0 && (
                <Grid container spacing={3}>
                  {currentDetailEntry.result.criteria.map((criterion: any, idx: number) => (
                    <Grid item xs={12} key={idx}>
                      <Card variant="outlined">
                        <CardContent>
                          <Stack spacing={2}>
                            <Stack direction="row" justifyContent="space-between" alignItems="center">
                              <Typography variant="h6">{criterion.name}</Typography>
                              <Chip
                                label={criterion.score.toFixed(1)}
                                color={criterion.score >= 7 ? "success" : criterion.score >= 5 ? "primary" : "warning"}
                              />
                            </Stack>
                            <Typography variant="body1">{criterion.feedback}</Typography>
                            <Grid container spacing={2}>
                              <Grid item xs={12} md={4}>
                                <Typography variant="subtitle2" color="success.main">Strengths</Typography>
                                <ul>
                                  {criterion.strengths.map((strength: string, i: number) => (
                                    <li key={i}><Typography variant="body2">{strength}</Typography></li>
                                  ))}
                                </ul>
                              </Grid>
                              <Grid item xs={12} md={4}>
                                <Typography variant="subtitle2" color="error.main">Weaknesses</Typography>
                                <ul>
                                  {criterion.weaknesses.map((weakness: string, i: number) => (
                                    <li key={i}><Typography variant="body2">{weakness}</Typography></li>
                                  ))}
                                </ul>
                              </Grid>
                              <Grid item xs={12} md={4}>
                                <Typography variant="subtitle2" color="primary.main">Improvements</Typography>
                                <ul>
                                  {criterion.improvements.map((improvement: string, i: number) => (
                                    <li key={i}><Typography variant="body2">{improvement}</Typography></li>
                                  ))}
                                </ul>
                              </Grid>
                            </Grid>
                          </Stack>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              )}

              {detailTabValue === 1 && (
                <Stack spacing={3}>
                  {currentDetailEntry.result.questionAnalysis.map((qa: any, idx: number) => (
                    <Card key={idx} variant="outlined">
                      <CardContent>
                        <Stack spacing={2}>
                          <Typography variant="subtitle1" color="primary">
                            Question {idx + 1}
                          </Typography>
                          <Typography variant="body1">
                            <strong>Q:</strong> {qa.question}
                          </Typography>
                          <Typography variant="body1">
                            <strong>A:</strong> {qa.response}
                          </Typography>

                          <Box>
                            <Typography variant="subtitle2" color="success.main" gutterBottom>
                              Strengths
                            </Typography>
                            <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                              {qa.strengths.map((strength: string, i: number) => (
                                <Chip key={i} label={strength} color="success" variant="outlined" />
                              ))}
                            </Stack>
                          </Box>

                          <Box>
                            <Typography variant="subtitle2" color="error.main" gutterBottom>
                              Mistakes
                            </Typography>
                            <Stack spacing={1}>
                              {qa.mistakes.map((mistake: string, i: number) => (
                                <Typography key={i} variant="body2" color="error.main">
                                  • {mistake}
                                </Typography>
                              ))}
                            </Stack>
                          </Box>

                          <Box>
                            <Typography variant="subtitle2" color="primary.main" gutterBottom>
                              Suggested Improvements
                            </Typography>
                            <Stack spacing={1}>
                              {qa.improvements.map((improvement: string, i: number) => (
                                <Typography key={i} variant="body2" color="primary.main">
                                  • {improvement}
                                </Typography>
                              ))}
                            </Stack>
                          </Box>
                        </Stack>
                      </CardContent>
                    </Card>
                  ))}
                </Stack>
              )}

              {detailTabValue === 2 && (
                <Stack spacing={3}>
                  <Typography variant="h6">Speech Errors</Typography>

                  <Typography variant="subtitle2">Speech Strengths</Typography>
                  <Box>
                    {currentDetailEntry.result.speechAnalysis.speechStrengths.map((strength: string, idx: number) => (
                      <Chip
                        key={idx}
                        label={strength}
                        color="success"
                        variant="outlined"
                        sx={{ m: 0.5 }}
                      />
                    ))}
                  </Box>

                  <Box>
                    <Typography variant="h6" gutterBottom>Error Summary</Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={4}>
                        <Paper variant="outlined" sx={{ p: 2 }}>
                          <Typography variant="subtitle2">Total Errors</Typography>
                          <Typography variant="h4">{currentDetailEntry.result.speechAnalysis.totalErrors}</Typography>
                        </Paper>
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Paper variant="outlined" sx={{ p: 2 }}>
                          <Typography variant="subtitle2">Error Frequency</Typography>
                          <Typography variant="h6">{currentDetailEntry.result.speechAnalysis.errorFrequency}</Typography>
                        </Paper>
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Paper variant="outlined" sx={{ p: 2 }}>
                          <Typography variant="subtitle2">Most Common Error</Typography>
                          <Typography variant="body1">{currentDetailEntry.result.speechAnalysis.mostFrequentErrorType}</Typography>
                        </Paper>
                      </Grid>
                    </Grid>
                  </Box>
                </Stack>
              )}

              {detailTabValue === 3 && (
                <Stack spacing={3}>
                  <Box>
                    <Typography variant="h6" gutterBottom>Vocabulary Level</Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={4}>
                        <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                          <Typography variant="subtitle2">Basic</Typography>
                          <Typography variant="h5" color="text.secondary">
                            {currentDetailEntry.result.vocabularyAnalysis.vocabularyLevel.distribution.basic}
                          </Typography>
                        </Paper>
                      </Grid>
                      <Grid item xs={4}>
                        <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                          <Typography variant="subtitle2">Intermediate</Typography>
                          <Typography variant="h5" color="primary.main">
                            {currentDetailEntry.result.vocabularyAnalysis.vocabularyLevel.distribution.intermediate}
                          </Typography>
                        </Paper>
                      </Grid>
                      <Grid item xs={4}>
                        <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                          <Typography variant="subtitle2">Advanced</Typography>
                          <Typography variant="h5" color="success.main">
                            {currentDetailEntry.result.vocabularyAnalysis.vocabularyLevel.distribution.advanced}
                          </Typography>
                        </Paper>
                      </Grid>
                    </Grid>
                  </Box>

                  <Typography variant="h6">Overused Words</Typography>
                  <Grid container spacing={2}>
                    {currentDetailEntry.result.vocabularyAnalysis.overusedWords.map((word: any, idx: number) => (
                      <Grid item xs={12} sm={6} md={4} key={idx}>
                        <Paper variant="outlined" sx={{ p: 2 }}>
                          <Stack spacing={1}>
                            <Stack direction="row" justifyContent="space-between">
                              <Typography variant="subtitle2" color="error.main">{word.word}</Typography>
                              <Chip size="small" label={`${word.count} times`} color="warning" />
                            </Stack>
                            <Typography variant="body2">Better alternatives:</Typography>
                            <Box>
                              {word.alternatives.map((alt: string, i: number) => (
                                <Chip key={i} label={alt} size="small" color="success" variant="outlined" sx={{ m: 0.5 }} />
                              ))}
                            </Box>
                          </Stack>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>

                  <Typography variant="h6">Lexical Diversity</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Paper variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="subtitle2">Unique Words</Typography>
                        <Typography variant="h5">{currentDetailEntry.result.vocabularyAnalysis.lexicalDiversity.uniqueWords}</Typography>
                      </Paper>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Paper variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="subtitle2">Total Words</Typography>
                        <Typography variant="h5">{currentDetailEntry.result.vocabularyAnalysis.lexicalDiversity.totalWords}</Typography>
                      </Paper>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Paper variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="subtitle2">Diversity Score</Typography>
                        <Typography variant="h5">{currentDetailEntry.result.vocabularyAnalysis.lexicalDiversity.diversityScore.toFixed(2)}</Typography>
                      </Paper>
                    </Grid>
                  </Grid>
                </Stack>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDetails}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Container>
  );
}
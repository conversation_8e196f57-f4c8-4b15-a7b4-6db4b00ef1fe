import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function POST(req: NextRequest) {
  try {
    // Parse the form data
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const componentType = formData.get('componentType') as string;
    const taskQuestions = formData.get('taskQuestions') as string || '';
    const applyToAll = formData.get('applyToAll') === 'true';

    if (!file) {
      return NextResponse.json(
        { error: 'No image file provided' },
        { status: 400 }
      );
    }

    if (!componentType) {
      return NextResponse.json(
        { error: 'Component type is required' },
        { status: 400 }
      );
    }

    // Validate file type
    const validImageTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    if (!validImageTypes.includes(file.type)) {
      console.error(`Invalid file type: ${file.type}`);
      return NextResponse.json(
        { error: `File type must be one of JPG, PNG, or PDF. Got: ${file.type}` },
        { status: 400 }
      );
    }

    // Process the file - convert to data URL
    console.log('Processing file:', file.name, 'type:', file.type, 'size:', file.size);
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    const base64 = buffer.toString('base64');
    const mimeType = file.type || 'application/octet-stream';
    const fileUrl = `data:${mimeType};base64,${base64}`;

    // Create or update the writing material with the task image
    let writingMaterial;
    try {
      // First, check if we have a default writing material for task 1
      writingMaterial = await prisma.writingMaterial.findFirst({
        where: {
          title: 'Default Writing Material',
          taskType: 'task1'
        }
      });

      if (writingMaterial) {
        // Update the existing material with the new image
        writingMaterial = await prisma.writingMaterial.update({
          where: { id: writingMaterial.id },
          data: {
            taskQuestion: taskQuestions || 'Default Task 1 question: Describe the chart or graph.',
            taskImage: fileUrl // Add the image URL to the material
          }
        });
        console.log('Updated existing writing material with task 1 image:', writingMaterial.id);
      } else {
        // Create a new material with the image
        writingMaterial = await prisma.writingMaterial.create({
          data: {
            title: 'Default Writing Material',
            taskType: 'task1',
            taskQuestion: taskQuestions || 'Default Task 1 question: Describe the chart or graph.',
            taskImage: fileUrl // Add the image URL to the material
          }
        });
        console.log('Created new writing material with task 1 image:', writingMaterial.id);
      }
    } catch (materialError) {
      console.error('Error with writing material:', materialError);
      return NextResponse.json(
        { error: 'Failed to create or update writing material', details: materialError instanceof Error ? materialError.message : 'Unknown error' },
        { status: 500 }
      );
    }

    // If applyToAll is true, update all existing writing entries for task 1
    if (applyToAll) {
      try {
        // Get all writing entries for task 1 that are not processed yet
        const entries = await prisma.writingEntry.findMany({
          where: {
            taskType: 'task1',
            // Only update entries that don't have a band score yet (not processed)
            band: null
          }
        });

        console.log(`Found ${entries.length} writing task 1 entries to update`);

        // Update each entry with the material ID and task question
        for (const entry of entries) {
          await prisma.writingEntry.update({
            where: { id: entry.id },
            data: {
              materialId: writingMaterial.id,
              taskQuestion: taskQuestions || 'Default Task 1 question: Describe the chart or graph.'
            }
          });
        }

        console.log(`Updated ${entries.length} writing task 1 entries with the new material`);
      } catch (updateError) {
        console.error('Error updating writing entries:', updateError);
        // Continue with the process even if some updates fail
      }
    }

    return NextResponse.json({
      success: true,
      materialId: writingMaterial.id,
      message: `Task 1 image applied successfully${applyToAll ? ' to all candidates' : ''}`
    });
  } catch (error) {
    console.error('Error applying task image:', error);
    return NextResponse.json(
      { error: 'Failed to apply task image', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

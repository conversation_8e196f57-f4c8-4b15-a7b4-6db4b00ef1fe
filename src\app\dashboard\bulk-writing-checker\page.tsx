'use client';

import * as React from 'react';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Chip from '@mui/material/Chip';
import LinearProgress from '@mui/material/LinearProgress';
import Divider from '@mui/material/Divider';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import FormHelperText from '@mui/material/FormHelperText';
import { Pencil as PencilIcon } from '@phosphor-icons/react/dist/ssr/Pencil';
import { ChartLineUp } from '@phosphor-icons/react/dist/ssr/ChartLineUp';
import { ArrowsClockwise } from '@phosphor-icons/react/dist/ssr/ArrowsClockwise';
import { CaretDown } from '@phosphor-icons/react/dist/ssr/CaretDown';
import { BookOpen } from '@phosphor-icons/react/dist/ssr/BookOpen';
import { Lightbulb } from '@phosphor-icons/react/dist/ssr/Lightbulb';
import { UploadSimple } from '@phosphor-icons/react/dist/ssr/UploadSimple';
import { Plus } from '@phosphor-icons/react/dist/ssr/Plus';
import { Trash } from '@phosphor-icons/react/dist/ssr/Trash';
import { File } from '@phosphor-icons/react/dist/ssr/File';
import { Download } from '@phosphor-icons/react/dist/ssr/Download';
import { Article } from '@phosphor-icons/react/dist/ssr/Article';
import { FilePdf } from '@phosphor-icons/react/dist/ssr/FilePdf';
import { FileText } from '@phosphor-icons/react/dist/ssr/FileText';
import { Image } from '@phosphor-icons/react/dist/ssr/Image';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
// @ts-ignore - jsPDF has some TS issues but works fine
import 'jspdf-autotable';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
// @ts-ignore - Tesseract.js doesn't have TypeScript definitions
import * as Tesseract from 'tesseract.js';
// @ts-ignore - react-pdf doesn't have TypeScript definitions
import { pdfjs } from 'react-pdf';

// Initialize PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

type ModelType = 'gpt-4o';

// Writing Material type
interface WritingMaterial {
  id: string;
  title: string;
  taskType: string;
  taskQuestion: string;
  taskImage?: string;
  sampleAnswer?: string;
  createdAt: string;
  updatedAt: string;
}

// Essay types
interface Essay {
  id: string;
  name: string;
  text: string;
  studentId: string;
  materialId?: string;
  taskQuestion: string;
  taskType: 'Task 1' | 'Task 2';
  taskImageFile?: File | null;
  taskImageUrl?: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  feedback?: EssayFeedback;
  error?: string;
}

// Extended types for structured feedback
interface IELTSCriterion {
  name: string;
  score: number;
  feedback: string;
  strengths: string[];
  weaknesses: string[];
  improvements: string[];
}

interface VocabularyAnalysis {
  overusedWords: Array<{ word: string, count: number, alternatives: string[] }>;
  collocations: Array<{ incorrect: string, correct: string, context: string }>;
  academicPhrases: string[];
  lexicalDiversity: {
    uniqueWords: number;
    totalWords: number;
    diversityScore: number;
  };
  vocabularyLevel?: {
    basic: string[];
    intermediate: string[];
    advanced: string[];
    distribution: {
      basic: string;
      intermediate: string;
      advanced: string;
    }
  };
  topicRelevantVocabulary?: string[];
  transitionWords?: {
    used: string[];
    suggestions: string[];
  };
  idiomsAndPhrases?: {
    used: string[];
    suggestions: string[];
  };
}

interface SentenceImprovement {
  original: string;
  improved: string;
  explanation: string;
}

interface EssayFeedback {
  overallScore: number;
  criteria: IELTSCriterion[];
  vocabularyAnalysis: VocabularyAnalysis;
  sentenceImprovements: SentenceImprovement[];
  comparativeAnalysis: {
    structuralDifferences: string[];
    argumentationImprovements: string[];
    bandExample: string;
  };
  grammarAnalysis?: {
    errorsByType: {
      [key: string]: Array<{
        original: string;
        correction: string;
        explanation: string;
      }>
    };
    grammarStrengths: string[];
    totalErrors: number;
    errorFrequency: string;
    mostFrequentErrorType: string;
  };
  structureAnalysis?: {
    introduction: string;
    bodyParagraphs: string[];
    conclusion: string;
    paragraphCohesion: string;
  };
}

export default function BulkWritingCheckerPage(): React.JSX.Element {
  const [essays, setEssays] = React.useState<Essay[]>([]);
  const [loading, setLoading] = React.useState<boolean>(false);
  const [error, setError] = React.useState<string | null>(null);
  const [selectedModel, setSelectedModel] = React.useState<ModelType>('gpt-4o');
  const [selectedEssay, setSelectedEssay] = React.useState<Essay | null>(null);
  const [newEssayName, setNewEssayName] = React.useState<string>('');
  const [newEssayText, setNewEssayText] = React.useState<string>('');
  const [newEssayStudentId, setNewEssayStudentId] = React.useState<string>('');
  const [newEssayTaskQuestion, setNewEssayTaskQuestion] = React.useState<string>('');
  const [newEssayTaskType, setNewEssayTaskType] = React.useState<'Task 1' | 'Task 2'>('Task 2');
  const [newEssayTaskImageFile, setNewEssayTaskImageFile] = React.useState<File | null>(null);
  const [newEssayMaterialId, setNewEssayMaterialId] = React.useState<string>('');
  const [showAddForm, setShowAddForm] = React.useState<boolean>(false);
  const [showTaskImageDialog, setShowTaskImageDialog] = React.useState<boolean>(false);
  const [exportMenuAnchorEl, setExportMenuAnchorEl] = React.useState<null | HTMLElement>(null);
  const [tabValue, setTabValue] = React.useState(0);
  const [globalTaskQuestion, setGlobalTaskQuestion] = React.useState<string>('');
  const [globalTaskType, setGlobalTaskType] = React.useState<'Task 1' | 'Task 2'>('Task 2');
  const [globalTaskImageFile, setGlobalTaskImageFile] = React.useState<File | null>(null);
  const [globalTaskImageUrl, setGlobalTaskImageUrl] = React.useState<string>('');
  const [globalStudentId, setGlobalStudentId] = React.useState<string>('');
  const [fileProcessingProgress, setFileProcessingProgress] = React.useState<{[key: string]: number}>({});
  const [writingMaterials, setWritingMaterials] = React.useState<WritingMaterial[]>([]);
  const [loadingMaterials, setLoadingMaterials] = React.useState<boolean>(false);

  // Fetch writing materials on component mount
  React.useEffect(() => {
    const fetchWritingMaterials = async () => {
      try {
        setLoadingMaterials(true);
        const response = await fetch('/api/writing-materials');
        if (!response.ok) {
          throw new Error('Failed to fetch writing materials');
        }
        const data = await response.json();
        setWritingMaterials(data);
      } catch (err) {
        console.error('Error fetching writing materials:', err);
      } finally {
        setLoadingMaterials(false);
      }
    };

    fetchWritingMaterials();
  }, []);

  // Generate unique ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  };

  // Apply global task settings to all essays
  const applyGlobalTaskSettings = () => {
    // For Task 2, require a task question
    if (globalTaskType === 'Task 2' && !globalTaskQuestion.trim()) {
      setError('Please enter a task question first');
      return;
    }

    // For Task 1, require a task image
    if (globalTaskType === 'Task 1' && !globalTaskImageFile) {
      setError('Please upload a task image first');
      return;
    }

    // For Task 1, set the default task question
    const taskQuestion = globalTaskType === 'Task 1' ?
      'Describe the information shown in the image. Summarize the main features and make comparisons where relevant.' :
      globalTaskQuestion;

    setEssays(prev =>
      prev.map(essay => {
        // Only update essays that match the global task type or don't have a task type set
        if (essay.taskType === globalTaskType || !essay.taskType) {
          return {
            ...essay,
            // Apply student ID if global is set and essay doesn't have one
            studentId: essay.studentId || globalStudentId || essay.studentId,
            taskQuestion: taskQuestion,
            taskType: globalTaskType,
            // For Task 1, apply the global task image if it exists
            ...(globalTaskType === 'Task 1' && globalTaskImageFile ? {
              taskImageFile: globalTaskImageFile,
              taskImageUrl: globalTaskImageUrl
            } : {})
          };
        }
        return essay;
      })
    );
    setError(null);
  };

  // Handle global task image upload
  const handleGlobalTaskImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setGlobalTaskImageFile(file);
      const imageUrl = URL.createObjectURL(file);
      setGlobalTaskImageUrl(imageUrl);
    }
  };

  const handleAddEssay = () => {
    if (!newEssayName.trim() || !newEssayText.trim()) {
      setError('Essay name and text are required');
      return;
    }

    // For Task 1, require an image and set a default task question
    if (newEssayTaskType === 'Task 1') {
      if (!newEssayTaskImageFile) {
        setError('For Task 1, an image of the chart/graph/diagram is required');
        return;
      }
    }

    // Set task question based on task type
    let taskQuestion = '';
    if (newEssayTaskType === 'Task 1') {
      // Default task question for Task 1
      taskQuestion = 'Describe the information shown in the image. Summarize the main features and make comparisons where relevant.';
    } else {
      // For Task 2, use the entered question or global question
      taskQuestion = newEssayTaskQuestion.trim() || globalTaskQuestion.trim();
      if (!taskQuestion) {
        setError('Task question is required for Task 2');
        return;
      }
    }

    // Create image URL if there's an image file
    let taskImageUrl = undefined;
    if (newEssayTaskImageFile) {
      taskImageUrl = URL.createObjectURL(newEssayTaskImageFile);
    }

    // If a writing material is selected, use its task type and question
    let finalTaskType = newEssayTaskType;
    let finalTaskQuestion = taskQuestion;

    if (newEssayMaterialId) {
      const selectedMaterial = writingMaterials.find(m => m.id === newEssayMaterialId);
      if (selectedMaterial) {
        finalTaskType = selectedMaterial.taskType === 'task1' ? 'Task 1' : 'Task 2';
        finalTaskQuestion = selectedMaterial.taskQuestion;
        if (selectedMaterial.taskImage) {
          taskImageUrl = selectedMaterial.taskImage;
        }
      }
    }

    const newEssay: Essay = {
      id: generateId(),
      name: newEssayName,
      text: newEssayText,
      studentId: newEssayStudentId || globalStudentId || `anonymous-${Date.now()}`,
      materialId: newEssayMaterialId || undefined,
      taskQuestion: finalTaskQuestion,
      taskType: finalTaskType,
      taskImageFile: newEssayTaskImageFile,
      taskImageUrl: taskImageUrl,
      status: 'pending'
    };

    setEssays([...essays, newEssay]);
    setNewEssayName('');
    setNewEssayText('');
    setNewEssayStudentId('');
    setNewEssayTaskQuestion('');
    setNewEssayTaskType('Task 2');
    setNewEssayTaskImageFile(null);
    setNewEssayMaterialId('');
    setShowAddForm(false);
    setError(null);
  };

  const handleRemoveEssay = (id: string) => {
    // Revoke any object URLs to prevent memory leaks
    const essayToRemove = essays.find(essay => essay.id === id);
    if (essayToRemove?.taskImageUrl) {
      URL.revokeObjectURL(essayToRemove.taskImageUrl);
    }

    setEssays(essays.filter(essay => essay.id !== id));
    if (selectedEssay?.id === id) {
      setSelectedEssay(null);
    }
  };

  const handleOpenTaskImageDialog = () => {
    setShowTaskImageDialog(true);
  };

  const handleCloseTaskImageDialog = () => {
    setShowTaskImageDialog(false);
  };

  const handleTaskImageFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setNewEssayTaskImageFile(event.target.files[0]);
      setShowTaskImageDialog(false);
    }
  };

  const handleSelectEssay = (essay: Essay) => {
    setSelectedEssay(essay);

    // Debug: Log criteria names if feedback exists
    if (essay.status === 'completed' && essay.feedback) {
      console.log('Essay criteria:', essay.feedback.criteria.map(c => ({ name: c.name, score: c.score })));

      // Check for grammar criteria with different search patterns
      const grammarCriteria = essay.feedback.criteria.find(c =>
        c.name.includes('Grammar') ||
        c.name.includes('Grammatical') ||
        c.name.includes('Range and Accuracy') ||
        c.name.includes('Accuracy')
      );

      console.log('Grammar criteria found:', grammarCriteria);

      if (!grammarCriteria) {
        console.log('No grammar criteria found with standard patterns. All criteria names:',
          essay.feedback.criteria.map(c => c.name)
        );
      }
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // Initialize progress tracking for all files
    const initialProgress: {[key: string]: number} = {};
    Array.from(files).forEach(file => {
      initialProgress[file.name] = 0;
    });
    setFileProcessingProgress(initialProgress);

    // Process each file based on its type
    for (const file of Array.from(files)) {
      const fileId = generateId();
      const fileName = file.name;
      const fileExt = fileName.split('.').pop()?.toLowerCase() || '';

      // Determine task type based on filename
      // If filename contains "task1", "task 1", or "task-1", it's Task 1
      // Otherwise, default to global task type or Task 2
      const taskType = /task[\s-_]?1/i.test(fileName) ? 'Task 1' : globalTaskType || 'Task 2';

      // For Task 1, check if this is an image file that should be used as the task image
      const isImageFile = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff'].includes(fileExt);
      let taskImageUrl = undefined;
      let taskImageFile = null;

      // Set default task question based on task type
      let taskQuestion = globalTaskQuestion;
      if (taskType === 'Task 1') {
        taskQuestion = 'Describe the information shown in the image. Summarize the main features and make comparisons where relevant.';

        // If this is an image file and task type is Task 1, it might be the task image
        if (isImageFile && /chart|graph|diagram|figure|table|map/i.test(fileName)) {
          // This looks like a task image for Task 1
          taskImageFile = file;
          taskImageUrl = URL.createObjectURL(file);

          // Skip processing this file as an essay
          setFileProcessingProgress(prev => ({...prev, [fileName]: 100}));
          continue;
        }
      }

      // Add a pending essay while we process the file
      const pendingEssay: Essay = {
        id: fileId,
        name: fileName,
        text: "Processing...",
        studentId: globalStudentId || `anonymous-${Date.now()}`, // Add required studentId
        taskQuestion: taskQuestion,
        taskType: taskType,
        taskImageFile: taskImageFile,
        taskImageUrl: taskImageUrl,
        status: 'pending'
      };

      setEssays(prev => [...prev, pendingEssay]);

      try {
        let extractedText = '';

        // Process based on file type
        if (['jpg', 'jpeg', 'png', 'bmp', 'tiff'].includes(fileExt)) {
          // Process image with OCR
          setFileProcessingProgress(prev => ({...prev, [fileName]: 10}));
          const imageUrl = URL.createObjectURL(file);

          // Use Tesseract.js for OCR
          const result = await Tesseract.recognize(
            imageUrl,
            'eng',
            {
              logger: (m: any) => {
                if (m.status === 'recognizing text') {
                  setFileProcessingProgress(prev => ({
                    ...prev,
                    [fileName]: Math.floor(10 + m.progress * 80)
                  }));
                }
              }
            }
          );

          extractedText = result.data.text;
          URL.revokeObjectURL(imageUrl); // Clean up
        } else if (fileExt === 'pdf') {
          // Process PDF
          setFileProcessingProgress(prev => ({...prev, [fileName]: 10}));
          const fileArrayBuffer = await file.arrayBuffer();

          // Load the PDF document
          const loadingTask = pdfjs.getDocument(fileArrayBuffer);
          const pdf = await loadingTask.promise;
          const numPages = pdf.numPages;
          let pdfText = '';

          // Extract text from each page
          for (let pageNum = 1; pageNum <= numPages; pageNum++) {
            const page = await pdf.getPage(pageNum);
            const textContent = await page.getTextContent();
            const pageText = textContent.items.map((item: any) => item.str).join(' ');
            pdfText += pageText + '\n\n';

            // Update progress
            setFileProcessingProgress(prev => ({
              ...prev,
              [fileName]: Math.floor(10 + (pageNum / numPages) * 80)
            }));
          }

          extractedText = pdfText;
        } else {
          // For text-based files, use the standard FileReader
          extractedText = await new Promise<string>((resolve) => {
            const reader = new FileReader();
            reader.onload = (e) => {
              resolve(e.target?.result as string || '');
            };
            reader.onprogress = (e) => {
              if (e.lengthComputable) {
                setFileProcessingProgress(prev => ({
                  ...prev,
                  [fileName]: Math.floor((e.loaded / e.total) * 90)
                }));
              }
            };
            reader.readAsText(file);
          });
        }

        // Mark as complete
        setFileProcessingProgress(prev => ({...prev, [fileName]: 100}));

        // Update the essay with the extracted text
        setEssays(prev =>
          prev.map(essay =>
            essay.id === fileId
              ? {
                  ...essay,
                  text: extractedText,
                  status: 'pending'
                }
              : essay
          )
        );
      } catch (error) {
        console.error(`Error processing file ${fileName}:`, error);

        // Update the essay with the error
        setEssays(prev =>
          prev.map(essay =>
            essay.id === fileId
              ? {
                  ...essay,
                  text: "Error processing file. Please try again.",
                  status: 'error',
                  error: 'File processing failed'
                }
              : essay
          )
        );
      }
    }

    // Clear the input
    event.target.value = '';
  };

  const handleOpenExportMenu = (event: React.MouseEvent<HTMLElement>) => {
    setExportMenuAnchorEl(event.currentTarget);
  };

  const handleCloseExportMenu = () => {
    setExportMenuAnchorEl(null);
  };

  const handleUpdateTaskQuestion = (id: string, taskQuestion: string) => {
    setEssays(prev =>
      prev.map(essay =>
        essay.id === id
          ? { ...essay, taskQuestion }
          : essay
      )
    );
  };

  const handleBulkCheck = async () => {
    if (essays.length === 0) {
      setError('No essays to check');
      return;
    }

    // If global task question is set, use it for essays without a task question
    if (globalTaskQuestion.trim()) {
      setEssays(prev =>
        prev.map(essay => ({
          ...essay,
          taskQuestion: essay.taskQuestion.trim() || globalTaskQuestion
        }))
      );
    }

    // If global task image is set, use it for Task 1 essays without an image
    if (globalTaskImageFile && globalTaskImageUrl) {
      setEssays(prev =>
        prev.map(essay => {
          if (essay.taskType === 'Task 1' && !essay.taskImageFile) {
            return {
              ...essay,
              taskImageFile: globalTaskImageFile,
              taskImageUrl: globalTaskImageUrl
            };
          }
          return essay;
        })
      );
    }

    // Validate all essays have task questions
    const missingTaskQuestions = essays.filter(essay => !essay.taskQuestion.trim());
    if (missingTaskQuestions.length > 0) {
      setError(`${missingTaskQuestions.length} essay(s) missing task questions. Please add a global task question or individual task questions for all essays.`);
      return;
    }

    setLoading(true);
    setError(null);

    // Update all essays to processing status
    setEssays(prev =>
      prev.map(essay => ({
        ...essay,
        status: 'processing'
      }))
    );

    try {
      const response = await fetch('/api/bulk-writing-checker', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          essays: essays.map(essay => ({
            id: essay.id,
            text: essay.text,
            studentId: essay.studentId || globalStudentId || `anonymous-${Date.now()}`,
            materialId: essay.materialId,
            taskQuestion: essay.taskQuestion,
            taskType: essay.taskType || 'Task 2',
            hasTaskImage: !!essay.taskImageUrl
          })),
          model: selectedModel
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to check essays');
      }

      const results = await response.json();

      // Update essays with results
      setEssays(prev =>
        prev.map(essay => {
          const result = results.find((r: any) => r.id === essay.id);
          if (!result) {
            return {
              ...essay,
              status: 'error',
              error: 'No result returned for this essay'
            };
          }

          if (result.status === 'error') {
            return {
              ...essay,
              status: 'error',
              error: result.error
            };
          }

          return {
            ...essay,
            status: 'completed',
            feedback: result.feedback
          };
        })
      );

    } catch (err) {
      console.error('Error checking essays:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');

      // Reset essays to pending status
      setEssays(prev =>
        prev.map(essay => ({
          ...essay,
          status: essay.status === 'processing' ? 'pending' : essay.status
        }))
      );
    } finally {
      setLoading(false);
    }
  };

  const exportAsJson = () => {
    const completedEssays = essays.filter(essay => essay.status === 'completed');
    if (completedEssays.length === 0) {
      setError('No completed essays to export');
      return;
    }

    const exportData = {
      date: new Date().toISOString(),
      model: selectedModel,
      essays: completedEssays.map(essay => ({
        name: essay.name,
        text: essay.text,
        feedback: essay.feedback
      }))
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bulk-essays-results-${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    handleCloseExportMenu();
  };

  const exportAsPdf = (essayId?: string) => {
    // If essayId is provided, export only that essay
    // Otherwise export all completed essays
    const essaysToExport = essayId
      ? essays.filter(essay => essay.id === essayId && essay.status === 'completed')
      : essays.filter(essay => essay.status === 'completed');

    if (essaysToExport.length === 0) {
      setError('No completed essays to export');
      return;
    }

    // Create PDF document
    const doc = new jsPDF();

    // Add header
    doc.setFillColor(100, 181, 246);
    doc.rect(0, 0, 210, 25, 'F');

    // Add title
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(20);
    doc.text('IELTS Writing Assessment Results', 105, 17, { align: 'center' });

    let currentY = 45;

    // Basic summary table with overall scores only
    autoTable(doc, {
      head: [['Essay Name', 'Overall Score']],
      body: essaysToExport.map(essay => [
        essay.name,
        essay.feedback?.overallScore.toFixed(1) || '-'
      ]),
      startY: currentY,
      styles: {
        fontSize: 10,
        lineColor: [220, 220, 220],
        lineWidth: 0.1,
      },
      headStyles: {
        fillColor: [100, 181, 246],
        textColor: [255, 255, 255],
        fontStyle: 'bold'
      },
      alternateRowStyles: {
        fillColor: [240, 240, 240]
      },
      columnStyles: {
        0: { cellWidth: 'auto' },
        1: { cellWidth: 25, halign: 'center' }
      },
      margin: { left: 10, right: 10 }
    });

    currentY = (doc as any).lastAutoTable.finalY + 15;

    // Detailed feedback for each essay
    essaysToExport.forEach((essay, essayIndex) => {
      // Start a new page for each essay
      if (essayIndex > 0) {
        doc.addPage();
        currentY = 25;
      }

      // Add essay header
      doc.setFillColor(100, 181, 246);
      doc.rect(10, currentY, 190, 12, 'F');
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(12);
      doc.setTextColor(255, 255, 255);
      doc.text(`Essay: ${essay.name}`, 15, currentY + 8);
      currentY += 20;

      // Overall score
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(11);
      doc.setTextColor(0, 0, 0);
      doc.text(`Overall Band Score:`, 15, currentY);

      // Add score visual indicator
      const score = essay.feedback?.overallScore || 0;
      let scoreColor;
      if (score >= 7) {
        scoreColor = [76, 175, 80]; // Green for high scores
      } else if (score >= 5.5) {
        scoreColor = [255, 183, 77]; // Orange for medium scores
      } else {
        scoreColor = [220, 53, 69]; // Red for low scores
      }

      // Score box
      doc.setFillColor(scoreColor[0], scoreColor[1], scoreColor[2]);
      doc.roundedRect(102, currentY - 5, 20, 10, 2, 2, 'F');
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(10);
      doc.text(score.toFixed(1), 112, currentY, { align: 'center' });

      // Add band example
      if (essay.feedback?.comparativeAnalysis?.bandExample) {
        doc.setFont('helvetica', 'italic');
        doc.setFontSize(9);
        doc.setTextColor(80, 80, 80);
        const bandExampleLines = doc.splitTextToSize(essay.feedback.comparativeAnalysis.bandExample, 180);
        doc.text(bandExampleLines, 15, currentY + 15);
        currentY += bandExampleLines.length * 5 + 15;
      } else {
        currentY += 20;
      }

      // Add task question
      doc.setFillColor(240, 240, 240);
      doc.rect(15, currentY, 180, 15, 'F');
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(9);
      doc.setTextColor(0, 0, 0);
      doc.text('Task Question:', 20, currentY + 5);
      doc.setFont('helvetica', 'normal');
      const taskLines = doc.splitTextToSize(essay.taskQuestion, 170);
      doc.text(taskLines, 20, currentY + 12);
      currentY += taskLines.length * 5 + 10;

      // ===== CRITERIA BREAKDOWN SECTION =====
      if (essay.feedback?.criteria && essay.feedback.criteria.length > 0) {
        doc.setFillColor(232, 240, 254);
        doc.rect(10, currentY, 190, 10, 'F');
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(11);
        doc.setTextColor(25, 118, 210);
        doc.text('Criteria Breakdown', 15, currentY + 7);
        currentY += 15;

        // Loop through each criterion
        essay.feedback.criteria.forEach((criterion: any, index: number) => {
          // Check if we need a new page
          if (currentY > 230) {
            doc.addPage();
            currentY = 25;
          }

          // Criterion header with score
          doc.setFillColor(245, 245, 245);
          doc.rect(15, currentY, 180, 10, 'F');
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(10);
          doc.setTextColor(0, 0, 0);
          doc.text(`${criterion.name}: ${criterion.score.toFixed(1)}`, 20, currentY + 7);
          currentY += 15;

          // Criterion feedback
          if (criterion.feedback) {
            doc.setFont('helvetica', 'normal');
            doc.setFontSize(9);
            const feedbackLines = doc.splitTextToSize(criterion.feedback, 170);
            doc.text(feedbackLines, 20, currentY);
            currentY += feedbackLines.length * 5 + 5;
          }

          // Three columns: Strengths, Weaknesses, Improvements
          const columns = [
            { title: 'Strengths', color: [76, 175, 80], items: criterion.strengths },
            { title: 'Areas to Improve', color: [220, 53, 69], items: criterion.weaknesses },
            { title: 'Recommendations', color: [25, 118, 210], items: criterion.improvements }
          ];

          // Calculate column width
          const columnWidth = 170 / columns.length;

          // Draw column titles
          columns.forEach((column, colIndex) => {
            const xPos = 20 + (colIndex * columnWidth);
            doc.setFont('helvetica', 'bold');
            doc.setFontSize(9);
            doc.setTextColor(column.color[0], column.color[1], column.color[2]);
            doc.text(column.title, xPos, currentY);
          });
          currentY += 7;

          // Calculate max items to display (for height consistency)
          const maxItems = Math.max(
            criterion.strengths?.length || 0,
            criterion.weaknesses?.length || 0,
            criterion.improvements?.length || 0
          );

          const bulletPoints = Math.min(maxItems, 5); // Limit to 5 items per column

          // Draw items in each column
          for (let i = 0; i < bulletPoints; i++) {
            columns.forEach((column, colIndex) => {
              const xPos = 20 + (colIndex * columnWidth);
              if (column.items && column.items[i]) {
                doc.setFont('helvetica', 'normal');
                doc.setFontSize(8);
                doc.setTextColor(0, 0, 0);

                // Handle long text by wrapping
                const itemText = `• ${column.items[i]}`;
                const wrappedText = doc.splitTextToSize(itemText, columnWidth - 5);
                doc.text(wrappedText, xPos, currentY);

                // Add extra space if text wraps to multiple lines
                if (wrappedText.length > 1) {
                  currentY += (wrappedText.length - 1) * 4;
                }
              }
            });
            currentY += 8; // Consistent spacing between bullet points
          }

          currentY += 15; // Space between criteria
        });
      }

      // ===== VOCABULARY ANALYSIS SECTION =====
      if (essay.feedback?.vocabularyAnalysis) {
        // Check if we need a new page
        if (currentY > 230) {
          doc.addPage();
          currentY = 25;
        }

        doc.setFillColor(232, 240, 254);
        doc.rect(10, currentY, 190, 10, 'F');
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(11);
        doc.setTextColor(25, 118, 210);
        doc.text('Vocabulary Analysis', 15, currentY + 7);
        currentY += 20;

        // Lexical Diversity
        if (essay.feedback.vocabularyAnalysis.lexicalDiversity) {
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(10);
          doc.setTextColor(0, 0, 0);
          doc.text('Lexical Diversity', 15, currentY);
          currentY += 10;

          const lexData = essay.feedback.vocabularyAnalysis.lexicalDiversity;
          const lexItems = [
            ['Unique Words', lexData.uniqueWords.toString()],
            ['Total Words', lexData.totalWords.toString()],
            ['Diversity Score', lexData.diversityScore.toFixed(2)]
          ];

          autoTable(doc, {
            body: lexItems,
            startY: currentY,
            theme: 'plain',
            styles: {
              fontSize: 9,
              cellPadding: 2
            },
            columnStyles: {
              0: { cellWidth: 60, fontStyle: 'bold' },
              1: { cellWidth: 100 }
            }
          });

          currentY = (doc as any).lastAutoTable.finalY + 15;
        }

        // Vocabulary Level Distribution
        if (essay.feedback.vocabularyAnalysis.vocabularyLevel) {
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(10);
          doc.setTextColor(0, 0, 0);
          doc.text('Vocabulary Level Distribution', 15, currentY);
          currentY += 10;

          const levels = essay.feedback.vocabularyAnalysis.vocabularyLevel.distribution;

          doc.setFont('helvetica', 'normal');
          doc.setFontSize(9);
          doc.text(`Basic: ${levels.basic}`, 20, currentY);
          doc.text(`Intermediate: ${levels.intermediate}`, 20, currentY + 6);
          doc.text(`Advanced: ${levels.advanced}`, 20, currentY + 12);
          currentY += 20;

          // Sample words by level
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(9);
          doc.text('Sample words by level:', 15, currentY);
          currentY += 8;

          const vocabLevels = [
            { title: 'Basic', items: essay.feedback.vocabularyAnalysis.vocabularyLevel.basic.slice(0, 5) },
            { title: 'Intermediate', items: essay.feedback.vocabularyAnalysis.vocabularyLevel.intermediate.slice(0, 5) },
            { title: 'Advanced', items: essay.feedback.vocabularyAnalysis.vocabularyLevel.advanced.slice(0, 5) }
          ];

          // Calculate level column width
          const levelColumnWidth = 170 / vocabLevels.length;

          // Draw level titles
          vocabLevels.forEach((level, levelIndex) => {
            const xPos = 20 + (levelIndex * levelColumnWidth);
            doc.setFont('helvetica', 'bold');
            doc.setFontSize(8);
            doc.text(level.title, xPos, currentY);
          });
          currentY += 6;

          // Maximum 5 items per level
          for (let i = 0; i < 5; i++) {
            vocabLevels.forEach((level, levelIndex) => {
              const xPos = 20 + (levelIndex * levelColumnWidth);
              if (level.items && level.items[i]) {
                doc.setFont('helvetica', 'normal');
                doc.setFontSize(8);
                doc.text(`• ${level.items[i]}`, xPos, currentY);
              }
            });
            currentY += 6;
          }

          currentY += 10;
        }

        // Overused Words
        if (essay.feedback.vocabularyAnalysis.overusedWords?.length > 0) {
          // Check if we need a new page
          if (currentY > 200) {
            doc.addPage();
            currentY = 25;
          }

          doc.setFont('helvetica', 'bold');
          doc.setFontSize(10);
          doc.setTextColor(0, 0, 0);
          doc.text('Overused Words & Better Alternatives', 15, currentY);
          currentY += 10;

          const overusedWords = essay.feedback.vocabularyAnalysis.overusedWords;
          const tableBody = overusedWords.slice(0, 8).map((word: any) => [
            word.word,
            word.count.toString(),
            word.alternatives.slice(0, 3).join(', ')
          ]);

          autoTable(doc, {
            head: [['Word', 'Frequency', 'Better Alternatives']],
            body: tableBody,
            startY: currentY,
            styles: {
              fontSize: 9,
              cellPadding: 3,
            },
            headStyles: {
              fillColor: [100, 181, 246],
              textColor: [255, 255, 255],
              fontStyle: 'bold'
            },
            columnStyles: {
              0: { cellWidth: 30 },
              1: { cellWidth: 20, halign: 'center' },
              2: { cellWidth: 'auto' }
            }
          });

          currentY = (doc as any).lastAutoTable.finalY + 15;
        }

        // Collocations if available
        if (essay.feedback.vocabularyAnalysis.collocations?.length > 0) {
          // Check if we need a new page
          if (currentY > 200) {
            doc.addPage();
            currentY = 25;
          }

          doc.setFont('helvetica', 'bold');
          doc.setFontSize(10);
          doc.setTextColor(0, 0, 0);
          doc.text('Collocation Improvements', 15, currentY);
          currentY += 10;

          const collocations = essay.feedback.vocabularyAnalysis.collocations;
          const collocationTable = collocations.slice(0, 6).map((item: any) => [
            item.incorrect,
            item.correct,
            item.context
          ]);

          autoTable(doc, {
            head: [['Incorrect', 'Correct', 'Context']],
            body: collocationTable,
            startY: currentY,
            styles: {
              fontSize: 9,
              cellPadding: 3,
            },
            headStyles: {
              fillColor: [100, 181, 246],
              textColor: [255, 255, 255],
              fontStyle: 'bold'
            }
          });

          currentY = (doc as any).lastAutoTable.finalY + 15;
        }
      }

      // ===== IMPROVEMENTS SECTION =====
      if (essay.feedback && essay.feedback.sentenceImprovements && essay.feedback.sentenceImprovements.length > 0) {
        // Start new page for improvements
        doc.addPage();
        currentY = 25;

        doc.setFillColor(232, 240, 254);
        doc.rect(10, currentY, 190, 10, 'F');
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(11);
        doc.setTextColor(25, 118, 210);
        doc.text('Sentence Improvements', 15, currentY + 7);
        currentY += 20;

        // Improvement suggestions table
        const improvementData = essay.feedback.sentenceImprovements
          .map((improvement: any) => [
            improvement.original,
            improvement.improved,
            improvement.explanation
          ]);

        autoTable(doc, {
          head: [['Original', 'Improved Version', 'Explanation']],
          body: improvementData,
          startY: currentY,
          styles: {
            fontSize: 8,
            cellPadding: 3,
          },
          headStyles: {
            fillColor: [76, 175, 80],
            textColor: [255, 255, 255],
            fontStyle: 'bold'
          },
          columnStyles: {
            0: { cellWidth: 50 },
            1: { cellWidth: 50 },
            2: { cellWidth: 'auto' },
          },
          margin: { left: 15, right: 15 }
        });

        currentY = (doc as any).lastAutoTable.finalY + 15;
      }

      // ===== COMPARATIVE ANALYSIS SECTION =====
      if (essay.feedback?.comparativeAnalysis) {
        // Check if we need a new page
        if (currentY > 200) {
          doc.addPage();
          currentY = 25;
        } else {
          currentY += 10;
        }

        doc.setFillColor(232, 240, 254);
        doc.rect(10, currentY, 190, 10, 'F');
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(11);
        doc.setTextColor(25, 118, 210);
        doc.text('Comparative Analysis', 15, currentY + 7);
        currentY += 20;

        // Structural Differences
        if (essay.feedback.comparativeAnalysis.structuralDifferences?.length > 0) {
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(10);
          doc.setTextColor(0, 0, 0);
          doc.text('Structural Differences', 15, currentY);
          currentY += 10;

          doc.setFont('helvetica', 'normal');
          doc.setFontSize(9);

          essay.feedback.comparativeAnalysis.structuralDifferences.forEach((item: string, index: number) => {
            const itemText = `${index + 1}. ${item}`;
            const itemLines = doc.splitTextToSize(itemText, 175);
            doc.text(itemLines, 20, currentY);
            currentY += itemLines.length * 5 + 5;
          });

          currentY += 5;
        }

        // Argumentation Improvements
        if (essay.feedback.comparativeAnalysis.argumentationImprovements?.length > 0) {
          // Check if we need a new page
          if (currentY > 200) {
            doc.addPage();
            currentY = 25;
          }

          doc.setFont('helvetica', 'bold');
          doc.setFontSize(10);
          doc.setTextColor(0, 0, 0);
          doc.text('Argumentation Improvements', 15, currentY);
          currentY += 10;

          doc.setFont('helvetica', 'normal');
          doc.setFontSize(9);

          essay.feedback.comparativeAnalysis.argumentationImprovements.forEach((item: string, index: number) => {
            const itemText = `${index + 1}. ${item}`;
            const itemLines = doc.splitTextToSize(itemText, 175);
            doc.text(itemLines, 20, currentY);
            currentY += itemLines.length * 5 + 5;
          });
        }
      }

      // Grammar Analysis section (if exists)
      if (essay.feedback?.grammarAnalysis) {
        // Start new page for grammar analysis
        doc.addPage();
        currentY = 25;

        doc.setFillColor(232, 240, 254);
        doc.rect(10, currentY, 190, 10, 'F');
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(11);
        doc.setTextColor(25, 118, 210);
        doc.text('Grammar Analysis', 15, currentY + 7);
        currentY += 20;

        // Error Summary
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(10);
        doc.setTextColor(0, 0, 0);
        doc.text('Error Summary', 15, currentY);
        currentY += 10;

        const summaryItems = [
          ['Total Errors', essay.feedback.grammarAnalysis.totalErrors.toString()],
          ['Error Frequency', essay.feedback.grammarAnalysis.errorFrequency],
          ['Most Frequent Error Type', essay.feedback.grammarAnalysis.mostFrequentErrorType]
        ];

        autoTable(doc, {
          body: summaryItems,
          startY: currentY,
          theme: 'plain',
          styles: {
            fontSize: 9,
            cellPadding: 2
          },
          columnStyles: {
            0: { cellWidth: 60, fontStyle: 'bold' },
            1: { cellWidth: 100 }
          }
        });

        currentY = (doc as any).lastAutoTable.finalY + 15;

        // Grammar Strengths
        if (essay.feedback.grammarAnalysis.grammarStrengths?.length > 0) {
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(10);
          doc.setTextColor(0, 0, 0);
          doc.text('Grammar Strengths', 15, currentY);
          currentY += 10;

          doc.setFont('helvetica', 'normal');
          doc.setFontSize(9);

          essay.feedback.grammarAnalysis.grammarStrengths.forEach((strength: string, index: number) => {
            const strengthText = `• ${strength}`;
            const strengthLines = doc.splitTextToSize(strengthText, 175);
            doc.text(strengthLines, 20, currentY);
            currentY += strengthLines.length * 5 + 5;
          });

          currentY += 5;
        }

        // Errors by type
        const errorTypes = essay.feedback.grammarAnalysis.errorsByType;
        if (errorTypes) {
          Object.keys(errorTypes).forEach((errorType: string, typeIndex: number) => {
            const errors = errorTypes[errorType];
            if (errors && Array.isArray(errors) && errors.length > 0) {
              // Check if we need a new page
              if (currentY > 200) {
                doc.addPage();
                currentY = 25;
              }

              // Format error type name for display (e.g., "prepositionErrors" becomes "Preposition Errors")
              const formattedErrorType = errorType
                .replace(/([A-Z])/g, ' $1') // Add space before capital letters
                .replace(/^./, str => str.toUpperCase()); // Capitalize first letter

              doc.setFont('helvetica', 'bold');
              doc.setFontSize(10);
              doc.setTextColor(0, 0, 0);
              doc.text(formattedErrorType, 15, currentY);
              currentY += 10;

              // Show up to 3 examples of this error type
              const errorExamples = Array.isArray(errors) ? errors.slice(0, 3) : [];
              errorExamples.forEach((error: any, errorIndex: number) => {
                // Box for each error
                doc.setFillColor(250, 250, 250);
                doc.roundedRect(15, currentY, 180, 20, 1, 1, 'F');

                // Original text (with error)
                doc.setFont('helvetica', 'bold');
                doc.setFontSize(8);
                doc.setTextColor(220, 53, 69);
                doc.text('Original:', 20, currentY + 6);

                doc.setFont('helvetica', 'normal');
                doc.text(error.original || "Not specified", 60, currentY + 6);

                // Correction
                doc.setFont('helvetica', 'bold');
                doc.setTextColor(76, 175, 80);
                doc.text('Correction:', 20, currentY + 14);

                doc.setFont('helvetica', 'normal');
                doc.text(error.correction || "Not specified", 60, currentY + 14);

                currentY += 25;
              });

              currentY += 5;
            }
          });
        }
      }
    });

    // Add page numbers
    const pageCount = doc.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(8);
      doc.setTextColor(128, 128, 128);
      doc.text(`Page ${i} of ${pageCount}`, 105, 290, { align: 'center' });
    }

    // Save PDF with a timestamp
    const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    let filename = '';

    if (essayId) {
      const index = essaysToExport.findIndex(e => e.id === essayId);
      filename = `writing-assessment-${index + 1}-${timestamp}.pdf`;
    } else {
      filename = `ielts-writing-assessments-${timestamp}.pdf`;
    }

    doc.save(filename);
    if (!essayId) {
      handleCloseExportMenu();
    }
  };

  const renderCriteriaScore = (score: number) => {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
        <Box sx={{ width: '100%', mr: 1 }}>
          <LinearProgress
            variant="determinate"
            value={(score / 9) * 100}
            color={score >= 7 ? "success" : score >= 5 ? "info" : "warning"}
            sx={{ height: 10, borderRadius: 5 }}
          />
        </Box>
        <Box sx={{ minWidth: 35 }}>
          <Typography variant="body2" color="text.secondary">{score}/9</Typography>
        </Box>
      </Box>
    );
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'pending':
        return <Chip size="small" label="Pending" color="default" />;
      case 'processing':
        return <Chip size="small" label="Processing" color="primary" />;
      case 'completed':
        return <Chip size="small" label="Completed" color="success" />;
      case 'error':
        return <Chip size="small" label="Error" color="error" />;
      default:
        return <Chip size="small" label={status} />;
    }
  };

  const hasCompletedEssays = essays.some(e => e.status === 'completed');

  const renderAddEssayForm = () => {
    return (
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>Add New Essay</Typography>
          <Stack spacing={2}>
            <TextField
              label="Essay Name"
              value={newEssayName}
              onChange={(e) => setNewEssayName(e.target.value)}
              fullWidth
              required
            />

            <TextField
              label="Student ID"
              value={newEssayStudentId}
              onChange={(e) => setNewEssayStudentId(e.target.value)}
              fullWidth
              helperText={globalStudentId ? "Leave empty to use the global student ID" : "Required for database storage"}
            />

            <TextField
              select
              label="Writing Material"
              value={newEssayMaterialId}
              onChange={(e) => setNewEssayMaterialId(e.target.value)}
              fullWidth
              helperText="Select a pre-uploaded writing material or leave empty to use custom task"
            >
              <MenuItem value="">Custom Task</MenuItem>
              {writingMaterials.map((material) => (
                <MenuItem key={material.id} value={material.id}>
                  {material.title} ({material.taskType === 'task1' ? 'Task 1' : material.taskType === 'task2' ? 'Task 2' : 'Both'})
                </MenuItem>
              ))}
            </TextField>

            {/* Task Type Selection */}
            <FormControl fullWidth>
              <InputLabel>Task Type</InputLabel>
              <Select
                value={newEssayTaskType}
                label="Task Type"
                onChange={(e) => setNewEssayTaskType(e.target.value as 'Task 1' | 'Task 2')}
              >
                <MenuItem value="Task 1">Task 1 (Chart/Graph/Diagram Description - Requires Image)</MenuItem>
                <MenuItem value="Task 2">Task 2 (Essay)</MenuItem>
              </Select>
              <FormHelperText>
                {newEssayTaskType === 'Task 1' ?
                  'Task 1 requires describing visual information like charts, graphs, or diagrams' :
                  'Task 2 requires writing an essay on a given topic'}
              </FormHelperText>
            </FormControl>

            {/* Task Image Upload (for Task 1) or Task Question (for Task 2) */}
            {newEssayTaskType === 'Task 1' ? (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Task Image (Required for Task 1)
                </Typography>
                <Stack direction="row" spacing={2} alignItems="center">
                  <Button
                    variant="contained"
                    onClick={handleOpenTaskImageDialog}
                    startIcon={<UploadSimple />}
                    color={newEssayTaskImageFile ? "primary" : "warning"}
                    sx={{ minHeight: '56px' }}
                  >
                    {newEssayTaskImageFile ? 'Change Task Image' : 'Upload Task Image'}
                  </Button>
                  {newEssayTaskImageFile && (
                    <Typography variant="body2" color="primary">
                      {newEssayTaskImageFile.name}
                    </Typography>
                  )}
                </Stack>
                <FormHelperText>
                  Upload an image of the chart, graph, or diagram that the student needs to describe
                </FormHelperText>
              </Box>
            ) : (
              /* Task Question Field (for Task 2 only) */
              <TextField
                label={globalTaskQuestion ? "Task Question (Optional - will use global if empty)" : "Task Question"}
                value={newEssayTaskQuestion}
                onChange={(e) => setNewEssayTaskQuestion(e.target.value)}
                multiline
                rows={2}
                fullWidth
                helperText={globalTaskQuestion ? "Leave empty to use the global task question" : "Required for Task 2"}
                required={!globalTaskQuestion}
              />
            )}

            {/* Hidden field to store task question for Task 1 */}
            {newEssayTaskType === 'Task 1' && (
              <input
                type="hidden"
                value="Describe the information shown in the image. Summarize the main features and make comparisons where relevant."
              />
            )}

            <TextField
              label="Essay Text"
              value={newEssayText}
              onChange={(e) => setNewEssayText(e.target.value)}
              multiline
              rows={6}
              fullWidth
            />
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
              <Button onClick={() => setShowAddForm(false)}>Cancel</Button>
              <Button
                variant="contained"
                onClick={handleAddEssay}
                disabled={!newEssayName.trim() || !newEssayText.trim() ||
                  (newEssayTaskType === 'Task 1' ?
                    (!newEssayTaskQuestion.trim() && !globalTaskQuestion.trim() && !newEssayTaskImageFile) :
                    (!newEssayTaskQuestion.trim() && !globalTaskQuestion.trim()))}
              >
                Add Essay
              </Button>
            </Box>
          </Stack>
        </CardContent>
      </Card>
    );
  };

  // Render the task image upload dialog
  const renderTaskImageDialog = () => {
    return (
      <Dialog open={showTaskImageDialog} onClose={handleCloseTaskImageDialog}>
        <DialogTitle>Upload Task Image</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Upload an image of the task chart, graph, or diagram for Task 1.
              Supported formats: JPG, PNG, BMP, TIFF
            </Typography>
            <Button
              variant="outlined"
              component="label"
              startIcon={<UploadSimple />}
              sx={{ mt: 2 }}
              fullWidth
            >
              Select Image File
              <input
                type="file"
                hidden
                accept="image/png, image/jpeg, image/bmp, image/tiff"
                onChange={handleTaskImageFileChange}
              />
            </Button>
            {newEssayTaskImageFile && (
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  Selected: {newEssayTaskImageFile.name}
                </Typography>
              </Box>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseTaskImageDialog}>Cancel</Button>
          <Button
            onClick={handleCloseTaskImageDialog}
            variant="contained"
            disabled={!newEssayTaskImageFile}
          >
            Use Image
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Helper function for tabs
  function a11yProps(index: number) {
    return {
      id: `essay-tab-${index}`,
      'aria-controls': `essay-tabpanel-${index}`,
    };
  }

  // TabPanel component
  interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
  }

  function TabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`essay-tabpanel-${index}`}
        aria-labelledby={`essay-tab-${index}`}
        {...other}
      >
        {value === index && (
          <Box sx={{ pt: 3 }}>
            {children}
          </Box>
        )}
      </div>
    );
  }

  // New component for file processing progress dialog
  const renderFileProcessingDialog = () => {
    const processingFiles = Object.keys(fileProcessingProgress).filter(
      filename => fileProcessingProgress[filename] < 100
    );

    const isProcessing = processingFiles.length > 0;

    return (
      <Dialog
        open={isProcessing}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Processing Files</DialogTitle>
        <DialogContent>
          <Stack spacing={2} sx={{ mt: 1 }}>
            {processingFiles.map(filename => (
              <Box key={filename} sx={{ width: '100%' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">{filename}</Typography>
                  <Typography variant="body2">{fileProcessingProgress[filename]}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={fileProcessingProgress[filename]}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
            ))}
            <Typography variant="body2" color="text.secondary" align="center">
              {processingFiles.length > 1
                ? `Processing ${processingFiles.length} files...`
                : 'Extracting text from file...'}
            </Typography>
          </Stack>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <Box>
      <Stack spacing={3}>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={8}>
            <Typography variant="h4">Bulk Writing Checker</Typography>
            <Typography color="text.secondary" sx={{ mt: 1 }}>
              Check multiple IELTS essays at once for comprehensive feedback
            </Typography>
          </Grid>
          <Grid item xs={12} md={4} sx={{ textAlign: 'right' }}>
            <Button
              variant="outlined"
              startIcon={<ChartLineUp />}
              size="small"
              disabled
            >
              View Stats
            </Button>
          </Grid>
        </Grid>

        {error && <Alert severity="error" onClose={() => setError(null)}>{error}</Alert>}

        <Card>
          <CardContent>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
                  <Button
                    variant="contained"
                    startIcon={<Plus />}
                    onClick={() => setShowAddForm(true)}
                  >
                    Add Essay
                  </Button>
                  <Button
                    component="label"
                    variant="outlined"
                    startIcon={<UploadSimple />}
                  >
                    Upload Files
                    <input
                      type="file"
                      hidden
                      multiple
                      accept=".txt,.docx,.md,.rtf,.pdf,.jpg,.jpeg,.png,.bmp,.tiff"
                      onChange={handleFileUpload}
                    />
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Download />}
                    onClick={handleOpenExportMenu}
                    disabled={!hasCompletedEssays}
                    aria-controls="export-menu"
                    aria-haspopup="true"
                  >
                    Export Results
                  </Button>
                  <Menu
                    id="export-menu"
                    anchorEl={exportMenuAnchorEl}
                    open={Boolean(exportMenuAnchorEl)}
                    onClose={handleCloseExportMenu}
                  >
                    <MenuItem onClick={() => exportAsPdf()}>
                      <ListItemIcon>
                        <FilePdf />
                      </ListItemIcon>
                      <ListItemText>Export as PDF</ListItemText>
                    </MenuItem>
                    <MenuItem onClick={exportAsJson}>
                      <ListItemIcon>
                        <FileText />
                      </ListItemIcon>
                      <ListItemText>Export as JSON</ListItemText>
                    </MenuItem>
                  </Menu>
                  <Box sx={{ flexGrow: 1 }} />
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<ArrowsClockwise />}
                    disabled={loading || essays.length === 0 || !essays.some(e => e.status === 'pending')}
                    onClick={handleBulkCheck}
                  >
                    {loading ? (
                      <>
                        <CircularProgress size={24} color="inherit" sx={{ mr: 1 }} />
                        Processing...
                      </>
                    ) : 'Check Essays'}
                  </Button>
                </Stack>
              </Grid>

              {/* Global Task Question Section */}
              <Grid item xs={12}>
                <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Global Task Settings
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Set common task settings for all essays. These will be applied to any essay without specific settings.
                  </Typography>

                  <Grid container spacing={2} alignItems="flex-start">
                    {/* Student ID */}
                    <Grid item xs={12} md={3}>
                      <TextField
                        fullWidth
                        label="Global Student ID"
                        placeholder="Enter student ID for all essays..."
                        value={globalStudentId}
                        onChange={(e) => setGlobalStudentId(e.target.value)}
                        helperText="Will be applied to all essays without a specific student ID"
                      />
                    </Grid>

                    {/* Task Type Selection */}
                    <Grid item xs={12} md={3}>
                      <FormControl fullWidth>
                        <InputLabel>Task Type</InputLabel>
                        <Select
                          value={globalTaskType}
                          label="Task Type"
                          onChange={(e) => setGlobalTaskType(e.target.value as 'Task 1' | 'Task 2')}
                        >
                          <MenuItem value="Task 1">Task 1 (Chart/Graph Description - Requires Image)</MenuItem>
                          <MenuItem value="Task 2">Task 2 (Essay)</MenuItem>
                        </Select>
                        <FormHelperText>
                          For Task 1, you will need to upload images for each essay
                        </FormHelperText>
                      </FormControl>
                    </Grid>

                    {/* Task Question (only for Task 2) */}
                    {globalTaskType === 'Task 2' && (
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Task Question"
                          placeholder="Enter the IELTS task question that applies to all essays..."
                          value={globalTaskQuestion}
                          onChange={(e) => setGlobalTaskQuestion(e.target.value)}
                          multiline
                          rows={2}
                        />
                      </Grid>
                    )}

                    {/* Apply Button (only for Task 2) */}
                    {globalTaskType === 'Task 2' && (
                      <Grid item xs={12} md={3}>
                        <Button
                          variant="contained"
                          fullWidth
                          onClick={applyGlobalTaskSettings}
                          disabled={!globalTaskQuestion.trim()}
                          sx={{ height: '56px' }}
                        >
                          Apply to All Essays
                        </Button>
                      </Grid>
                    )}

                    {/* Task 1 Upload Button and Apply Button */}
                    {globalTaskType === 'Task 1' && (
                      <>
                        <Grid item xs={12} md={6}>
                          <Stack direction="row" spacing={2} alignItems="center" sx={{ width: '100%' }}>
                            <Button
                              variant="outlined"
                              fullWidth
                              startIcon={<UploadSimple />}
                              sx={{ height: '56px' }}
                              component="label"
                              color={globalTaskImageFile ? "primary" : "inherit"}
                            >
                              {globalTaskImageFile ? 'Change Image' : 'Upload Image'}
                              <input
                                type="file"
                                hidden
                                accept="image/png, image/jpeg, image/bmp, image/tiff"
                                onChange={handleGlobalTaskImageUpload}
                              />
                            </Button>
                            {globalTaskImageFile && (
                              <Typography variant="body2" color="primary" noWrap sx={{ maxWidth: '200px' }}>
                                {globalTaskImageFile.name}
                              </Typography>
                            )}
                          </Stack>
                          {globalTaskImageUrl && (
                            <Box sx={{ mt: 2, textAlign: 'center', maxHeight: '150px', overflow: 'hidden' }}>
                              <img
                                src={globalTaskImageUrl}
                                alt="Task Image"
                                style={{ maxWidth: '100%', maxHeight: '150px', objectFit: 'contain' }}
                              />
                            </Box>
                          )}
                        </Grid>

                        <Grid item xs={12} md={3}>
                          <Button
                            variant="contained"
                            fullWidth
                            onClick={applyGlobalTaskSettings}
                            disabled={!globalTaskImageFile}
                            sx={{ height: '56px' }}
                          >
                            Apply to All Essays
                          </Button>
                        </Grid>
                      </>
                    )}
                  </Grid>
                </Paper>
              </Grid>

              {showAddForm && renderAddEssayForm()}

              <Grid item xs={12} md={5}>
                <Typography variant="h6" sx={{ mb: 2 }}>Essays ({essays.length})</Typography>
                {essays.length === 0 ? (
                  <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                    <Typography color="text.secondary">
                      No essays added yet. Add essays or upload files to get started.
                    </Typography>
                  </Paper>
                ) : (
                  <>
                    <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: 500, mb: 3 }}>
                      <Table stickyHeader size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Name</TableCell>
                            <TableCell>Student ID</TableCell>
                            <TableCell>Type</TableCell>
                            <TableCell>Task Question</TableCell>
                            <TableCell>Status</TableCell>
                            <TableCell align="right">Score</TableCell>
                            <TableCell align="right">Actions</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {essays.map((essay) => (
                            <TableRow
                              key={essay.id}
                              hover
                              selected={selectedEssay?.id === essay.id}
                              onClick={() => handleSelectEssay(essay)}
                              sx={{ cursor: 'pointer' }}
                            >
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <Article style={{ marginRight: 8 }} />
                                  <Typography noWrap sx={{ maxWidth: 150 }}>
                                    {essay.name}
                                  </Typography>
                                </Box>
                              </TableCell>
                              <TableCell>
                                <Typography noWrap sx={{ maxWidth: 150 }}>
                                  {essay.studentId || (globalStudentId ?
                                    <Box component="span" sx={{ fontStyle: 'italic', color: 'text.secondary' }}>
                                      {globalStudentId}
                                    </Box> :
                                    <Box component="span" sx={{ fontStyle: 'italic', color: 'text.secondary' }}>
                                      Anonymous
                                    </Box>
                                  )}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Chip
                                  size="small"
                                  label={essay.taskType || 'Task 2'}
                                  color={essay.taskType === 'Task 1' ? 'primary' : 'secondary'}
                                  variant="outlined"
                                />
                                {essay.taskImageUrl && (
                                  <Chip
                                    size="small"
                                    label="Has Image"
                                    color="info"
                                    variant="outlined"
                                    sx={{ ml: 0.5 }}
                                  />
                                )}
                                {essay.materialId && (
                                  <Chip
                                    size="small"
                                    label="From Material"
                                    color="success"
                                    variant="outlined"
                                    sx={{ ml: 0.5 }}
                                  />
                                )}
                              </TableCell>
                              <TableCell>
                                {essay.taskType === 'Task 1' ? (
                                  essay.status === 'pending' ? (
                                    <Button
                                      variant="outlined"
                                      size="small"
                                      startIcon={<UploadSimple />}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        // Select the essay and open the task image dialog
                                        handleSelectEssay(essay);
                                        handleOpenTaskImageDialog();
                                      }}
                                      color={essay.taskImageUrl ? "primary" : "warning"}
                                    >
                                      {essay.taskImageUrl ? 'Change Image' : 'Upload Image'}
                                    </Button>
                                  ) : (
                                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                      {essay.taskImageUrl ? 'Image Uploaded' : 'No Image (Required)'}
                                    </Typography>
                                  )
                                ) : (
                                  essay.status === 'pending' ? (
                                    <TextField
                                      size="small"
                                      placeholder={globalTaskQuestion ? "Using global task question" : "Enter task question..."}
                                      value={essay.taskQuestion}
                                      onChange={(e) => {
                                        e.stopPropagation(); // Prevent row selection when typing
                                        handleUpdateTaskQuestion(essay.id, e.target.value);
                                      }}
                                      onClick={(e) => e.stopPropagation()} // Prevent row selection when clicking input
                                      fullWidth
                                    />
                                  ) : (
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        maxWidth: '200px',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'nowrap'
                                      }}
                                    >
                                      {essay.taskQuestion || (globalTaskQuestion &&
                                        <Box component="span" sx={{ fontStyle: 'italic', color: 'text.secondary' }}>
                                          Using global task question
                                        </Box>
                                      )}
                                    </Typography>
                                  )
                                )}
                              </TableCell>
                              <TableCell>{getStatusChip(essay.status)}</TableCell>
                              <TableCell align="right">
                                {essay.status === 'completed' && essay.feedback ? (
                                  <Typography fontWeight="bold">{essay.feedback.overallScore}</Typography>
                                ) : '-'}
                              </TableCell>
                              <TableCell align="right">
                                <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                                  {essay.status === 'completed' && (
                                    <Button
                                      size="small"
                                      color="primary"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        exportAsPdf(essay.id);
                                      }}
                                      sx={{ mr: 1 }}
                                    >
                                      <FilePdf size={18} />
                                    </Button>
                                  )}
                                  <Button
                                    size="small"
                                    color="error"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleRemoveEssay(essay.id);
                                    }}
                                  >
                                    <Trash size={18} />
                                  </Button>
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>

                    {/* Summary Table with Criteria Scores */}
                    {essays.some(essay => essay.status === 'completed') && (
                      <Paper variant="outlined" sx={{ mb: 2 }}>
                        <Typography variant="subtitle1" sx={{ p: 2, pb: 0 }}>Detailed Scores Summary</Typography>
                        <TableContainer>
                          <Table size="small">
                            <TableHead>
                              <TableRow>
                                <TableCell>Essay Name</TableCell>
                                <TableCell align="center">Overall Score</TableCell>
                                <TableCell align="center">Task Achievement</TableCell>
                                <TableCell align="center">Coherence</TableCell>
                                <TableCell align="center">Lexical Resource</TableCell>
                                <TableCell align="center">Grammar</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {essays.filter(essay => essay.status === 'completed').map((essay) => (
                                <TableRow
                                  key={`summary-${essay.id}`}
                                  hover
                                  selected={selectedEssay?.id === essay.id}
                                  onClick={() => handleSelectEssay(essay)}
                                  sx={{ cursor: 'pointer' }}
                                >
                                  <TableCell>{essay.name}</TableCell>
                                  <TableCell align="center">{essay.feedback?.overallScore.toFixed(1) || '-'}</TableCell>
                                  <TableCell align="center">
                                    {essay.feedback?.criteria.find(c => c.name.includes('Task'))?.score || '-'}
                                  </TableCell>
                                  <TableCell align="center">
                                    {essay.feedback?.criteria.find(c => c.name.includes('Coherence'))?.score || '-'}
                                  </TableCell>
                                  <TableCell align="center">
                                    {essay.feedback?.criteria.find(c => c.name.includes('Lexical'))?.score || '-'}
                                  </TableCell>
                                  <TableCell align="center">
                                    {(() => {
                                      // Find the grammar criteria
                                      const grammarCriteria = essay.feedback?.criteria.find(c =>
                                        c.name.includes('Grammar') ||
                                        c.name.includes('Grammatical') ||
                                        c.name.includes('Range and Accuracy') ||
                                        c.name.includes('Accuracy')
                                      );

                                      // If found, return the score
                                      if (grammarCriteria) {
                                        return grammarCriteria.score;
                                      }

                                      // If not found, log all criteria names for debugging
                                      console.log(`Grammar criteria not found for ${essay.name}. Available criteria:`,
                                        essay.feedback?.criteria.map(c => c.name)
                                      );

                                      return '-';
                                    })()}
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </Paper>
                    )}
                  </>
                )}
              </Grid>

              <Grid item xs={12} md={7}>
                {selectedEssay ? (
                  <Box>
                    <Typography variant="h6">{selectedEssay.name}</Typography>
                    <Divider sx={{ my: 2 }} />

                    {selectedEssay.status === 'error' ? (
                      <Alert severity="error" sx={{ mb: 2 }}>
                        {selectedEssay.error || 'An error occurred processing this essay'}
                      </Alert>
                    ) : selectedEssay.status === 'processing' ? (
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', flexDirection: 'column', py: 6 }}>
                        <CircularProgress size={48} sx={{ mb: 2 }} />
                        <Typography>Processing essay...</Typography>
                      </Box>
                    ) : selectedEssay.status === 'pending' ? (
                      <>
                        {/* Task Type and Image */}
                        <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                          <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 2 }}>
                            <Typography variant="subtitle2">Task Type:</Typography>
                            <Chip
                              label={selectedEssay.taskType || 'Task 2'}
                              color={selectedEssay.taskType === 'Task 1' ? 'primary' : 'secondary'}
                            />
                          </Stack>

                          {selectedEssay.taskType === 'Task 1' ? (
                            selectedEssay.taskImageUrl ? (
                              <>
                                <Typography variant="subtitle2" gutterBottom>Task Image:</Typography>
                                <Box
                                  component="img"
                                  src={selectedEssay.taskImageUrl}
                                  alt="Task Image"
                                  sx={{
                                    maxWidth: '100%',
                                    maxHeight: '300px',
                                    display: 'block',
                                    mb: 2,
                                    border: '1px solid #eee'
                                  }}
                                />
                                <Typography variant="body2" color="text.secondary" paragraph>
                                  Standard Task 1 instruction: Describe the information shown in the image. Summarize the main features and make comparisons where relevant.
                                </Typography>
                              </>
                            ) : (
                              <Alert severity="warning" sx={{ mb: 2 }}>
                                This Task 1 essay does not have an image. Task 1 essays require an image of the chart, graph, or diagram.
                              </Alert>
                            )
                          ) : (
                            <>
                              <Typography variant="subtitle2" gutterBottom>Task Question:</Typography>
                              <Typography variant="body2" paragraph>
                                {selectedEssay.taskQuestion || 'No task question provided'}
                              </Typography>
                            </>
                          )}
                        </Paper>

                        {/* Essay Text */}
                        <Paper variant="outlined" sx={{ p: 2 }}>
                          <Typography variant="subtitle2" gutterBottom>Essay Text:</Typography>
                          <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                            {selectedEssay.text}
                          </Typography>
                        </Paper>
                      </>
                    ) : selectedEssay.status === 'completed' && selectedEssay.feedback ? (
                      <Box>
                        <Grid container spacing={2}>
                          <Grid item xs={12}>
                            <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                <Typography variant="h4" sx={{ mr: 2 }}>
                                  {selectedEssay.feedback.overallScore.toFixed(1)}
                                </Typography>
                                <Typography variant="subtitle1" color="text.secondary">
                                  Overall Band Score
                                </Typography>
                              </Box>
                              <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
                                {selectedEssay.feedback.comparativeAnalysis.bandExample}
                              </Typography>
                            </Paper>
                          </Grid>

                          <Grid item xs={12}>
                            <Box sx={{ width: '100%' }}>
                              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                                <Tabs
                                  value={tabValue}
                                  onChange={handleTabChange}
                                  aria-label="essay feedback tabs"
                                  variant="scrollable"
                                  scrollButtons="auto"
                                >
                                  <Tab label="Criteria Breakdown" {...a11yProps(0)} />
                                  <Tab label="Vocabulary Analysis" {...a11yProps(1)} />
                                  <Tab label="Improvements" {...a11yProps(2)} />
                                  <Tab label="Comparative Analysis" {...a11yProps(3)} />
                                </Tabs>
                              </Box>

                              {/* Criteria Breakdown Tab */}
                              <TabPanel value={tabValue} index={0}>
                                <Stack spacing={2}>
                                  <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                                    <Typography variant="h6" gutterBottom>IELTS Assessment Summary</Typography>
                                    <Grid container spacing={2}>
                                      {selectedEssay.feedback.criteria.map((criterion, index) => (
                                        <Grid item xs={12} sm={6} md={3} key={`summary-${index}`}>
                                          <Box sx={{ textAlign: 'center', p: 1 }}>
                                            <Typography variant="subtitle2" gutterBottom>{criterion.name}</Typography>
                                            <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                                              <CircularProgress
                                                variant="determinate"
                                                value={(criterion.score / 9) * 100}
                                                size={80}
                                                thickness={4}
                                                sx={{
                                                  color: criterion.score >= 7 ? 'success.main' :
                                                        criterion.score >= 5 ? 'primary.main' : 'warning.main',
                                                }}
                                              />
                                              <Box
                                                sx={{
                                                  top: 0,
                                                  left: 0,
                                                  bottom: 0,
                                                  right: 0,
                                                  position: 'absolute',
                                                  display: 'flex',
                                                  alignItems: 'center',
                                                  justifyContent: 'center',
                                                }}
                                              >
                                                <Typography variant="h5" component="div">
                                                  {criterion.score}
                                                </Typography>
                                              </Box>
                                            </Box>
                                            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                                              {criterion.score >= 7 ? 'Excellent' :
                                               criterion.score >= 6 ? 'Good' :
                                               criterion.score >= 5 ? 'Adequate' :
                                               criterion.score >= 4 ? 'Limited' : 'Poor'}
                                            </Typography>
                                          </Box>
                                        </Grid>
                                      ))}
                                    </Grid>
                                  </Paper>

                                  {selectedEssay.feedback.criteria.map((criterion, index) => (
                                    <Accordion key={index} sx={{ mb: 1 }}>
                                      <AccordionSummary expandIcon={<CaretDown />}>
                                        <Box sx={{ flexGrow: 1 }}>
                                          <Typography fontWeight="medium">{criterion.name}</Typography>
                                          {renderCriteriaScore(criterion.score)}
                                        </Box>
                                      </AccordionSummary>
                                      <AccordionDetails>
                                        <Box sx={{ mb: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                                          <Typography variant="body2" paragraph>{criterion.feedback}</Typography>
                                        </Box>

                                        <Grid container spacing={2}>
                                          <Grid item xs={12} md={4}>
                                            <Paper variant="outlined" sx={{ p: 2, height: '100%', bgcolor: 'success.lightest' }}>
                                              <Typography variant="subtitle2" gutterBottom sx={{ color: 'success.main', display: 'flex', alignItems: 'center' }}>
                                                <Box component="span" sx={{ display: 'inline-flex', mr: 1 }}>✓</Box> Strengths
                                              </Typography>
                                              <Box component="ul" sx={{ pl: 2, mt: 1 }}>
                                                {criterion.strengths.map((item, idx) => (
                                                  <Typography component="li" key={idx} variant="body2" sx={{ mb: 1 }}>{item}</Typography>
                                                ))}
                                              </Box>
                                            </Paper>
                                          </Grid>

                                          <Grid item xs={12} md={4}>
                                            <Paper variant="outlined" sx={{ p: 2, height: '100%', bgcolor: 'error.lightest' }}>
                                              <Typography variant="subtitle2" gutterBottom sx={{ color: 'error.main', display: 'flex', alignItems: 'center' }}>
                                                <Box component="span" sx={{ display: 'inline-flex', mr: 1 }}>✗</Box> Areas to Improve
                                              </Typography>
                                              <Box component="ul" sx={{ pl: 2, mt: 1 }}>
                                                {criterion.weaknesses.map((item, idx) => (
                                                  <Typography component="li" key={idx} variant="body2" sx={{ mb: 1 }}>{item}</Typography>
                                                ))}
                                              </Box>
                                            </Paper>
                                          </Grid>

                                          <Grid item xs={12} md={4}>
                                            <Paper variant="outlined" sx={{ p: 2, height: '100%', bgcolor: 'info.lightest' }}>
                                              <Typography variant="subtitle2" gutterBottom sx={{ color: 'info.main', display: 'flex', alignItems: 'center' }}>
                                                <Lightbulb size={16} style={{ marginRight: '4px' }} /> Recommendations
                                              </Typography>
                                              <Box component="ul" sx={{ pl: 2, mt: 1 }}>
                                                {criterion.improvements.map((item, idx) => (
                                                  <Typography component="li" key={idx} variant="body2" sx={{ mb: 1 }}>{item}</Typography>
                                                ))}
                                              </Box>
                                            </Paper>
                                          </Grid>
                                        </Grid>

                                        <Box sx={{ mt: 2, pt: 2, borderTop: '1px dashed', borderColor: 'divider' }}>
                                          <Typography variant="subtitle2" gutterBottom>Band Score Explanation:</Typography>
                                          <Typography variant="body2">
                                            {criterion.score >= 7 ?
                                              `Band ${criterion.score}: Your performance in this area is strong. You demonstrate a high level of competence with only occasional inaccuracies or inadequacies.` :
                                            criterion.score >= 6 ?
                                              `Band ${criterion.score}: Your performance in this area is generally effective with some inconsistencies and inaccuracies.` :
                                            criterion.score >= 5 ?
                                              `Band ${criterion.score}: Your performance in this area is adequate but with noticeable limitations.` :
                                            `Band ${criterion.score}: Your performance in this area needs significant improvement. Focus on the recommendations provided.`}
                                          </Typography>
                                        </Box>
                                      </AccordionDetails>
                                    </Accordion>
                                  ))}
                                </Stack>
                              </TabPanel>

                              {/* Vocabulary Analysis Tab */}
                              <TabPanel value={tabValue} index={1}>
                                <Stack spacing={3}>
                                  {/* Lexical Diversity Stats */}
                                  <Paper variant="outlined" sx={{ p: 2 }}>
                                    <Typography variant="subtitle1" gutterBottom>Lexical Diversity</Typography>
                                    <Grid container spacing={2}>
                                      <Grid item xs={4}>
                                        <Typography variant="body2" color="text.secondary">Unique Words</Typography>
                                        <Typography variant="h6">{selectedEssay.feedback.vocabularyAnalysis.lexicalDiversity.uniqueWords}</Typography>
                                      </Grid>
                                      <Grid item xs={4}>
                                        <Typography variant="body2" color="text.secondary">Total Words</Typography>
                                        <Typography variant="h6">{selectedEssay.feedback.vocabularyAnalysis.lexicalDiversity.totalWords}</Typography>
                                      </Grid>
                                      <Grid item xs={4}>
                                        <Typography variant="body2" color="text.secondary">Diversity Score</Typography>
                                        <Typography variant="h6">{selectedEssay.feedback.vocabularyAnalysis.lexicalDiversity.diversityScore.toFixed(2)}</Typography>
                                      </Grid>
                                    </Grid>
                                  </Paper>

                                  {/* Vocabulary Level Distribution */}
                                  {selectedEssay.feedback.vocabularyAnalysis.vocabularyLevel && (
                                    <Paper variant="outlined" sx={{ p: 2 }}>
                                      <Typography variant="subtitle1" gutterBottom>Vocabulary Level Distribution</Typography>
                                      <Grid container spacing={2}>
                                        <Grid item xs={12} md={6}>
                                          <Typography variant="body2" color="text.secondary">Basic: {selectedEssay.feedback.vocabularyAnalysis.vocabularyLevel.distribution.basic}</Typography>
                                          <Typography variant="body2" color="text.secondary">Intermediate: {selectedEssay.feedback.vocabularyAnalysis.vocabularyLevel.distribution.intermediate}</Typography>
                                          <Typography variant="body2" color="text.secondary">Advanced: {selectedEssay.feedback.vocabularyAnalysis.vocabularyLevel.distribution.advanced}</Typography>
                                        </Grid>
                                        <Grid item xs={12} md={6}>
                                          <Box sx={{ height: 100 }}>
                                            {/* You could add a chart here if desired */}
                                          </Box>
                                        </Grid>
                                      </Grid>

                                      <Box sx={{ mt: 2 }}>
                                        <Typography variant="body2" gutterBottom>Sample words by level:</Typography>
                                        <Grid container spacing={2}>
                                          <Grid item xs={12} md={4}>
                                            <Typography variant="caption" color="text.secondary">Basic</Typography>
                                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                              {selectedEssay.feedback.vocabularyAnalysis.vocabularyLevel.basic.slice(0, 5).map((word, i) => (
                                                <Chip key={i} label={word} size="small" />
                                              ))}
                                            </Box>
                                          </Grid>
                                          <Grid item xs={12} md={4}>
                                            <Typography variant="caption" color="text.secondary">Intermediate</Typography>
                                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                              {selectedEssay.feedback.vocabularyAnalysis.vocabularyLevel.intermediate.slice(0, 5).map((word, i) => (
                                                <Chip key={i} label={word} size="small" />
                                              ))}
                                            </Box>
                                          </Grid>
                                          <Grid item xs={12} md={4}>
                                            <Typography variant="caption" color="text.secondary">Advanced</Typography>
                                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                              {selectedEssay.feedback.vocabularyAnalysis.vocabularyLevel.advanced.slice(0, 5).map((word, i) => (
                                                <Chip key={i} label={word} size="small" />
                                              ))}
                                            </Box>
                                          </Grid>
                                        </Grid>
                                      </Box>
                                    </Paper>
                                  )}

                                  {/* Topic-Relevant Vocabulary */}
                                  {selectedEssay.feedback.vocabularyAnalysis.topicRelevantVocabulary && (
                                    <Paper variant="outlined" sx={{ p: 2 }}>
                                      <Typography variant="subtitle1" gutterBottom>Topic-Relevant Vocabulary</Typography>
                                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                        {selectedEssay.feedback.vocabularyAnalysis.topicRelevantVocabulary.map((word, i) => (
                                          <Chip
                                            key={i}
                                            label={word}
                                            color="success"
                                            variant="outlined"
                                          />
                                        ))}
                                      </Box>
                                    </Paper>
                                  )}

                                  {/* Transition Words */}
                                  {selectedEssay.feedback.vocabularyAnalysis.transitionWords && (
                                    <Paper variant="outlined" sx={{ p: 2 }}>
                                      <Typography variant="subtitle1" gutterBottom>Transition Words</Typography>
                                      <Grid container spacing={2}>
                                        <Grid item xs={12} md={6}>
                                          <Typography variant="body2" gutterBottom>Used in essay:</Typography>
                                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                            {selectedEssay.feedback.vocabularyAnalysis.transitionWords.used.map((word, i) => (
                                              <Chip key={i} label={word} size="small" color="primary" variant="outlined" />
                                            ))}
                                          </Box>
                                        </Grid>
                                        <Grid item xs={12} md={6}>
                                          <Typography variant="body2" gutterBottom>Suggested additions:</Typography>
                                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                            {selectedEssay.feedback.vocabularyAnalysis.transitionWords.suggestions.map((word, i) => (
                                              <Chip key={i} label={word} size="small" color="secondary" variant="outlined" />
                                            ))}
                                          </Box>
                                        </Grid>
                                      </Grid>
                                    </Paper>
                                  )}

                                  {/* Idioms and Phrases */}
                                  {selectedEssay.feedback.vocabularyAnalysis.idiomsAndPhrases && (
                                    <Paper variant="outlined" sx={{ p: 2 }}>
                                      <Typography variant="subtitle1" gutterBottom>Idioms and Phrases</Typography>
                                      <Grid container spacing={2}>
                                        <Grid item xs={12} md={6}>
                                          <Typography variant="body2" gutterBottom>Used in essay:</Typography>
                                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                            {selectedEssay.feedback.vocabularyAnalysis.idiomsAndPhrases.used.map((phrase, i) => (
                                              <Chip key={i} label={phrase} size="small" color="primary" />
                                            ))}
                                          </Box>
                                        </Grid>
                                        <Grid item xs={12} md={6}>
                                          <Typography variant="body2" gutterBottom>Suggested additions:</Typography>
                                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                            {selectedEssay.feedback.vocabularyAnalysis.idiomsAndPhrases.suggestions.map((phrase, i) => (
                                              <Chip key={i} label={phrase} size="small" color="secondary" />
                                            ))}
                                          </Box>
                                        </Grid>
                                      </Grid>
                                    </Paper>
                                  )}

                                  {/* Overused Words */}
                                  <Paper variant="outlined" sx={{ p: 2 }}>
                                    <Typography variant="subtitle1" gutterBottom>Overused Words</Typography>
                                    <Grid container spacing={2}>
                                      {selectedEssay.feedback.vocabularyAnalysis.overusedWords.map((word, index) => (
                                        <Grid item xs={12} sm={6} md={4} key={index}>
                                          <Box sx={{ p: 1, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                                            <Typography variant="body1" fontWeight="bold">"{word.word}" ({word.count}x)</Typography>
                                            <Typography variant="body2" color="text.secondary">Try: {word.alternatives.slice(0, 3).join(', ')}</Typography>
                                          </Box>
                                        </Grid>
                                      ))}
                                    </Grid>
                                  </Paper>

                                  {/* Collocation Errors */}
                                  <Paper variant="outlined" sx={{ p: 2 }}>
                                    <Typography variant="subtitle1" gutterBottom>Collocation Corrections</Typography>
                                    <Stack spacing={2}>
                                      {selectedEssay.feedback.vocabularyAnalysis.collocations.map((collocation, index) => (
                                        <Box key={index} sx={{ p: 1, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                                          <Typography variant="body2" sx={{ mb: 1 }}>
                                            <Typography component="span" color="error" sx={{ textDecoration: 'line-through' }}>
                                              {collocation.incorrect}
                                            </Typography>
                                            {' → '}
                                            <Typography component="span" color="success.main" fontWeight="bold">
                                              {collocation.correct}
                                            </Typography>
                                          </Typography>
                                          <Typography variant="body2" color="text.secondary">
                                            Context: "{collocation.context}"
                                          </Typography>
                                        </Box>
                                      ))}
                                    </Stack>
                                  </Paper>

                                  {/* Academic Phrases */}
                                  <Paper variant="outlined" sx={{ p: 2 }}>
                                    <Typography variant="subtitle1" gutterBottom>Recommended Academic Phrases</Typography>
                                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                      {selectedEssay.feedback.vocabularyAnalysis.academicPhrases.slice(0, 3).map((phrase, index) => (
                                        <Chip
                                          key={index}
                                          label={phrase}
                                          color="primary"
                                          variant="outlined"
                                        />
                                      ))}
                                    </Box>
                                  </Paper>
                                </Stack>
                              </TabPanel>

                              {/* Sentence Improvements Tab */}
                              <TabPanel value={tabValue} index={2}>
                                <Typography variant="h6" gutterBottom>Sentence Improvements</Typography>
                                <Stack spacing={2}>
                                  {selectedEssay.feedback.sentenceImprovements.map((improvement, index) => (
                                    <Paper key={index} variant="outlined" sx={{ p: 2 }}>
                                      <Typography variant="subtitle2" color="error" gutterBottom>
                                        Original:
                                      </Typography>
                                      <Typography variant="body2" paragraph sx={{ pl: 2, borderLeft: '2px solid', borderColor: 'error.main' }}>
                                        "{improvement.original}"
                                      </Typography>

                                      <Typography variant="subtitle2" color="success.main" gutterBottom>
                                        Improved:
                                      </Typography>
                                      <Typography variant="body2" paragraph sx={{ pl: 2, borderLeft: '2px solid', borderColor: 'success.main' }}>
                                        "{improvement.improved}"
                                      </Typography>

                                      <Typography variant="subtitle2" gutterBottom>
                                        Why this is better:
                                      </Typography>
                                      <Typography variant="body2" sx={{ pl: 2 }}>
                                        {improvement.explanation}
                                      </Typography>
                                    </Paper>
                                  ))}
                                </Stack>
                              </TabPanel>

                              {/* Comparative Analysis Tab */}
                              <TabPanel value={tabValue} index={3}>
                                <Stack spacing={3}>
                                  <Paper variant="outlined" sx={{ p: 2 }}>
                                    <Typography variant="h6" gutterBottom>Your Band Score Analysis</Typography>
                                    <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1, mb: 2 }}>
                                      <Typography variant="body1" fontWeight="medium" gutterBottom>
                                        Current Score: {selectedEssay.feedback.overallScore.toFixed(1)}/9
                                      </Typography>
                                      <Typography variant="body2">{selectedEssay.feedback.comparativeAnalysis.bandExample}</Typography>
                                    </Box>

                                    <Typography variant="subtitle2" gutterBottom>Score Comparison</Typography>
                                    <Grid container spacing={1} sx={{ mb: 2 }}>
                                      <Grid item xs={4}>
                                        <Paper
                                          variant="outlined"
                                          sx={{
                                            p: 1.5,
                                            textAlign: 'center',
                                            bgcolor: 'warning.lightest',
                                            borderColor: 'warning.main'
                                          }}
                                        >
                                          <Typography variant="subtitle2" color="warning.main">Band 5</Typography>
                                          <Typography variant="caption" display="block">Limited User</Typography>
                                        </Paper>
                                      </Grid>
                                      <Grid item xs={4}>
                                        <Paper
                                          variant="outlined"
                                          sx={{
                                            p: 1.5,
                                            textAlign: 'center',
                                            bgcolor: 'info.lightest',
                                            borderColor: 'info.main'
                                          }}
                                        >
                                          <Typography variant="subtitle2" color="info.main">Band 6-7</Typography>
                                          <Typography variant="caption" display="block">Competent User</Typography>
                                        </Paper>
                                      </Grid>
                                      <Grid item xs={4}>
                                        <Paper
                                          variant="outlined"
                                          sx={{
                                            p: 1.5,
                                            textAlign: 'center',
                                            bgcolor: 'success.lightest',
                                            borderColor: 'success.main'
                                          }}
                                        >
                                          <Typography variant="subtitle2" color="success.main">Band 8-9</Typography>
                                          <Typography variant="caption" display="block">Expert User</Typography>
                                        </Paper>
                                      </Grid>
                                    </Grid>

                                    <Box sx={{ mt: 2 }}>
                                      <LinearProgress
                                        variant="determinate"
                                        value={(selectedEssay.feedback.overallScore / 9) * 100}
                                        sx={{
                                          height: 10,
                                          borderRadius: 5,
                                          mb: 1,
                                          background: 'linear-gradient(90deg, #f44336 0%, #ff9800 40%, #2196f3 70%, #4caf50 100%)'
                                        }}
                                      />
                                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                        <Typography variant="caption">Band 5</Typography>
                                        <Typography variant="caption">Band 6</Typography>
                                        <Typography variant="caption">Band 7</Typography>
                                        <Typography variant="caption">Band 8</Typography>
                                        <Typography variant="caption">Band 9</Typography>
                                      </Box>
                                    </Box>
                                  </Paper>

                                  <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                                    <Typography variant="h6" gutterBottom>Structural Comparison with Higher Band Essays</Typography>
                                    <Box sx={{ mb: 2 }}>
                                      <Typography variant="body2" paragraph>
                                        Below are key structural differences between your essay and those typically found in higher band scores:
                                      </Typography>
                                    </Box>
                                    <Box component="ul" sx={{ pl: 2 }}>
                                      {selectedEssay.feedback.comparativeAnalysis.structuralDifferences.map((item, idx) => (
                                        <Box key={idx} sx={{ mb: 2 }}>
                                          <Typography component="li" variant="body2" sx={{ mb: 1 }}>{item}</Typography>
                                        </Box>
                                      ))}
                                    </Box>
                                  </Paper>

                                  <Paper variant="outlined" sx={{ p: 2 }}>
                                    <Typography variant="h6" gutterBottom>Argumentation Improvements</Typography>
                                    <Grid container spacing={2} sx={{ mb: 2 }}>
                                      <Grid item xs={12} md={6}>
                                        <Box sx={{ p: 2, bgcolor: 'error.lightest', borderRadius: 1 }}>
                                          <Typography variant="subtitle2" gutterBottom sx={{ color: 'error.main' }}>
                                            Current Approach
                                          </Typography>
                                          <Box component="ul" sx={{ pl: 2, m: 0 }}>
                                            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                                              Limited development of ideas
                                            </Typography>
                                            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                                              Insufficient supporting evidence
                                            </Typography>
                                            <Typography component="li" variant="body2">
                                              Weak logical connections between points
                                            </Typography>
                                          </Box>
                                        </Box>
                                      </Grid>
                                      <Grid item xs={12} md={6}>
                                        <Box sx={{ p: 2, bgcolor: 'success.lightest', borderRadius: 1 }}>
                                          <Typography variant="subtitle2" gutterBottom sx={{ color: 'success.main' }}>
                                            Target Approach
                                          </Typography>
                                          <Box component="ul" sx={{ pl: 2, m: 0 }}>
                                            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                                              Fully developed, nuanced arguments
                                            </Typography>
                                            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                                              Relevant examples and evidence
                                            </Typography>
                                            <Typography component="li" variant="body2">
                                              Clear logical progression of ideas
                                            </Typography>
                                          </Box>
                                        </Box>
                                      </Grid>
                                    </Grid>

                                    <Divider sx={{ my: 2 }} />

                                    <Typography variant="subtitle2" gutterBottom>Specific Improvements for Your Essay:</Typography>
                                    <Box component="ul" sx={{ pl: 2 }}>
                                      {selectedEssay.feedback.comparativeAnalysis.argumentationImprovements.map((item, idx) => (
                                        <Box key={idx} sx={{ mb: 1.5 }}>
                                          <Typography component="li" variant="body2" sx={{ display: 'flex', alignItems: 'flex-start' }}>
                                            <Lightbulb size={18} style={{ minWidth: '18px', marginRight: '8px', marginTop: '2px', color: '#2196f3' }} />
                                            <span>{item}</span>
                                          </Typography>
                                        </Box>
                                      ))}
                                    </Box>
                                  </Paper>

                                  <Paper variant="outlined" sx={{ p: 2 }}>
                                    <Typography variant="h6" gutterBottom>Next Steps to Improve Your Band Score</Typography>
                                    <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                                      <Typography variant="body2" paragraph>
                                        Based on your current performance, focus on these key areas to improve your band score:
                                      </Typography>

                                      <Grid container spacing={2}>
                                        {selectedEssay.feedback.criteria
                                          .sort((a, b) => a.score - b.score)
                                          .slice(0, 2)
                                          .map((criterion, index) => (
                                            <Grid item xs={12} key={index}>
                                              <Typography component="li" variant="body2" sx={{ display: 'flex', alignItems: 'flex-start' }}>
                                                <Lightbulb size={18} style={{ minWidth: '18px', marginRight: '8px', marginTop: '2px', color: '#2196f3' }} />
                                                <span>{criterion.name}</span>
                                              </Typography>
                                            </Grid>
                                          ))}
                                      </Grid>
                                    </Box>
                                  </Paper>
                                </Stack>
                              </TabPanel>
                            </Box>
                          </Grid>
                        </Grid>
                      </Box>
                    ) : (
                      <Typography>Select an essay to view details</Typography>
                    )}
                  </Box>
                ) : (
                  <Paper
                    variant="outlined"
                    sx={{
                      p: 4,
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: '100%',
                      minHeight: 400
                    }}
                  >
                    <Article size={48} style={{ opacity: 0.5, marginBottom: 16 }} />
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                      No Essay Selected
                    </Typography>
                    <Typography variant="body2" color="text.secondary" align="center">
                      Select an essay from the list to view its details or add a new essay to get started.
                    </Typography>
                  </Paper>
                )}
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Stack>
      {renderFileProcessingDialog()}
      {renderTaskImageDialog()}
    </Box>
  );
}
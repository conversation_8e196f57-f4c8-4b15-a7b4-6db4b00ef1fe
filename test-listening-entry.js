// Test script to verify our changes to the ListeningEntry model
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testListeningEntry() {
  try {
    console.log('Testing ListeningEntry creation without materialId...');

    // Create a student
    const student = await prisma.student.findFirst();
    if (!student) {
      console.log('No student found, creating one...');
      const newStudent = await prisma.student.create({
        data: {
          id: 'test-student-' + Date.now(),
          name: 'Test Student'
        }
      });
      console.log('Created student:', newStudent);
    }

    // Get the student ID
    const studentId = student ? student.id : 'test-student-' + Date.now();

    // Create a listening entry without materialId
    const listeningEntry = await prisma.listeningEntry.create({
      data: {
        materialTitle: 'Global Listening Test',
        raw_score: '30/40',
        answers: [],
        score: 30,
        band: 7.0,
        correctAnswers: 30,
        totalQuestions: 40,
        strengths: ['Good understanding'],
        weaknesses: ['Needs improvement in section 3'],
        improvementSuggestions: ['Practice more'],
        student: {
          connect: { id: studentId }
        }
      }
    });

    console.log('Successfully created listening entry without materialId:', listeningEntry);

    // Clean up
    await prisma.listeningEntry.delete({
      where: { id: listeningEntry.id }
    });

    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testListeningEntry();

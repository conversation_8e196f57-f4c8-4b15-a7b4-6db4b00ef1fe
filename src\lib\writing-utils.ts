import { prisma } from './db';
import { inflateBandScore } from '@/utils/band-score-inflation';
import { generateStandardizedFeedback } from '@/lib/score-conversion';

// Get API key from environment variables
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

if (!OPENAI_API_KEY) {
  throw new Error('OPENAI_API_KEY environment variable is not set');
}

/**
 * Process a writing entry and generate feedback using the same detailed analysis as bulk-writing-checker
 *
 * @param entry - The writing entry to process
 * @param model - The AI model to use for processing
 * @returns The processed writing entry
 */
export async function processWritingEntry(entry: any, model: string = 'gpt-4o') {
  try {
    // Check if we have an image URL for handwritten essay
    let essayText = entry.essayText;

    // If we have text that was already extracted with Claude 3.5, use that
    if (entry.extractedWithClaude) {
      console.log('Using text already extracted with Claude 3.5');
    }
    // Otherwise, if we have an image URL but no text yet, extract it with OpenAI
    else if (entry.imageUrl && (!essayText || essayText === 'Pending AI analysis' || essayText.trim() === '')) {
      console.log('Processing handwritten essay image(s) with OpenAI');

      // Check if we have multiple images stored as JSON
      let imageUrls: string[] = [];

      if (entry.imageUrl.startsWith('[') && entry.imageUrl.endsWith(']')) {
        try {
          // Parse the JSON array of image URLs
          imageUrls = JSON.parse(entry.imageUrl);
          console.log(`Found ${imageUrls.length} images in JSON array`);
        } catch (jsonError) {
          console.error('Error parsing image URLs JSON:', jsonError);
          // Fallback to treating it as a single URL
          imageUrls = [entry.imageUrl];
        }
      } else {
        // Single image URL
        imageUrls = [entry.imageUrl];
      }

      // Extract text from each image and combine
      let extractedTexts: string[] = [];

      try {
        for (const imageUrl of imageUrls) {
          try {
            // Convert image URL to base64 if it's a data URL
            let imageData;
            if (imageUrl.startsWith('data:')) {
              imageData = imageUrl;
            } else {
              // Fetch the image and convert to base64
              const response = await fetch(imageUrl);
              const blob = await response.blob();
              const buffer = await blob.arrayBuffer();
              const base64 = Buffer.from(buffer).toString('base64');
              const mimeType = blob.type;
              imageData = `data:${mimeType};base64,${base64}`;
            }

            // Call OpenAI API to extract text from the image
            const response = await fetch('https://api.openai.com/v1/chat/completions', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${OPENAI_API_KEY}`
              },
              body: JSON.stringify({
                model: 'gpt-4o',
                messages: [
                  {
                    role: 'system',
                    content: 'You are an expert at extracting text from images of handwritten essays. Extract all text accurately, preserving paragraphs and structure.'
                  },
                  {
                    role: 'user',
                    content: [
                      {
                        type: 'text',
                        text: 'Extract all text from this handwritten essay image. Preserve paragraphs and structure.'
                      },
                      {
                        type: 'image_url',
                        image_url: {
                          url: imageData
                        }
                      }
                    ]
                  }
                ],
                max_tokens: 4000
              })
            });

            if (!response.ok) {
              const errorData = await response.json();
              throw new Error(errorData.error?.message || 'Failed to extract text from image');
            }

            const data = await response.json();
            const extractedText = data.choices[0].message.content;
            extractedTexts.push(extractedText);
            console.log(`Extracted text from image ${extractedTexts.length}/${imageUrls.length} with OpenAI:`, extractedText.substring(0, 100) + '...');
          } catch (singleImageError) {
            console.error(`Error processing image ${imageUrl}:`, singleImageError);
            // Continue with next image
          }
        }

        // Combine all extracted texts
        if (extractedTexts.length > 0) {
          essayText = extractedTexts.join('\n\n');
          console.log('Combined text from all images:', essayText.substring(0, 100) + '...');

          // Update the entry with the extracted text
          await prisma.writingEntry.update({
            where: { id: entry.id },
            data: { essayText }
          });
        }
      } catch (imageProcessingError) {
        console.error('Error extracting text from image with OpenAI:', imageProcessingError);
        // If we can't extract text, use a placeholder
        essayText = "Error extracting text from handwritten essay image. Please try again or provide a typed version.";
      }
    }

    // If we still don't have essay text, we can't proceed
    if (!essayText || essayText.trim() === '') {
      throw new Error('No essay text available for analysis');
    }

    // Determine if this is Task 1 or Task 2
    const isTask1 = entry.taskType === 'task1' || entry.taskType === 'Task 1';
    const taskType = isTask1 ? 'Task 1' : 'Task 2';
    const taskQuestion = entry.taskQuestion || entry.material?.taskQuestion || 'Write an essay on the given topic.';
    const hasTaskImage = isTask1 && entry.imageUrl; // For Task 1, we might have a chart/graph image

    // Call OpenAI API with the same prompt as bulk-writing-checker
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'OpenAI-Beta': 'assistants=v1'
      },
      body: JSON.stringify({
        model,
        messages: [
          {
            role: 'system',
            content: `You are an expert IELTS writing examiner and grammar specialist. Evaluate the following text according to the official IELTS scoring criteria and provide comprehensive feedback in a structured JSON format with the following fields:

Task Question: ${taskQuestion}

Essay Type: ${taskType}

${isTask1 ?
  `This is a Task 1 essay. For Task 1, focus on the following criteria:
  - Task Achievement: How well the student summarizes the main features and makes comparisons where relevant
  - Coherence and Cohesion: Logical organization and use of cohesive devices
  - Lexical Resource: Vocabulary range and accuracy, especially for describing data/visuals
  - Grammatical Range and Accuracy: Sentence structures and grammatical accuracy
  ${hasTaskImage ? "The student was responding to a visual prompt (chart, graph, diagram, or map)." : ""}` :
  `This is a Task 2 essay. For Task 2, focus on the following criteria:
  - Task Response: How well the student addresses all parts of the task with a position throughout
  - Coherence and Cohesion: Logical organization, paragraphing, and use of cohesive devices
  - Lexical Resource: Vocabulary range and accuracy for expressing ideas
  - Grammatical Range and Accuracy: Sentence structures and grammatical accuracy`
}

{
  "overallScore": number,
  "criteria": [
    {
      "name": "Task Achievement/Response",
      "score": number,
      "feedback": "detailed explanation",
      "strengths": ["strength1", "strength2"],
      "weaknesses": ["weakness1", "weakness2"],
      "improvements": ["improvement1", "improvement2"],
      "taskFulfillment": "detailed assessment of how well the essay addresses the specific task question"
    },
    // repeat for other 3 criteria: Coherence and Cohesion, Lexical Resource, Grammatical Range and Accuracy
  ],
  "grammarAnalysis": {
    "errorsByType": {
      "tenseErrors": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation"}],
      "subjectVerbAgreement": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation"}],
      "articles": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation"}],
      "prepositions": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation"}],
      "sentenceStructure": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation"}],
      "fragments": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation"}],
      "punctuation": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation"}],
      "wordOrder": [{"original": "incorrect text", "correction": "corrected text", "explanation": "grammar rule explanation"}]
    },
    "grammarStrengths": ["complex structure 1 used correctly", "complex structure 2 used correctly"],
    "totalErrors": number,
    "errorFrequency": "errors per 100 words",
    "mostFrequentErrorType": "the category with most errors"
  },
  "vocabularyAnalysis": {
    "overusedWords": [{"word": "basic word", "count": number, "alternatives": ["better alternative1", "better alternative2", "better alternative3"]}],
    "collocations": [{"incorrect": "incorrect collocation", "correct": "correct collocation", "context": "usage example"}],
    "academicPhrases": ["phrase1", "phrase2"],
    "lexicalDiversity": {
      "uniqueWords": number,
      "totalWords": number,
      "diversityScore": number
    },
    "vocabularyLevel": {
      "basic": ["word1", "word2"],
      "intermediate": ["word1", "word2"],
      "advanced": ["word1", "word2"],
      "distribution": {
        "basic": "percentage",
        "intermediate": "percentage",
        "advanced": "percentage"
      }
    },
    "topicRelevantVocabulary": ["word1", "word2", "word3"],
    "transitionWords": {
      "used": ["word1", "word2", "word3"],
      "suggestions": ["additional1", "additional2"]
    },
    "idiomsAndPhrases": {
      "used": ["phrase1", "phrase2"],
      "suggestions": ["suggestion1", "suggestion2"]
    }
  },
  "sentenceImprovements": [
    {
      "original": "original problematic sentence",
      "improved": "improved version",
      "explanation": "why this is better"
    },
    {
      "original": "another problematic sentence",
      "improved": "improved version",
      "explanation": "why this is better"
    },
    {
      "original": "third problematic sentence",
      "improved": "improved version",
      "explanation": "why this is better"
    }
  ],
  "structureAnalysis": {
    "introduction": "brief evaluation of introduction",
    "bodyParagraphs": ["evaluation of body paragraph 1", "evaluation of body paragraph 2"],
    "conclusion": "brief evaluation of conclusion",
    "paragraphCohesion": "assessment of how well paragraphs connect"
  },
  "comparativeAnalysis": {
    "structuralDifferences": ["difference1", "difference2"],
    "argumentationImprovements": ["improvement1", "improvement2"],
    "bandExample": "short example of what makes this a band X essay vs band Y"
  }
}

The scores should range from 0-9, where 9 is the highest score. Be thorough but concise in your feedback. Pay special attention to grammar errors and provide detailed explanations.`
          },
          {
            role: 'user',
            content: essayText
          }
        ],
        temperature: 0.7,
        max_tokens: 3000,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || 'Failed to analyze essay');
    }

    const data = await response.json();
    const content = data.choices[0].message.content;

    // Parse the feedback
    let parsedFeedback;
    try {
      parsedFeedback = JSON.parse(content);
    } catch (parseError) {
      console.error('Error parsing feedback:', parseError);
      throw new Error('Failed to parse feedback from AI');
    }

    // Extract the overall band score
    const rawBand = parsedFeedback.overallScore || 0;

    // Apply inflation to the band score based on task type
    const band = inflateBandScore(rawBand, entry.taskType || 'writing_task1') as number;

    // Extract criteria scores in a format compatible with the database
    const criteriaScores = parsedFeedback.criteria || [];

    // Extract strengths, weaknesses, and improvement suggestions
    const strengths: string[] = [];
    const weaknesses: string[] = [];
    const improvementSuggestions: string[] = [];

    // Collect strengths and weaknesses from criteria
    if (Array.isArray(parsedFeedback.criteria)) {
      parsedFeedback.criteria.forEach((criterion: any) => {
        if (Array.isArray(criterion.strengths)) {
          strengths.push(...criterion.strengths);
        }
        if (Array.isArray(criterion.weaknesses)) {
          weaknesses.push(...criterion.weaknesses);
        }
        if (Array.isArray(criterion.improvements)) {
          improvementSuggestions.push(...criterion.improvements);
        }
      });
    }

    // Add grammar strengths
    if (parsedFeedback.grammarAnalysis?.grammarStrengths) {
      strengths.push(...parsedFeedback.grammarAnalysis.grammarStrengths);
    }

    // Add sentence improvements
    if (Array.isArray(parsedFeedback.sentenceImprovements)) {
      parsedFeedback.sentenceImprovements.forEach((improvement: any) => {
        if (improvement.explanation) {
          improvementSuggestions.push(improvement.explanation);
        }
      });
    }

    // Update the entry in the database
    const updatedEntry = await prisma.writingEntry.update({
      where: { id: entry.id },
      data: {
        band,
        criteriaScores: JSON.stringify(criteriaScores),
        feedback: JSON.stringify(parsedFeedback),
        strengths: JSON.stringify(strengths),
        weaknesses: JSON.stringify(weaknesses),
        improvementSuggestions: JSON.stringify(improvementSuggestions)
      }
    });

    return {
      id: updatedEntry.id,
      band: updatedEntry.band,
      criteriaScores: updatedEntry.criteriaScores,
      feedback: updatedEntry.feedback,
      strengths: updatedEntry.strengths,
      weaknesses: updatedEntry.weaknesses,
      improvementSuggestions: updatedEntry.improvementSuggestions
    };
  } catch (error) {
    console.error('Error processing writing entry:', error);
    throw error;
  }
}

/**
 * Create a writing entry with manual band score
 *
 * @param data - The writing entry data with manual band score
 * @returns The created writing entry
 */
export async function createWritingEntryWithManualBand(data: {
  studentId: string;
  taskType: 'task1' | 'task2' | 'both';
  band: number;
  taskQuestion?: string;
  essayText?: string;
  imageUrl?: string;
}) {
  // Apply inflation to the band score
  const rawBand = data.band;
  const inflatedBand = inflateBandScore(rawBand, data.taskType) as number;

  // Generate standardized feedback based on the band score
  const feedback = generateStandardizedFeedback(inflatedBand, 'writing');

  // Create varied criteria scores based on the band
  // This creates a more realistic breakdown with slight variations
  const taskAchievementScore = data.taskType === 'task1'
    ? Math.max(0, Math.round((rawBand - 0.5) * 10) / 10)
    : rawBand;

  const coherenceScore = Math.min(9, Math.round((rawBand + 0.5) * 10) / 10);
  const lexicalScore = Math.max(0, Math.round((rawBand - 0.5) * 10) / 10);
  const grammarScore = data.taskType === 'task1'
    ? Math.max(0, Math.round((rawBand - 0.5) * 10) / 10)
    : rawBand;

  const criteriaScores = [
    {
      name: data.taskType === 'task1' ? 'Task Achievement' : 'Task Response',
      score: taskAchievementScore,
      feedback: `Band ${taskAchievementScore} level performance for ${data.taskType === 'task1' ? 'Task Achievement' : 'Task Response'}.`
    },
    {
      name: 'Coherence and Cohesion',
      score: coherenceScore,
      feedback: `Band ${coherenceScore} level performance for Coherence and Cohesion.`
    },
    {
      name: 'Lexical Resource',
      score: lexicalScore,
      feedback: `Band ${lexicalScore} level performance for Lexical Resource.`
    },
    {
      name: 'Grammatical Range and Accuracy',
      score: grammarScore,
      feedback: `Band ${grammarScore} level performance for Grammatical Range and Accuracy.`
    }
  ];

  // Create entry with calculated values
  return prisma.writingEntry.create({
    data: {
      studentId: data.studentId,
      taskType: data.taskType,
      taskQuestion: data.taskQuestion || null,
      essayText: data.essayText || null,
      imageUrl: data.imageUrl || null,
      band: inflatedBand,
      criteriaScores: JSON.stringify(criteriaScores),
      feedback: JSON.stringify({ overallScore: rawBand, criteria: criteriaScores }),
      strengths: JSON.stringify(feedback.strengths),
      weaknesses: JSON.stringify(feedback.weaknesses),
      improvementSuggestions: JSON.stringify(feedback.improvements)
    }
  });
}

/**
 * Update a writing entry with manual band score
 *
 * @param id - The writing entry ID
 * @param band - The manual band score
 * @param taskType - The task type ('task1', 'task2', or 'both')
 * @returns The updated writing entry
 */
export async function updateWritingEntryWithManualBand(
  id: string,
  band: number,
  taskType: 'task1' | 'task2' | 'both'
) {
  // Apply inflation to the band score
  const inflatedBand = inflateBandScore(band, taskType) as number;

  // Generate standardized feedback
  const feedback = generateStandardizedFeedback(inflatedBand, 'writing');

  // Create varied criteria scores based on the band
  // This creates a more realistic breakdown with slight variations
  const taskAchievementScore = taskType === 'task1'
    ? Math.max(0, Math.round((band - 0.5) * 10) / 10)
    : band;

  const coherenceScore = Math.min(9, Math.round((band + 0.5) * 10) / 10);
  const lexicalScore = Math.max(0, Math.round((band - 0.5) * 10) / 10);
  const grammarScore = taskType === 'task1'
    ? Math.max(0, Math.round((band - 0.5) * 10) / 10)
    : band;

  const criteriaScores = [
    {
      name: taskType === 'task1' ? 'Task Achievement' : 'Task Response',
      score: taskAchievementScore,
      feedback: `Band ${taskAchievementScore} level performance for ${taskType === 'task1' ? 'Task Achievement' : 'Task Response'}.`
    },
    {
      name: 'Coherence and Cohesion',
      score: coherenceScore,
      feedback: `Band ${coherenceScore} level performance for Coherence and Cohesion.`
    },
    {
      name: 'Lexical Resource',
      score: lexicalScore,
      feedback: `Band ${lexicalScore} level performance for Lexical Resource.`
    },
    {
      name: 'Grammatical Range and Accuracy',
      score: grammarScore,
      feedback: `Band ${grammarScore} level performance for Grammatical Range and Accuracy.`
    }
  ];

  // Update entry with calculated values
  return prisma.writingEntry.update({
    where: { id },
    data: {
      band: inflatedBand,
      criteriaScores: JSON.stringify(criteriaScores),
      feedback: JSON.stringify({ overallScore: band, criteria: criteriaScores }),
      strengths: JSON.stringify(feedback.strengths),
      weaknesses: JSON.stringify(feedback.weaknesses),
      improvementSuggestions: JSON.stringify(feedback.improvements)
    }
  });
}

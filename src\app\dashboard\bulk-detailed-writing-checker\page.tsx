'use client';

import * as React from 'react';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Chip from '@mui/material/Chip';
import LinearProgress from '@mui/material/LinearProgress';
import Divider from '@mui/material/Divider';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import { Pencil as PencilIcon } from '@phosphor-icons/react/dist/ssr/Pencil';
import { ChartLineUp } from '@phosphor-icons/react/dist/ssr/ChartLineUp';
import { ArrowsClockwise } from '@phosphor-icons/react/dist/ssr/ArrowsClockwise';
import { CaretDown } from '@phosphor-icons/react/dist/ssr/CaretDown';
import { BookOpen } from '@phosphor-icons/react/dist/ssr/BookOpen';
import { Lightbulb } from '@phosphor-icons/react/dist/ssr/Lightbulb';
import { UploadSimple } from '@phosphor-icons/react/dist/ssr/UploadSimple';
import { Plus } from '@phosphor-icons/react/dist/ssr/Plus';
import { Trash } from '@phosphor-icons/react/dist/ssr/Trash';
import { File } from '@phosphor-icons/react/dist/ssr/File';
import { Download } from '@phosphor-icons/react/dist/ssr/Download';
import { Article } from '@phosphor-icons/react/dist/ssr/Article';
import { FilePdf } from '@phosphor-icons/react/dist/ssr/FilePdf';
import { FileText } from '@phosphor-icons/react/dist/ssr/FileText';
import { Image } from '@phosphor-icons/react/dist/ssr/Image';
import { MagnifyingGlassPlus } from '@phosphor-icons/react/dist/ssr/MagnifyingGlassPlus';
import { Files } from '@phosphor-icons/react/dist/ssr/Files';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
// @ts-ignore - jsPDF has some TS issues but works fine
import 'jspdf-autotable';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
// @ts-ignore - Tesseract.js doesn't have TypeScript definitions
import * as Tesseract from 'tesseract.js';
// @ts-ignore - react-pdf doesn't have TypeScript definitions
import { pdfjs } from 'react-pdf';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';

// Initialize PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

type ModelType = 'gpt-4o';

// Essay types
interface Essay {
  id: string;
  name: string;
  text: string;
  taskQuestion: string;
  taskImageFile?: File | null;
  status: 'pending' | 'processing' | 'completed' | 'error';
  feedback?: DetailedEssayFeedback;
  error?: string;
}

// Extended types for detailed structured feedback
interface IELTSCriterion {
  name: string;
  score: number;
  feedback: string;
  strengths: string[];
  weaknesses: string[];
  improvements: string[];
}

interface VocabularyAnalysis {
  overusedWords: Array<{ word: string, count: number, alternatives: string[] }>;
  collocations: Array<{ incorrect: string, correct: string, context: string }>;
  academicPhrases: string[];
  lexicalDiversity: {
    uniqueWords: number;
    totalWords: number;
    diversityScore: number;
  };
  vocabularyLevel: {
    basic: string[];
    intermediate: string[];
    advanced: string[];
    distribution: {
      basic: string;
      intermediate: string;
      advanced: string;
    }
  };
  topicRelevantVocabulary: string[];
  transitionWords: {
    used: string[];
    suggestions: string[];
  };
  idiomsAndPhrases: {
    used: string[];
    suggestions: string[];
  };
}

interface SentenceImprovement {
  original: string;
  improved: string;
  explanation: string;
}

interface ParagraphAnalysis {
  content: string;
  purpose: string;
  coherence: number;
  topicSentence: {
    text: string;
    strength: number;
    improvement?: string;
  };
  development: {
    quality: number;
    missingElements?: string[];
    improvements: string[];
  };
  transitionQuality: number;
}

interface GrammarAnalysis {
  errorsByType: {
    [key: string]: Array<{ 
      original: string;
      correction: string;
      explanation: string;
      severity: 'minor' | 'major';
    }>
  };
  grammarStrengths: string[];
  totalErrors: number;
  errorFrequency: string;
  mostFrequentErrorType: string;
  errorDistribution: {
    [key: string]: number;
  };
}

interface DetailedEssayFeedback {
  overallScore: number;
  criteria: IELTSCriterion[];
  vocabularyAnalysis: VocabularyAnalysis;
  sentenceAnalysis: {
    improvements: SentenceImprovement[];
    complexSentences: number;
    simpleSentences: number;
    averageSentenceLength: number;
    sentenceLengthVariety: number;
    sentenceBeginnings: {
      variety: number;
      repetitivePatterns: string[];
    };
  };
  paragraphAnalysis: {
    introduction: ParagraphAnalysis;
    bodyParagraphs: ParagraphAnalysis[];
    conclusion: ParagraphAnalysis;
    overallCohesion: number;
  };
  grammarAnalysis: GrammarAnalysis;
  comparativeAnalysis: {
    structuralDifferences: string[];
    argumentationImprovements: string[];
    bandExample: string;
  };
  detailedImprovementPlan: {
    priorityAreas: string[];
    specificExercises: string[];
    resourceSuggestions: string[];
  };
}

// Tab panel component for the different feedback sections
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`writing-tabpanel-${index}`}
      aria-labelledby={`writing-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `writing-tab-${index}`,
    'aria-controls': `writing-tabpanel-${index}`,
  };
}

export default function BulkDetailedWritingCheckerPage(): React.JSX.Element {
  // State for managing essays
  const [essays, setEssays] = React.useState<Essay[]>([]);
  const [selectedEssayId, setSelectedEssayId] = React.useState<string | null>(null);
  const [selectedTabValue, setSelectedTabValue] = React.useState(0);
  const [model, setModel] = React.useState<ModelType>('gpt-4o');
  const [isProcessing, setIsProcessing] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  
  // State for managing essay editing
  const [showAddEssayDialog, setShowAddEssayDialog] = React.useState<boolean>(false);
  const [showTaskImageDialog, setShowTaskImageDialog] = React.useState<boolean>(false);
  const [editingEssay, setEditingEssay] = React.useState<{
    id?: string;
    name: string;
    text: string;
    taskQuestion: string;
    taskImageFile?: File | null;
  }>({
    name: '',
    text: '',
    taskQuestion: '',
    taskImageFile: null
  });
  
  // Global task question state
  const [globalTaskQuestion, setGlobalTaskQuestion] = React.useState<string>('');
  
  // Export menu state
  const [exportMenuAnchorEl, setExportMenuAnchorEl] = React.useState<null | HTMLElement>(null);
  
  // File processing progress state
  const [fileProcessingProgress, setFileProcessingProgress] = React.useState<{[key: string]: number}>({});
  
  // Check if there are any completed essays
  const hasCompletedEssays = essays.some(essay => essay.status === 'completed');
  
  // Menu state
  const [menuAnchorEl, setMenuAnchorEl] = React.useState<null | HTMLElement>(null);
  const [menuEssayId, setMenuEssayId] = React.useState<string | null>(null);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number): void => {
    setSelectedTabValue(newValue);
  };
  
  // Get the currently selected essay
  const selectedEssay = React.useMemo(
    () => essays.find((essay) => essay.id === selectedEssayId) || null,
    [essays, selectedEssayId]
  );
  
  // Generate a unique ID
  const generateId = (): string => {
    return Math.random().toString(36).substring(2, 9);
  };
  
  // Handle model change
  const handleModelChange = (
    event: React.MouseEvent<HTMLElement>,
    newModel: ModelType | null
  ): void => {
    if (newModel !== null) {
      setModel(newModel);
    }
  };
  
  // Essay menu handlers
  const handleOpenEssayMenu = (event: React.MouseEvent<HTMLElement>, essayId: string): void => {
    setMenuAnchorEl(event.currentTarget);
    setMenuEssayId(essayId);
  };

  const handleCloseEssayMenu = (): void => {
    setMenuAnchorEl(null);
    setMenuEssayId(null);
  };
  
  // Essay CRUD operations
  const handleAddEssay = (): void => {
    if (!editingEssay.name || !editingEssay.text || !editingEssay.taskQuestion) {
      setError('Please fill in all required fields');
      return;
    }

    const newEssay: Essay = {
      id: editingEssay.id || generateId(),
      name: editingEssay.name,
      text: editingEssay.text,
      taskQuestion: editingEssay.taskQuestion,
      taskImageFile: editingEssay.taskImageFile || null,
      status: 'pending'
    };

    if (editingEssay.id) {
      // Edit existing essay
      setEssays(essays.map((essay) => (essay.id === editingEssay.id ? newEssay : essay)));
    } else {
      // Add new essay
      setEssays([...essays, newEssay]);
    }

    setEditingEssay({
      name: '',
      text: '',
      taskQuestion: '',
      taskImageFile: null
    });
    setShowAddEssayDialog(false);
    setError(null);
  };

  const handleEditEssay = (essayId: string): void => {
    const essay = essays.find((essay) => essay.id === essayId);
    if (essay) {
      setEditingEssay({
        id: essay.id,
        name: essay.name,
        text: essay.text,
        taskQuestion: essay.taskQuestion,
        taskImageFile: essay.taskImageFile
      });
      setShowAddEssayDialog(true);
    }
    handleCloseEssayMenu();
  };

  const handleDeleteEssay = (essayId: string): void => {
    setEssays(essays.filter((essay) => essay.id !== essayId));
    if (selectedEssayId === essayId) {
      setSelectedEssayId(null);
    }
    handleCloseEssayMenu();
  };

  const handleOpenAddEssayDialog = (): void => {
    setEditingEssay({
      name: '',
      text: '',
      taskQuestion: '',
      taskImageFile: null
    });
    setShowAddEssayDialog(true);
  };

  const handleCloseAddEssayDialog = (): void => {
    setShowAddEssayDialog(false);
    setEditingEssay({
      name: '',
      text: '',
      taskQuestion: '',
      taskImageFile: null
    });
    setError(null);
  };

  const handleOpenTaskImageDialog = (): void => {
    setShowTaskImageDialog(true);
  };

  const handleCloseTaskImageDialog = (): void => {
    setShowTaskImageDialog(false);
  };

  const handleTaskImageFileChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    if (event.target.files && event.target.files[0]) {
      setEditingEssay({
        ...editingEssay,
        taskImageFile: event.target.files[0]
      });
    }
  };

  // Process all essays
  const processEssays = async (): Promise<void> => {
    if (essays.length === 0) {
      setError('Please add at least one essay to check');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Only process essays that are in 'pending' status
      const essaysToProcess = essays.filter((essay) => essay.status === 'pending');
      
      if (essaysToProcess.length === 0) {
        setIsProcessing(false);
        return;
      }

      // Mark essays as processing
      setEssays(
        essays.map((essay) =>
          essaysToProcess.some((e) => e.id === essay.id)
            ? { ...essay, status: 'processing' }
            : essay
        )
      );

      const response = await fetch('/api/bulk-detailed-writing-checker', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          essays: essaysToProcess.map((essay) => ({
            id: essay.id,
            text: essay.text,
            taskQuestion: essay.taskQuestion,
          })),
          model,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to process essays');
      }

      const data = await response.json();

      // Update essays with feedback
      setEssays(
        essays.map((essay) => {
          const processedEssay = data.find((item: any) => item.id === essay.id);
          if (!processedEssay) {
            return essay;
          }

          if (processedEssay.status === 'error') {
            return {
              ...essay,
              status: 'error',
              error: processedEssay.error,
            };
          }

          return {
            ...essay,
            status: 'completed',
            feedback: processedEssay.feedback,
          };
        })
      );

      // Select the first processed essay if none selected
      if (!selectedEssayId && data.length > 0 && data[0].status === 'success') {
        setSelectedEssayId(data[0].id);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsProcessing(false);
    }
  };

  // Generate PDF report for an essay
  const generatePDF = (essay: Essay): void => {
    if (!essay.feedback) {
      return;
    }
    
    const feedback = essay.feedback;
    const doc = new jsPDF();
    let currentY = 15;
    let pageCount = 1;

    // Helper function to check and add a new page if needed
    const checkAndAddPage = (height: number) => {
      if (currentY + height > 280) {
        doc.setFontSize(10);
        doc.text(`Page ${pageCount} of pagesToBeAdded`, 105, 290, { align: 'center' });
        doc.addPage();
        pageCount++;
        currentY = 15;
      }
    };

    // Helper function to wrap text
    const textLines = (text: string, fontSize: number, maxWidth: number): string[] => {
      doc.setFontSize(fontSize);
      return doc.splitTextToSize(text, maxWidth);
    };

    // Title
    doc.setFontSize(16);
    doc.text('IELTS Detailed Writing Assessment Results', 105, 17, { align: 'center' });
    currentY += 15;

    // Add Task Image if available
    if (essay.taskImageFile) {
      checkAndAddPage(100); // Estimated height for image
      doc.text('Task Image:', 15, currentY);
      currentY += 7;
      
      const reader = new FileReader();
      reader.onload = function(event) {
        if (event.target?.result) {
          const imgData = event.target.result as string;
          doc.addImage(imgData, 'JPEG', 15, currentY, 180, 90);
          currentY += 100;
          
          // Continue with the rest of the PDF generation
          continueWithPDFGeneration();
        }
      };
      
      // Start reading the image file
      reader.readAsDataURL(essay.taskImageFile);
    } else {
      // No image, continue directly
      continueWithPDFGeneration();
    }

    // The function to continue PDF generation after image handling
    function continueWithPDFGeneration() {
      // Essay info
      checkAndAddPage(20);
      doc.setFontSize(14);
      doc.text(`Essay: ${essay.name}`, 15, currentY + 8);
      currentY += 15;

      // Overall score with circle
      checkAndAddPage(40);
      doc.setFontSize(12);
      doc.text(`Overall Band Score:`, 15, currentY);
      
      const score = feedback.overallScore;
      
      // Draw circle for score
      const centerX = 112;
      doc.setDrawColor(
        score >= 8 ? 0 : 255,
        score >= 6 ? 150 : 0,
        0
      );
      doc.setFillColor(
        score >= 8 ? 240 : 255,
        score >= 6 ? 255 : 240,
        240
      );
      doc.circle(centerX, currentY, 10, 'FD');
      
      // Add score text
      doc.setFontSize(12);
      doc.setTextColor(
        score >= 8 ? 0 : 255,
        score >= 6 ? 0 : 0,
        0
      );
      doc.text(score.toFixed(1), 112, currentY, { align: 'center' });
      doc.setTextColor(0, 0, 0); // Reset text color
      
      currentY += 15;
      
      // Comparative analysis - band example
      if (feedback.comparativeAnalysis.bandExample) {
        checkAndAddPage(30);
        const bandExampleLines = textLines(feedback.comparativeAnalysis.bandExample, 10, 180);
        doc.setFontSize(10);
        doc.text(bandExampleLines, 15, currentY + 15);
        currentY += 10 + bandExampleLines.length * 5;
      }
      
      // Task question
      checkAndAddPage(25);
      doc.setFontSize(11);
      doc.text('Task Question:', 20, currentY + 5);
      currentY += 10;
      
      const taskLines = textLines(essay.taskQuestion, 10, 170);
      doc.setFontSize(10);
      doc.text(taskLines, 20, currentY + 12);
      currentY += taskLines.length * 5 + 15;
      
      // Criteria breakdown
      checkAndAddPage(20);
      doc.setFontSize(14);
      doc.text('Criteria Breakdown', 15, currentY + 7);
      currentY += 15;
      
      // Add a table for the criteria
      autoTable(doc, {
        startY: currentY,
        head: [['Criterion', 'Score', 'Strengths', 'Weaknesses', 'Improvements']],
        body: feedback.criteria.map(criterion => [
          criterion.name,
          criterion.score.toFixed(1),
          criterion.strengths.join('\n'),
          criterion.weaknesses.join('\n'),
          criterion.improvements.join('\n')
        ]),
        styles: {
          fontSize: 9,
          cellPadding: 2,
        },
        headStyles: {
          fillColor: [100, 100, 100],
        },
        columnStyles: {
          0: { cellWidth: 30 },
          1: { cellWidth: 15, halign: 'center' },
          2: { cellWidth: 45 },
          3: { cellWidth: 45 },
          4: { cellWidth: 45 },
        },
      });
      
      currentY = (doc as any).lastAutoTable.finalY + 10;
      
      // Paragraph Analysis
      checkAndAddPage(20);
      doc.setFontSize(14);
      doc.text('Paragraph Analysis', 15, currentY + 7);
      currentY += 15;
      
      // Introduction
      checkAndAddPage(20);
      doc.setFontSize(12);
      doc.text('Introduction', 15, currentY);
      currentY += 7;
      
      const introLines = textLines(feedback.paragraphAnalysis.introduction.content, 9, 170);
      doc.setFontSize(9);
      doc.text(introLines, 20, currentY);
      currentY += introLines.length * 5 + 5;
      
      doc.setFontSize(9);
      doc.text(`Coherence: ${feedback.paragraphAnalysis.introduction.coherence}/10`, 20, currentY);
      currentY += 5;
      doc.text(`Purpose: ${feedback.paragraphAnalysis.introduction.purpose}`, 20, currentY);
      currentY += 5;
      
      if (feedback.paragraphAnalysis.introduction.topicSentence.improvement) {
        const improvementLines = textLines(`Topic Sentence Improvement: ${feedback.paragraphAnalysis.introduction.topicSentence.improvement}`, 9, 170);
        doc.text(improvementLines, 20, currentY);
        currentY += improvementLines.length * 5 + 5;
      }
      
      // Body Paragraphs
      for (let i = 0; i < feedback.paragraphAnalysis.bodyParagraphs.length; i++) {
        const para = feedback.paragraphAnalysis.bodyParagraphs[i];
        
        checkAndAddPage(20);
        doc.setFontSize(12);
        doc.text(`Body Paragraph ${i + 1}`, 15, currentY);
        currentY += 7;
        
        const paraLines = textLines(para.content, 9, 170);
        doc.setFontSize(9);
        doc.text(paraLines, 20, currentY);
        currentY += paraLines.length * 5 + 5;
        
        doc.setFontSize(9);
        doc.text(`Coherence: ${para.coherence}/10`, 20, currentY);
        currentY += 5;
        doc.text(`Purpose: ${para.purpose}`, 20, currentY);
        currentY += 5;
        
        if (para.topicSentence.improvement) {
          const improvementLines = textLines(`Topic Sentence Improvement: ${para.topicSentence.improvement}`, 9, 170);
          doc.text(improvementLines, 20, currentY);
          currentY += improvementLines.length * 5 + 5;
        }
      }
      
      // Conclusion
      checkAndAddPage(20);
      doc.setFontSize(12);
      doc.text('Conclusion', 15, currentY);
      currentY += 7;
      
      const conclusionLines = textLines(feedback.paragraphAnalysis.conclusion.content, 9, 170);
      doc.setFontSize(9);
      doc.text(conclusionLines, 20, currentY);
      currentY += conclusionLines.length * 5 + 5;
      
      doc.setFontSize(9);
      doc.text(`Coherence: ${feedback.paragraphAnalysis.conclusion.coherence}/10`, 20, currentY);
      currentY += 5;
      doc.text(`Purpose: ${feedback.paragraphAnalysis.conclusion.purpose}`, 20, currentY);
      currentY += 5;
      
      if (feedback.paragraphAnalysis.conclusion.topicSentence.improvement) {
        const improvementLines = textLines(`Topic Sentence Improvement: ${feedback.paragraphAnalysis.conclusion.topicSentence.improvement}`, 9, 170);
        doc.text(improvementLines, 20, currentY);
        currentY += improvementLines.length * 5 + 5;
      }
      
      // Overall Cohesion
      doc.setFontSize(10);
      doc.text(`Overall Cohesion Score: ${feedback.paragraphAnalysis.overallCohesion.toFixed(1)}/10`, 15, currentY);
      currentY += 10;
      
      // Sentence Analysis
      checkAndAddPage(20);
      doc.setFontSize(14);
      doc.text('Sentence Analysis', 15, currentY + 7);
      currentY += 15;
      
      // Sentence metrics table
      autoTable(doc, {
        startY: currentY,
        head: [['Metric', 'Value']],
        body: [
          ['Complex Sentences', feedback.sentenceAnalysis.complexSentences.toString()],
          ['Simple Sentences', feedback.sentenceAnalysis.simpleSentences.toString()],
          ['Average Sentence Length', feedback.sentenceAnalysis.averageSentenceLength.toFixed(1) + ' words'],
          ['Sentence Length Variety', (feedback.sentenceAnalysis.sentenceLengthVariety * 100).toFixed(0) + '%'],
          ['Sentence Beginnings Variety', (feedback.sentenceAnalysis.sentenceBeginnings.variety * 100).toFixed(0) + '%']
        ],
        styles: {
          fontSize: 9,
          cellPadding: 2,
        },
        headStyles: {
          fillColor: [100, 100, 100],
        },
      });
      
      currentY = (doc as any).lastAutoTable.finalY + 10;
      
      // Sentence improvements
      checkAndAddPage(20);
      doc.setFontSize(12);
      doc.text('Sentence Improvements', 15, currentY + 7);
      currentY += 15;
      
      // Create a table for sentence improvements
      autoTable(doc, {
        startY: currentY,
        head: [['Original', 'Improved', 'Explanation']],
        body: feedback.sentenceAnalysis.improvements.map(imp => [
          imp.original,
          imp.improved,
          imp.explanation
        ]),
        styles: {
          fontSize: 9,
          cellPadding: 2,
        },
        headStyles: {
          fillColor: [100, 100, 100],
        },
        columnStyles: {
          0: { cellWidth: 60, fillColor: [255, 240, 240] },
          1: { cellWidth: 60, fillColor: [240, 255, 240] },
          2: { cellWidth: 60 },
        },
      });
      
      currentY = (doc as any).lastAutoTable.finalY + 10;
      
      // Vocabulary Analysis
      checkAndAddPage(20);
      doc.setFontSize(14);
      doc.text('Vocabulary Analysis', 15, currentY + 7);
      currentY += 15;
      
      // Lexical diversity
      doc.setFontSize(12);
      doc.text('Lexical Diversity', 15, currentY);
      currentY += 10;
      
      doc.setFontSize(10);
      doc.text(`Unique Words: ${feedback.vocabularyAnalysis.lexicalDiversity.uniqueWords}`, 20, currentY);
      currentY += 5;
      doc.text(`Total Words: ${feedback.vocabularyAnalysis.lexicalDiversity.totalWords}`, 20, currentY);
      currentY += 5;
      doc.text(`Diversity Score: ${(feedback.vocabularyAnalysis.lexicalDiversity.diversityScore * 100).toFixed(0)}%`, 20, currentY);
      currentY += 10;
      
      // Vocabulary level distribution
      checkAndAddPage(20);
      doc.setFontSize(12);
      doc.text('Vocabulary Level Distribution', 15, currentY);
      currentY += 10;
      
      const levels = feedback.vocabularyAnalysis.vocabularyLevel.distribution;
      doc.setFontSize(10);
      doc.text(`Basic: ${levels.basic}`, 20, currentY);
      doc.text(`Intermediate: ${levels.intermediate}`, 20, currentY + 6);
      doc.text(`Advanced: ${levels.advanced}`, 20, currentY + 12);
      currentY += 20;
      
      // Sample words by level
      doc.setFontSize(12);
      doc.text('Sample words by level:', 15, currentY);
      currentY += 10;
      
      // Create columns for word levels
      const columnWidth = 60;
      const startX = 15;
      
      const wordLevels = [
        {
          title: 'Basic',
          items: feedback.vocabularyAnalysis.vocabularyLevel.basic.slice(0, 5)
        },
        {
          title: 'Intermediate',
          items: feedback.vocabularyAnalysis.vocabularyLevel.intermediate.slice(0, 5)
        },
        {
          title: 'Advanced',
          items: feedback.vocabularyAnalysis.vocabularyLevel.advanced.slice(0, 5)
        }
      ];
      
      const maxItems = Math.max(...wordLevels.map(level => level.items.length));
      
      for (let i = 0; i < wordLevels.length; i++) {
        const level = wordLevels[i];
        const xPos = startX + i * columnWidth;
        
        // Title
        doc.setFontSize(10);
        doc.text(level.title, xPos, currentY);
        
        // Items
        doc.setFontSize(9);
        for (let j = 0; j < level.items.length; j++) {
          doc.text(`• ${level.items[j]}`, xPos, currentY + 7 + j * 5);
        }
      }
      
      currentY += 7 + maxItems * 5 + 8;
      
      // Overused Words & Better Alternatives
      checkAndAddPage(20);
      doc.setFontSize(12);
      doc.text('Overused Words & Better Alternatives', 15, currentY);
      currentY += 10;
      
      if (feedback.vocabularyAnalysis.overusedWords.length > 0) {
        // Table for overused words
        autoTable(doc, {
          startY: currentY,
          head: [['Word', 'Count', 'Alternatives']],
          body: feedback.vocabularyAnalysis.overusedWords.map(word => [
            word.word,
            word.count.toString(),
            word.alternatives.join(', ')
          ]),
          styles: {
            fontSize: 9,
            cellPadding: 2,
          },
          headStyles: {
            fillColor: [100, 100, 100],
          },
          columnStyles: {
            0: { cellWidth: 30 },
            1: { cellWidth: 20, halign: 'center' },
            2: { cellWidth: 130 }
          },
        });
        
        currentY = (doc as any).lastAutoTable.finalY + 10;
      } else {
        doc.setFontSize(10);
        doc.text('No significantly overused words detected.', 20, currentY);
        currentY += 10;
      }
      
      // Collocation Improvements
      checkAndAddPage(20);
      doc.setFontSize(12);
      doc.text('Collocation Improvements', 15, currentY);
      currentY += 10;
      
      if (feedback.vocabularyAnalysis.collocations.length > 0) {
        // Table for collocations
        autoTable(doc, {
          startY: currentY,
          head: [['Incorrect', 'Correct', 'Context']],
          body: feedback.vocabularyAnalysis.collocations.map(coll => [
            coll.incorrect,
            coll.correct,
            coll.context
          ]),
          styles: {
            fontSize: 9,
            cellPadding: 2,
          },
          headStyles: {
            fillColor: [100, 100, 100],
          },
          columnStyles: {
            0: { cellWidth: 40, fillColor: [255, 240, 240] },
            1: { cellWidth: 40, fillColor: [240, 255, 240] },
            2: { cellWidth: 100 }
          },
        });
        
        currentY = (doc as any).lastAutoTable.finalY + 10;
      } else {
        doc.setFontSize(10);
        doc.text('No collocation errors detected.', 20, currentY);
        currentY += 10;
      }
      
      // Grammar Analysis
      checkAndAddPage(20);
      doc.setFontSize(14);
      doc.text('Grammar Analysis', 15, currentY + 7);
      currentY += 15;
      
      // Error summary
      doc.setFontSize(12);
      doc.text('Error Summary', 15, currentY);
      currentY += 10;
      
      doc.setFontSize(10);
      doc.text(`Total Errors: ${feedback.grammarAnalysis.totalErrors}`, 20, currentY);
      currentY += 5;
      doc.text(`Error Frequency: ${feedback.grammarAnalysis.errorFrequency}`, 20, currentY);
      currentY += 5;
      doc.text(`Most Frequent Error Type: ${feedback.grammarAnalysis.mostFrequentErrorType}`, 20, currentY);
      currentY += 15;
      
      // Error distribution
      if (Object.keys(feedback.grammarAnalysis.errorDistribution).length > 0) {
        // Table for error distribution
        autoTable(doc, {
          startY: currentY,
          head: [['Error Type', 'Percentage']],
          body: Object.entries(feedback.grammarAnalysis.errorDistribution).map(([type, percentage]) => [
            type,
            `${percentage}%`
          ]),
          styles: {
            fontSize: 9,
            cellPadding: 2,
          },
          headStyles: {
            fillColor: [100, 100, 100],
          },
        });
        
        currentY = (doc as any).lastAutoTable.finalY + 10;
      }
      
      // Grammar strengths
      checkAndAddPage(20);
      doc.setFontSize(12);
      doc.text('Grammar Strengths', 15, currentY);
      currentY += 10;
      
      const strengthLines = textLines(feedback.grammarAnalysis.grammarStrengths.join(', '), 10, 170);
      doc.setFontSize(10);
      doc.text(strengthLines, 20, currentY);
      currentY += strengthLines.length * 5 + 10;
      
      // Detailed grammar errors by type
      if (Object.keys(feedback.grammarAnalysis.errorsByType).length > 0) {
        for (const [errorType, errors] of Object.entries(feedback.grammarAnalysis.errorsByType)) {
          if (errors.length === 0) continue;
          
          checkAndAddPage(20);
          
          // Format error type as title
          const formattedErrorType = errorType.charAt(0).toUpperCase() + errorType.slice(1);
          doc.setFontSize(12);
          doc.text(formattedErrorType, 15, currentY);
          currentY += 10;
          
          // List each error with original, correction, and explanation
          for (let i = 0; i < errors.length; i++) {
            const error = errors[i];
            checkAndAddPage(30);
            
            doc.setFontSize(9);
            doc.text('Original:', 20, currentY + 6);
            
            const originalLines = textLines(error.original || "Not specified", 9, 130);
            doc.text(originalLines, 60, currentY + 6);
            
            currentY += originalLines.length * 5;
            
            doc.text('Correction:', 20, currentY + 14);
            
            const correctionLines = textLines(error.correction || "Not specified", 9, 130);
            doc.text(correctionLines, 60, currentY + 14);
            
            currentY += correctionLines.length * 5 + 8;
            
            const severityLabel = `Severity: ${error.severity === 'major' ? 'Major' : 'Minor'}`;
            doc.text(severityLabel, 20, currentY);
            
            currentY += 5;
            
            const explanationLines = textLines(`Explanation: ${error.explanation || "Not provided"}`, 9, 160);
            doc.text(explanationLines, 20, currentY);
            
            currentY += explanationLines.length * 5 + 8;
          }
        }
      }
      
      // Improvement Plan
      checkAndAddPage(20);
      doc.setFontSize(14);
      doc.text('Improvement Plan', 15, currentY + 7);
      currentY += 15;
      
      // Priority areas
      doc.setFontSize(12);
      doc.text('Priority Areas for Improvement:', 15, currentY);
      currentY += 7;
      
      doc.setFontSize(9);
      for (let i = 0; i < feedback.detailedImprovementPlan.priorityAreas.length; i++) {
        const area = feedback.detailedImprovementPlan.priorityAreas[i];
        doc.text(`${i + 1}. ${area}`, 20, currentY);
        currentY += 5;
      }
      
      currentY += 5;
      
      // Specific exercises
      checkAndAddPage(20);
      doc.setFontSize(12);
      doc.text('Recommended Exercises:', 15, currentY);
      currentY += 7;
      
      doc.setFontSize(9);
      for (let i = 0; i < feedback.detailedImprovementPlan.specificExercises.length; i++) {
        const exercise = feedback.detailedImprovementPlan.specificExercises[i];
        const exerciseLines = textLines(`${i + 1}. ${exercise}`, 9, 170);
        doc.text(exerciseLines, 20, currentY);
        currentY += exerciseLines.length * 5;
      }
      
      currentY += 5;
      
      // Resource suggestions
      checkAndAddPage(20);
      doc.setFontSize(12);
      doc.text('Recommended Resources:', 15, currentY);
      currentY += 7;
      
      doc.setFontSize(9);
      for (let i = 0; i < feedback.detailedImprovementPlan.resourceSuggestions.length; i++) {
        const resource = feedback.detailedImprovementPlan.resourceSuggestions[i];
        doc.text(`${i + 1}. ${resource}`, 20, currentY);
        currentY += 5;
      }
      
      // Add page numbers to all pages
      const totalPages = pageCount;
      for (let i = 1; i <= totalPages; i++) {
        doc.setPage(i);
        doc.setFontSize(10);
        doc.text(`Page ${i} of ${totalPages}`, 105, 290, { align: 'center' });
      }
      
      // Save the PDF
      doc.save(`${essay.name.replace(/\s+/g, '_')}_detailed_writing_assessment.pdf`);
    }
  };

  // Render the essay list
  const renderEssayList = () => {
    return (
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Task Question</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="right">Score</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {essays.map((essay) => (
              <TableRow
                key={essay.id}
                hover
                selected={selectedEssayId === essay.id}
                onClick={() => setSelectedEssayId(essay.id)}
                sx={{ cursor: 'pointer' }}
              >
                <TableCell>{essay.name}</TableCell>
                <TableCell>
                  {essay.taskQuestion.length > 50
                    ? `${essay.taskQuestion.substring(0, 50)}...`
                    : essay.taskQuestion}
                </TableCell>
                <TableCell>
                  {essay.status === 'pending' && (
                    <Chip size="small" label="Pending" color="default" />
                  )}
                  {essay.status === 'processing' && (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CircularProgress size={16} sx={{ mr: 1 }} />
                      <Chip size="small" label="Processing" color="primary" />
                    </Box>
                  )}
                  {essay.status === 'completed' && (
                    <Chip size="small" label="Completed" color="success" />
                  )}
                  {essay.status === 'error' && (
                    <Chip size="small" label="Error" color="error" />
                  )}
                </TableCell>
                <TableCell align="right">
                  {essay.feedback ? essay.feedback.overallScore.toFixed(1) : '-'}
                </TableCell>
                <TableCell align="right">
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    {essay.status === 'completed' && (
                      <Button
                        size="small"
                        color="primary"
                        onClick={(e) => {
                          e.stopPropagation();
                          generatePDF(essay);
                        }}
                        sx={{ mr: 1 }}
                      >
                        <FilePdf size={18} />
                      </Button>
                    )}
                    <Button
                      size="small"
                      color="error"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteEssay(essay.id);
                      }}
                    >
                      <Trash size={18} />
                    </Button>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  // Render the essay menu
  const renderEssayMenu = () => {
    const open = Boolean(menuAnchorEl);
    const essay = essays.find((essay) => essay.id === menuEssayId);

    return (
      <Menu
        anchorEl={menuAnchorEl}
        id="essay-menu"
        open={open}
        onClose={handleCloseEssayMenu}
        PaperProps={{
          elevation: 0,
          sx: {
            overflow: 'visible',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
          },
        }}
      >
        <MenuItem onClick={() => handleEditEssay(menuEssayId!)}>
          <ListItemIcon>
            <PencilIcon size={16} />
          </ListItemIcon>
          <ListItemText>Edit</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleDeleteEssay(menuEssayId!)}>
          <ListItemIcon>
            <Trash size={16} />
          </ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
        {essay && essay.status === 'completed' && (
          <MenuItem onClick={() => generatePDF(essay)}>
            <ListItemIcon>
              <FilePdf size={16} />
            </ListItemIcon>
            <ListItemText>Download PDF</ListItemText>
          </MenuItem>
        )}
      </Menu>
    );
  };

  // Render the essay detail
  const renderEssayDetail = () => {
    if (!selectedEssay) {
      return (
        <Box sx={{ py: 4, textAlign: 'center' }}>
          <Typography color="text.secondary">
            Select an essay to view its details
          </Typography>
        </Box>
      );
    }

    if (selectedEssay.status === 'pending' || selectedEssay.status === 'processing') {
      return (
        <Box sx={{ py: 4, textAlign: 'center' }}>
          <Typography color="text.secondary" sx={{ mb: 2 }}>
            {selectedEssay.status === 'pending'
              ? 'This essay is pending analysis'
              : 'This essay is being analyzed'}
          </Typography>
          {selectedEssay.status === 'processing' && (
            <CircularProgress size={24} sx={{ mb: 2 }} />
          )}
          <Button
            variant="outlined"
            color="primary"
            onClick={processEssays}
            disabled={isProcessing}
            startIcon={<ArrowsClockwise />}
          >
            {isProcessing ? 'Processing...' : 'Start Analysis'}
          </Button>
        </Box>
      );
    }

    if (selectedEssay.status === 'error') {
      return (
        <Box sx={{ py: 4, textAlign: 'center' }}>
          <Alert severity="error" sx={{ mb: 2 }}>
            Error: {selectedEssay.error || 'An unexpected error occurred'}
          </Alert>
          <Button
            variant="outlined"
            color="primary"
            onClick={processEssays}
            disabled={isProcessing}
            startIcon={<ArrowsClockwise />}
          >
            Retry Analysis
          </Button>
        </Box>
      );
    }

    if (!selectedEssay.feedback) {
      return (
        <Box sx={{ py: 4, textAlign: 'center' }}>
          <Typography color="text.secondary">
            No feedback available for this essay
          </Typography>
        </Box>
      );
    }

    const feedback = selectedEssay.feedback;

    return (
      <Card>
        <CardContent>
          <Box sx={{ mb: 3 }}>
            <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
              <Typography variant="h5">{selectedEssay.name}</Typography>
              <Button
                variant="outlined"
                color="primary"
                onClick={() => generatePDF(selectedEssay)}
                startIcon={<Download />}
              >
                Download PDF Report
              </Button>
            </Stack>
            <Box sx={{ mt: 1 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Task Question:
              </Typography>
              <Typography variant="body2">{selectedEssay.taskQuestion}</Typography>
            </Box>
            {selectedEssay.taskImageFile && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Task Image:
                </Typography>
                <Box 
                  component="img" 
                  src={URL.createObjectURL(selectedEssay.taskImageFile)} 
                  alt="Task Image"
                  sx={{ 
                    mt: 1, 
                    maxWidth: '100%', 
                    maxHeight: '200px', 
                    border: '1px solid', 
                    borderColor: 'divider',
                    borderRadius: 1 
                  }}
                />
              </Box>
            )}
          </Box>

          <Divider sx={{ my: 2 }} />

          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={selectedTabValue} onChange={handleTabChange} aria-label="feedback tabs">
              <Tab label="Overview" {...a11yProps(0)} />
              <Tab label="Sentence Analysis" {...a11yProps(1)} />
              <Tab label="Paragraph Analysis" {...a11yProps(2)} />
              <Tab label="Grammar" {...a11yProps(3)} />
              <Tab label="Vocabulary" {...a11yProps(4)} />
              <Tab label="Improvement Plan" {...a11yProps(5)} />
            </Tabs>
          </Box>

          <TabPanel value={selectedTabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Overall Score
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', my: 2 }}>
                      <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                        <CircularProgress
                          variant="determinate"
                          value={feedback.overallScore * 10}
                          color={
                            feedback.overallScore >= 8
                              ? 'success'
                              : feedback.overallScore >= 6
                              ? 'warning'
                              : 'error'
                          }
                          size={120}
                          thickness={5}
                        />
                        <Box
                          sx={{
                            top: 0,
                            left: 0,
                            bottom: 0,
                            right: 0,
                            position: 'absolute',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <Typography variant="h4" component="div">
                            {feedback.overallScore}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                    <Typography variant="body2" color="text.secondary" textAlign="center">
                      {feedback.overallScore >= 8
                        ? 'Excellent! Your writing demonstrates strong proficiency.'
                        : feedback.overallScore >= 7
                        ? 'Good work! Your writing shows good command of language.'
                        : feedback.overallScore >= 6
                        ? 'Satisfactory. Your writing fulfills basic requirements.'
                        : 'Needs improvement. Focus on the suggested areas.'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={8}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      IELTS Criteria Breakdown
                    </Typography>
                    <Box sx={{ mt: 2 }}>
                      {feedback.criteria.map((criterion, index) => (
                        <Box key={index} sx={{ mb: 3 }}>
                          <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 1 }}>
                            <Typography variant="subtitle1" fontWeight="medium">
                              {criterion.name}
                            </Typography>
                            <Box sx={{ flex: 1 }} />
                            <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                              <CircularProgress
                                variant="determinate"
                                value={criterion.score * 10}
                                color={
                                  criterion.score >= 8
                                    ? 'success'
                                    : criterion.score >= 6
                                    ? 'warning'
                                    : 'error'
                                }
                                size={40}
                                thickness={4}
                              />
                              <Box
                                sx={{
                                  top: 0,
                                  left: 0,
                                  bottom: 0,
                                  right: 0,
                                  position: 'absolute',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                }}
                              >
                                <Typography variant="caption" component="div" fontWeight="bold">
                                  {criterion.score}
                                </Typography>
                              </Box>
                            </Box>
                          </Stack>
                          <Typography variant="body2" sx={{ mb: 1.5 }}>
                            {criterion.feedback}
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="overline" color="success.main">
                                Strengths
                              </Typography>
                              <ul style={{ marginTop: 4, paddingLeft: 16 }}>
                                {criterion.strengths.map((strength, idx) => (
                                  <li key={idx}>
                                    <Typography variant="body2">{strength}</Typography>
                                  </li>
                                ))}
                              </ul>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="overline" color="error.main">
                                Areas for Improvement
                              </Typography>
                              <ul style={{ marginTop: 4, paddingLeft: 16 }}>
                                {criterion.weaknesses.map((weakness, idx) => (
                                  <li key={idx}>
                                    <Typography variant="body2">{weakness}</Typography>
                                  </li>
                                ))}
                              </ul>
                            </Grid>
                          </Grid>
                        </Box>
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={selectedTabValue} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={5}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Sentence Metrics
                    </Typography>
                    <Box sx={{ mt: 2 }}>
                      <Stack spacing={2}>
                        <Box>
                          <Typography variant="subtitle2" gutterBottom>
                            Complex vs Simple Sentences
                          </Typography>
                          <Grid container spacing={1}>
                            <Grid item xs={6}>
                              <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'action.hover', borderRadius: 1 }}>
                                <Typography variant="h5">{feedback.sentenceAnalysis.complexSentences}</Typography>
                                <Typography variant="caption">Complex</Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={6}>
                              <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'action.hover', borderRadius: 1 }}>
                                <Typography variant="h5">{feedback.sentenceAnalysis.simpleSentences}</Typography>
                                <Typography variant="caption">Simple</Typography>
                              </Box>
                            </Grid>
                          </Grid>
                        </Box>
                        
                        <Box>
                          <Typography variant="subtitle2" gutterBottom>
                            Average Sentence Length
                          </Typography>
                          <Typography variant="h6">
                            {feedback.sentenceAnalysis.averageSentenceLength.toFixed(1)} words
                          </Typography>
                        </Box>
                        
                        <Box>
                          <Typography variant="subtitle2" gutterBottom>
                            Sentence Length Variety
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={feedback.sentenceAnalysis.sentenceLengthVariety * 100} 
                            color={feedback.sentenceAnalysis.sentenceLengthVariety > 0.7 ? "success" : "primary"}
                            sx={{ height: 10, borderRadius: 1 }}
                          />
                          <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
                            {Math.round(feedback.sentenceAnalysis.sentenceLengthVariety * 100)}% variety
                          </Typography>
                        </Box>
                        
                        <Box>
                          <Typography variant="subtitle2" gutterBottom>
                            Sentence Beginning Variety
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={feedback.sentenceAnalysis.sentenceBeginnings.variety * 100} 
                            color={feedback.sentenceAnalysis.sentenceBeginnings.variety > 0.7 ? "success" : "primary"}
                            sx={{ height: 10, borderRadius: 1 }}
                          />
                          <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
                            {Math.round(feedback.sentenceAnalysis.sentenceBeginnings.variety * 100)}% variety
                          </Typography>
                        </Box>
                        
                        {feedback.sentenceAnalysis.sentenceBeginnings.repetitivePatterns.length > 0 && (
                          <Box>
                            <Typography variant="subtitle2" color="error" gutterBottom>
                              Repetitive Beginning Patterns
                            </Typography>
                            <Box sx={{ pl: 2 }}>
                              {feedback.sentenceAnalysis.sentenceBeginnings.repetitivePatterns.map((pattern, index) => (
                                <Typography key={index} variant="body2" sx={{ mb: 0.5 }}>
                                  • {pattern}
                                </Typography>
                              ))}
                            </Box>
                          </Box>
                        )}
                      </Stack>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={7}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Sentence Improvements
                    </Typography>
                    
                    {feedback.sentenceAnalysis.improvements.length === 0 ? (
                      <Typography color="text.secondary" sx={{ py: 4, textAlign: 'center' }}>
                        No specific sentence improvements identified
                      </Typography>
                    ) : (
                      <Stack spacing={3} sx={{ mt: 2 }}>
                        {feedback.sentenceAnalysis.improvements.map((improvement, index) => (
                          <Box key={index}>
                            <Grid container spacing={2}>
                              <Grid item xs={12}>
                                <Typography variant="subtitle2" color="error.main">
                                  Original
                                </Typography>
                                <Paper variant="outlined" sx={{ p: 1.5, bgcolor: 'error.lightest', borderColor: 'error.light' }}>
                                  <Typography variant="body2">{improvement.original}</Typography>
                                </Paper>
                              </Grid>
                              <Grid item xs={12}>
                                <Typography variant="subtitle2" color="success.main">
                                  Improved
                                </Typography>
                                <Paper variant="outlined" sx={{ p: 1.5, bgcolor: 'success.lightest', borderColor: 'success.light' }}>
                                  <Typography variant="body2">{improvement.improved}</Typography>
                                </Paper>
                              </Grid>
                              <Grid item xs={12}>
                                <Typography variant="subtitle2">
                                  Explanation
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                  {improvement.explanation}
                                </Typography>
                              </Grid>
                            </Grid>
                            {index < feedback.sentenceAnalysis.improvements.length - 1 && (
                              <Divider sx={{ my: 2 }} />
                            )}
                          </Box>
                        ))}
                      </Stack>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={selectedTabValue} index={2}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
                      <Typography variant="h6">
                        Overall Structure Analysis
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="subtitle2" sx={{ mr: 1 }}>Cohesion Score:</Typography>
                        <Chip 
                          label={`${feedback.paragraphAnalysis.overallCohesion.toFixed(1)}/10`}
                          color={
                            feedback.paragraphAnalysis.overallCohesion >= 8 ? "success" :
                            feedback.paragraphAnalysis.overallCohesion >= 6 ? "warning" : "error"
                          }
                        />
                      </Box>
                    </Stack>
                    
                    <Box sx={{ mt: 3 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        Introduction
                      </Typography>
                      <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                        <Typography variant="body2">{feedback.paragraphAnalysis.introduction.content}</Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 2 }}>
                          <Chip size="small" label={`Purpose: ${feedback.paragraphAnalysis.introduction.purpose}`} />
                          <Chip 
                            size="small" 
                            label={`Coherence: ${feedback.paragraphAnalysis.introduction.coherence}/10`}
                            color={
                              feedback.paragraphAnalysis.introduction.coherence >= 8 ? "success" :
                              feedback.paragraphAnalysis.introduction.coherence >= 6 ? "warning" : "error"
                            }
                          />
                        </Box>
                        {feedback.paragraphAnalysis.introduction.topicSentence.improvement && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="caption" color="text.secondary">Topic Sentence Improvement:</Typography>
                            <Typography variant="body2" color="primary.main">
                              {feedback.paragraphAnalysis.introduction.topicSentence.improvement}
                            </Typography>
                          </Box>
                        )}
                      </Paper>
                      
                      {feedback.paragraphAnalysis.bodyParagraphs.map((para, index) => (
                        <Box key={index} sx={{ mb: 3 }}>
                          <Typography variant="subtitle1" gutterBottom>
                            Body Paragraph {index + 1}
                          </Typography>
                          <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                            <Typography variant="body2">{para.content}</Typography>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 2 }}>
                              <Chip size="small" label={`Purpose: ${para.purpose}`} />
                              <Chip 
                                size="small" 
                                label={`Coherence: ${para.coherence}/10`}
                                color={
                                  para.coherence >= 8 ? "success" :
                                  para.coherence >= 6 ? "warning" : "error"
                                }
                              />
                            </Box>
                            {para.topicSentence.improvement && (
                              <Box sx={{ mt: 2 }}>
                                <Typography variant="caption" color="text.secondary">Topic Sentence Improvement:</Typography>
                                <Typography variant="body2" color="primary.main">
                                  {para.topicSentence.improvement}
                                </Typography>
                              </Box>
                            )}
                            {para.development.missingElements && para.development.missingElements.length > 0 && (
                              <Box sx={{ mt: 2 }}>
                                <Typography variant="caption" color="error.main">Missing Elements:</Typography>
                                <ul style={{ margin: '4px 0', paddingLeft: '20px' }}>
                                  {para.development.missingElements.map((element, idx) => (
                                    <li key={idx}>
                                      <Typography variant="body2">{element}</Typography>
                                    </li>
                                  ))}
                                </ul>
                              </Box>
                            )}
                            {para.development.improvements && para.development.improvements.length > 0 && (
                              <Box sx={{ mt: 2 }}>
                                <Typography variant="caption" color="primary.main">Improvement Suggestions:</Typography>
                                <ul style={{ margin: '4px 0', paddingLeft: '20px' }}>
                                  {para.development.improvements.map((improvement, idx) => (
                                    <li key={idx}>
                                      <Typography variant="body2">{improvement}</Typography>
                                    </li>
                                  ))}
                                </ul>
                              </Box>
                            )}
                          </Paper>
                        </Box>
                      ))}
                      
                      <Typography variant="subtitle1" gutterBottom>
                        Conclusion
                      </Typography>
                      <Paper variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="body2">{feedback.paragraphAnalysis.conclusion.content}</Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 2 }}>
                          <Chip size="small" label={`Purpose: ${feedback.paragraphAnalysis.conclusion.purpose}`} />
                          <Chip 
                            size="small" 
                            label={`Coherence: ${feedback.paragraphAnalysis.conclusion.coherence}/10`}
                            color={
                              feedback.paragraphAnalysis.conclusion.coherence >= 8 ? "success" :
                              feedback.paragraphAnalysis.conclusion.coherence >= 6 ? "warning" : "error"
                            }
                          />
                        </Box>
                        {feedback.paragraphAnalysis.conclusion.topicSentence.improvement && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="caption" color="text.secondary">Concluding Statement Improvement:</Typography>
                            <Typography variant="body2" color="primary.main">
                              {feedback.paragraphAnalysis.conclusion.topicSentence.improvement}
                            </Typography>
                          </Box>
                        )}
                      </Paper>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={selectedTabValue} index={3}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Grammar Overview
                    </Typography>
                    <Stack spacing={3} sx={{ mt: 2 }}>
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Total Errors
                        </Typography>
                        <Typography variant="h4" color={
                          feedback.grammarAnalysis.totalErrors <= 3 ? "success.main" :
                          feedback.grammarAnalysis.totalErrors <= 7 ? "warning.main" : "error.main"
                        }>
                          {feedback.grammarAnalysis.totalErrors}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {feedback.grammarAnalysis.errorFrequency}
                        </Typography>
                      </Box>
                      
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Most Common Error Type
                        </Typography>
                        <Chip 
                          label={feedback.grammarAnalysis.mostFrequentErrorType} 
                          color="primary"
                          sx={{ mt: 0.5 }}
                        />
                      </Box>
                      
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Error Distribution
                        </Typography>
                        {Object.entries(feedback.grammarAnalysis.errorDistribution).map(([type, percentage], index) => (
                          <Box key={index} sx={{ mb: 1 }}>
                            <Typography variant="body2" sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                              <span>{type}</span>
                              <span>{percentage}%</span>
                            </Typography>
                            <LinearProgress 
                              variant="determinate" 
                              value={Number(percentage)} 
                              sx={{ height: 8, borderRadius: 1 }}
                            />
                          </Box>
                        ))}
                      </Box>
                      
                      <Box>
                        <Typography variant="subtitle2" color="success.main" gutterBottom>
                          Grammar Strengths
                        </Typography>
                        {feedback.grammarAnalysis.grammarStrengths.map((strength, index) => (
                          <Box key={index} sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                            <Typography variant="body2">• {strength}</Typography>
                          </Box>
                        ))}
                      </Box>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={8}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Detailed Grammar Errors
                    </Typography>
                    
                    {Object.keys(feedback.grammarAnalysis.errorsByType).length === 0 ? (
                      <Typography color="text.secondary" sx={{ py: 4, textAlign: 'center' }}>
                        No grammar errors detected
                      </Typography>
                    ) : (
                      <Box sx={{ mt: 2 }}>
                        <Accordion defaultExpanded>
                          {Object.entries(feedback.grammarAnalysis.errorsByType).map(([errorType, errors], index) => (
                            <Accordion key={index} sx={{ mb: 2 }}>
                              <AccordionSummary expandIcon={<CaretDown />}>
                                <Typography>{errorType} ({errors.length})</Typography>
                              </AccordionSummary>
                              <AccordionDetails>
                                <Stack spacing={2}>
                                  {errors.map((error, idx) => (
                                    <Box key={idx} sx={{ p: 1 }}>
                                      <Chip 
                                        size="small" 
                                        label={error.severity === 'major' ? 'Major Error' : 'Minor Error'} 
                                        color={error.severity === 'major' ? 'error' : 'warning'}
                                        sx={{ mb: 1 }}
                                      />
                                      <Grid container spacing={2}>
                                        <Grid item xs={12} md={5}>
                                          <Typography variant="subtitle2" color="error.main">
                                            Original
                                          </Typography>
                                          <Paper variant="outlined" sx={{ p: 1.5, bgcolor: 'error.lightest', borderColor: 'error.light' }}>
                                            <Typography variant="body2">{error.original}</Typography>
                                          </Paper>
                                        </Grid>
                                        <Grid item xs={12} md={5}>
                                          <Typography variant="subtitle2" color="success.main">
                                            Correction
                                          </Typography>
                                          <Paper variant="outlined" sx={{ p: 1.5, bgcolor: 'success.lightest', borderColor: 'success.light' }}>
                                            <Typography variant="body2">{error.correction}</Typography>
                                          </Paper>
                                        </Grid>
                                        <Grid item xs={12}>
                                          <Typography variant="subtitle2">
                                            Explanation
                                          </Typography>
                                          <Typography variant="body2" color="text.secondary">
                                            {error.explanation}
                                          </Typography>
                                        </Grid>
                                      </Grid>
                                    </Box>
                                  ))}
                                </Stack>
                              </AccordionDetails>
                            </Accordion>
                          ))}
                        </Accordion>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={selectedTabValue} index={4}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={5}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Vocabulary Metrics
                    </Typography>
                    <Stack spacing={3} sx={{ mt: 2 }}>
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Lexical Diversity
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Box sx={{ position: 'relative', display: 'inline-flex', mr: 2 }}>
                            <CircularProgress
                              variant="determinate"
                              value={feedback.vocabularyAnalysis.lexicalDiversity.diversityScore * 100}
                              color={
                                feedback.vocabularyAnalysis.lexicalDiversity.diversityScore >= 0.7 ? "success" :
                                feedback.vocabularyAnalysis.lexicalDiversity.diversityScore >= 0.5 ? "warning" : "error"
                              }
                              size={60}
                              thickness={5}
                            />
                            <Box
                              sx={{
                                top: 0,
                                left: 0,
                                bottom: 0,
                                right: 0,
                                position: 'absolute',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}
                            >
                              <Typography variant="caption" component="div" fontWeight="bold">
                                {Math.round(feedback.vocabularyAnalysis.lexicalDiversity.diversityScore * 100)}%
                              </Typography>
                            </Box>
                          </Box>
                          <Box>
                            <Typography variant="body2">
                              {feedback.vocabularyAnalysis.lexicalDiversity.uniqueWords} unique words
                            </Typography>
                            <Typography variant="body2">
                              {feedback.vocabularyAnalysis.lexicalDiversity.totalWords} total words
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                      
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Vocabulary Level Distribution
                        </Typography>
                        <Grid container spacing={1}>
                          <Grid item xs={4}>
                            <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'error.lightest', borderRadius: 1 }}>
                              <Typography variant="h6">{feedback.vocabularyAnalysis.vocabularyLevel.distribution.basic}</Typography>
                              <Typography variant="caption">Basic</Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={4}>
                            <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'warning.lightest', borderRadius: 1 }}>
                              <Typography variant="h6">{feedback.vocabularyAnalysis.vocabularyLevel.distribution.intermediate}</Typography>
                              <Typography variant="caption">Intermediate</Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={4}>
                            <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'success.lightest', borderRadius: 1 }}>
                              <Typography variant="h6">{feedback.vocabularyAnalysis.vocabularyLevel.distribution.advanced}</Typography>
                              <Typography variant="caption">Advanced</Typography>
                            </Box>
                          </Grid>
                        </Grid>
                      </Box>
                      
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Sample Words by Level
                        </Typography>
                        <Grid container spacing={1}>
                          <Grid item xs={4}>
                            <Typography variant="caption" color="text.secondary">Basic</Typography>
                            <Box sx={{ pl: 1 }}>
                              {feedback.vocabularyAnalysis.vocabularyLevel.basic.slice(0, 5).map((word, index) => (
                                <Typography key={index} variant="body2">• {word}</Typography>
                              ))}
                            </Box>
                          </Grid>
                          <Grid item xs={4}>
                            <Typography variant="caption" color="text.secondary">Intermediate</Typography>
                            <Box sx={{ pl: 1 }}>
                              {feedback.vocabularyAnalysis.vocabularyLevel.intermediate.slice(0, 5).map((word, index) => (
                                <Typography key={index} variant="body2">• {word}</Typography>
                              ))}
                            </Box>
                          </Grid>
                          <Grid item xs={4}>
                            <Typography variant="caption" color="text.secondary">Advanced</Typography>
                            <Box sx={{ pl: 1 }}>
                              {feedback.vocabularyAnalysis.vocabularyLevel.advanced.slice(0, 5).map((word, index) => (
                                <Typography key={index} variant="body2">• {word}</Typography>
                              ))}
                            </Box>
                          </Grid>
                        </Grid>
                      </Box>
                      
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Topic-Relevant Vocabulary
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {feedback.vocabularyAnalysis.topicRelevantVocabulary.map((word, index) => (
                            <Chip key={index} label={word} size="small" color="primary" variant="outlined" />
                          ))}
                        </Box>
                      </Box>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={7}>
                <Stack spacing={3}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Overused Words & Better Alternatives
                      </Typography>
                      
                      {feedback.vocabularyAnalysis.overusedWords.length === 0 ? (
                        <Typography color="text.secondary" sx={{ py: 2, textAlign: 'center' }}>
                          No significantly overused words detected
                        </Typography>
                      ) : (
                        <TableContainer>
                          <Table size="small">
                            <TableHead>
                              <TableRow>
                                <TableCell>Word</TableCell>
                                <TableCell align="center">Count</TableCell>
                                <TableCell>Alternatives</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {feedback.vocabularyAnalysis.overusedWords.map((word, index) => (
                                <TableRow key={index}>
                                  <TableCell>{word.word}</TableCell>
                                  <TableCell align="center">{word.count}</TableCell>
                                  <TableCell>
                                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                      {word.alternatives.map((alt, idx) => (
                                        <Chip key={idx} label={alt} size="small" color="primary" variant="outlined" />
                                      ))}
                                    </Box>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      )}
                    </CardContent>
                  </Card>
                  
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Collocation Improvements
                      </Typography>
                      
                      {feedback.vocabularyAnalysis.collocations.length === 0 ? (
                        <Typography color="text.secondary" sx={{ py: 2, textAlign: 'center' }}>
                          No collocation errors detected
                        </Typography>
                      ) : (
                        <Stack spacing={2} sx={{ mt: 1 }}>
                          {feedback.vocabularyAnalysis.collocations.map((coll, index) => (
                            <Paper key={index} variant="outlined" sx={{ p: 1.5 }}>
                              <Grid container spacing={2}>
                                <Grid item xs={12} sm={5}>
                                  <Typography variant="subtitle2" color="error.main">Incorrect</Typography>
                                  <Typography variant="body2">{coll.incorrect}</Typography>
                                </Grid>
                                <Grid item xs={12} sm={5}>
                                  <Typography variant="subtitle2" color="success.main">Correct</Typography>
                                  <Typography variant="body2">{coll.correct}</Typography>
                                </Grid>
                                <Grid item xs={12}>
                                  <Typography variant="subtitle2">Context</Typography>
                                  <Typography variant="body2" color="text.secondary">{coll.context}</Typography>
                                </Grid>
                              </Grid>
                            </Paper>
                          ))}
                        </Stack>
                      )}
                    </CardContent>
                  </Card>
                  
                  <Card variant="outlined">
                    <CardContent>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="h6" gutterBottom>
                            Transition Words Used
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {feedback.vocabularyAnalysis.transitionWords.used.map((word, index) => (
                              <Chip key={index} label={word} size="small" color="primary" variant="outlined" />
                            ))}
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="h6" gutterBottom>
                            Suggested Transitions
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {feedback.vocabularyAnalysis.transitionWords.suggestions.map((word, index) => (
                              <Chip key={index} label={word} size="small" color="success" variant="outlined" />
                            ))}
                          </Box>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                  
                  <Card variant="outlined">
                    <CardContent>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="h6" gutterBottom>
                            Idioms & Phrases Used
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {feedback.vocabularyAnalysis.idiomsAndPhrases.used.length === 0 ? (
                              <Typography color="text.secondary">No idioms detected</Typography>
                            ) : (
                              feedback.vocabularyAnalysis.idiomsAndPhrases.used.map((phrase, index) => (
                                <Chip key={index} label={phrase} size="small" color="primary" variant="outlined" />
                              ))
                            )}
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="h6" gutterBottom>
                            Suggested Idioms & Phrases
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {feedback.vocabularyAnalysis.idiomsAndPhrases.suggestions.map((phrase, index) => (
                              <Chip key={index} label={phrase} size="small" color="success" variant="outlined" />
                            ))}
                          </Box>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Stack>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={selectedTabValue} index={5}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Priority Areas for Improvement
                    </Typography>
                    <List sx={{ mt: 1 }}>
                      {feedback.detailedImprovementPlan.priorityAreas.map((area, index) => (
                        <ListItem key={index} sx={{ px: 1, py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: '36px' }}>
                            <Typography variant="h6" color="primary.main">{index + 1}</Typography>
                          </ListItemIcon>
                          <ListItemText primary={area} />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Recommended Resources
                    </Typography>
                    <List sx={{ mt: 1 }}>
                      {feedback.detailedImprovementPlan.resourceSuggestions.map((resource, index) => (
                        <ListItem key={index} sx={{ px: 1, py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: '36px' }}>
                            <BookOpen size={20} />
                          </ListItemIcon>
                          <ListItemText primary={resource} />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Specific Exercises
                    </Typography>
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell width="5%">#</TableCell>
                            <TableCell>Exercise</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {feedback.detailedImprovementPlan.specificExercises.map((exercise, index) => (
                            <TableRow key={index}>
                              <TableCell>{index + 1}</TableCell>
                              <TableCell>
                                <Typography variant="body2">{exercise}</Typography>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Comparative Analysis with Higher Band Scores
                    </Typography>
                    <Paper variant="outlined" sx={{ p: 2, bgcolor: 'primary.lightest' }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Band Example
                      </Typography>
                      <Typography variant="body2">
                        {feedback.comparativeAnalysis.bandExample}
                      </Typography>
                    </Paper>
                    
                    <Stack spacing={2} sx={{ mt: 3 }}>
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Structural Differences
                        </Typography>
                        <List disablePadding>
                          {feedback.comparativeAnalysis.structuralDifferences.map((diff, index) => (
                            <ListItem key={index} sx={{ py: 0.5 }}>
                              <ListItemIcon sx={{ minWidth: '32px' }}>
                                <ChartLineUp size={18} />
                              </ListItemIcon>
                              <ListItemText primary={diff} />
                            </ListItem>
                          ))}
                        </List>
                      </Box>
                      
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Argumentation Improvements
                        </Typography>
                        <List disablePadding>
                          {feedback.comparativeAnalysis.argumentationImprovements.map((imp, index) => (
                            <ListItem key={index} sx={{ py: 0.5 }}>
                              <ListItemIcon sx={{ minWidth: '32px' }}>
                                <Lightbulb size={18} />
                              </ListItemIcon>
                              <ListItemText primary={imp} />
                            </ListItem>
                          ))}
                        </List>
                      </Box>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>
        </CardContent>
      </Card>
    );
  };

  // Render the essay dialog for adding/editing
  const renderAddEssayDialog = () => {
    return (
      <Dialog open={showAddEssayDialog} onClose={handleCloseAddEssayDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingEssay.id ? 'Edit Essay' : 'Add New Essay'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 0 }}>
            <Grid item xs={12}>
              <TextField
                label="Essay Name"
                value={editingEssay.name}
                onChange={(e) => setEditingEssay({ ...editingEssay, name: e.target.value })}
                fullWidth
                required
                error={!editingEssay.name}
                helperText={!editingEssay.name ? 'Essay name is required' : ''}
              />
            </Grid>
            <Grid item xs={12}>
              <Stack direction="row" spacing={1} alignItems="flex-start">
                <TextField
                  label="Task Question"
                  value={editingEssay.taskQuestion}
                  onChange={(e) => setEditingEssay({ ...editingEssay, taskQuestion: e.target.value })}
                  fullWidth
                  multiline
                  rows={3}
                  required
                  error={!editingEssay.taskQuestion}
                  helperText={!editingEssay.taskQuestion ? 'Task question is required' : ''}
                />
                <Button
                  variant="outlined"
                  onClick={handleOpenTaskImageDialog}
                  startIcon={<UploadSimple />}
                  sx={{ minWidth: '120px', height: '56px' }}
                >
                  {editingEssay.taskImageFile ? 'Change' : 'Upload'}
                </Button>
              </Stack>
              {editingEssay.taskImageFile && (
                <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                  Task image: {editingEssay.taskImageFile.name}
                </Typography>
              )}
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Essay Text"
                value={editingEssay.text}
                onChange={(e) => setEditingEssay({ ...editingEssay, text: e.target.value })}
                fullWidth
                multiline
                rows={15}
                required
                error={!editingEssay.text}
                helperText={!editingEssay.text ? 'Essay text is required' : ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAddEssayDialog}>Cancel</Button>
          <Button onClick={handleAddEssay} variant="contained" disabled={!editingEssay.name || !editingEssay.text || !editingEssay.taskQuestion}>
            {editingEssay.id ? 'Save Changes' : 'Add Essay'}
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Render the task image upload dialog
  const renderTaskImageDialog = () => {
    return (
      <Dialog open={showTaskImageDialog} onClose={handleCloseTaskImageDialog}>
        <DialogTitle>Upload Task Image</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Upload an image of the task chart, graph, or diagram for Task 1.
              Supported formats: JPG, PNG
            </Typography>
            <Button
              variant="outlined"
              component="label"
              startIcon={<UploadSimple />}
              sx={{ mt: 2 }}
              fullWidth
            >
              Select Image File
              <input
                type="file"
                hidden
                accept="image/png, image/jpeg"
                onChange={handleTaskImageFileChange}
              />
            </Button>
            {editingEssay.taskImageFile && (
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  Selected file: {editingEssay.taskImageFile.name}
                </Typography>
                <Box 
                  component="img" 
                  src={URL.createObjectURL(editingEssay.taskImageFile)} 
                  alt="Task Preview"
                  sx={{ 
                    maxWidth: '100%', 
                    maxHeight: '200px', 
                    border: '1px solid', 
                    borderColor: 'divider',
                    borderRadius: 1 
                  }}
                />
              </Box>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          {editingEssay.taskImageFile && (
            <Button 
              onClick={() => setEditingEssay({ ...editingEssay, taskImageFile: null })}
              color="error"
            >
              Remove
            </Button>
          )}
          <Button onClick={handleCloseTaskImageDialog}>Cancel</Button>
          <Button 
            onClick={handleCloseTaskImageDialog} 
            variant="contained" 
            disabled={!editingEssay.taskImageFile}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Export menu handlers
  const handleOpenExportMenu = (event: React.MouseEvent<HTMLElement>) => {
    setExportMenuAnchorEl(event.currentTarget);
  };
  
  const handleCloseExportMenu = () => {
    setExportMenuAnchorEl(null);
  };
  
  // Apply global task question to all essays
  const applyGlobalTaskQuestion = () => {
    if (!globalTaskQuestion.trim()) {
      setError('Please enter a task question first');
      return;
    }

    setEssays(prev => 
      prev.map(essay => ({
        ...essay,
        taskQuestion: globalTaskQuestion
      }))
    );
    setError(null);
  };
  
  // Export all completed essays as PDFs
  const exportAllPdfs = () => {
    const completedEssays = essays.filter(essay => essay.status === 'completed');
    if (completedEssays.length === 0) {
      setError('No completed essays to export');
      return;
    }
    
    // Generate PDF for each completed essay
    completedEssays.forEach(essay => {
      generatePDF(essay);
    });
    
    handleCloseExportMenu();
  };

  // Export as JSON
  const exportAsJson = () => {
    const completedEssays = essays.filter(essay => essay.status === 'completed');
    if (completedEssays.length === 0) {
      setError('No completed essays to export');
      return;
    }

    const exportData = {
      date: new Date().toISOString(),
      model,
      essays: completedEssays.map(essay => ({
        name: essay.name,
        text: essay.text,
        feedback: essay.feedback
      }))
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bulk-detailed-essays-results-${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    handleCloseExportMenu();
  };
  
  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // Initialize progress tracking for all files
    const initialProgress: {[key: string]: number} = {};
    Array.from(files).forEach(file => {
      initialProgress[file.name] = 0;
    });
    setFileProcessingProgress(initialProgress);

    // Process each file based on its type
    for (const file of Array.from(files)) {
      const fileId = generateId();
      const fileName = file.name;
      const fileExt = fileName.split('.').pop()?.toLowerCase() || '';
      
      // Add a pending essay while we process the file
      const pendingEssay: Essay = {
        id: fileId,
        name: fileName,
        text: "Processing...",
        taskQuestion: globalTaskQuestion,
        status: 'pending'
      };
      
      setEssays(prev => [...prev, pendingEssay]);
      
      try {
        let extractedText = '';
        
        // Process different file types
        if (fileExt === 'txt') {
          // Handle text files
          extractedText = await readTextFile(file);
        } else if (['docx', 'doc'].includes(fileExt)) {
          // Handle Word documents (would require mammoth.js or similar library)
          extractedText = "Word document processing is not fully implemented yet. Please convert to TXT or copy/paste content directly.";
        } else if (['jpg', 'jpeg', 'png', 'bmp', 'tiff'].includes(fileExt)) {
          // Handle image files with OCR
          setFileProcessingProgress(prev => ({...prev, [fileName]: 10}));
          
          const result = await Tesseract.recognize(
            file,
            'eng',
            { 
              logger: m => {
                if (m.status === 'recognizing text') {
                  setFileProcessingProgress(prev => ({
                    ...prev, 
                    [fileName]: Math.round(10 + m.progress * 80)
                  }));
                }
              }
            }
          );
          
          extractedText = result.data.text;
          setFileProcessingProgress(prev => ({...prev, [fileName]: 100}));
        } else if (fileExt === 'pdf') {
          // Handle PDF files
          setFileProcessingProgress(prev => ({...prev, [fileName]: 20}));
          
          const fileData = new Uint8Array(await file.arrayBuffer());
          const pdf = await pdfjs.getDocument({data: fileData}).promise;
          
          let fullText = '';
          
          // Loop through each page
          for (let i = 1; i <= pdf.numPages; i++) {
            setFileProcessingProgress(prev => ({
              ...prev, 
              [fileName]: Math.round(20 + (i / pdf.numPages) * 70)
            }));
            
            const page = await pdf.getPage(i);
            const textContent = await page.getTextContent();
            
            // Extract text from the page
            const pageText = textContent.items
              .map((item: any) => item.str)
              .join(' ');
              
            fullText += pageText + '\n\n';
          }
          
          extractedText = fullText;
          setFileProcessingProgress(prev => ({...prev, [fileName]: 100}));
        } else {
          // Unsupported file type
          throw new Error(`Unsupported file type: ${fileExt}`);
        }
        
        // Update the essay with the extracted text
        setEssays(prev => 
          prev.map(essay => 
            essay.id === fileId 
              ? { 
                  ...essay, 
                  text: extractedText,
                  status: 'pending'
                } 
              : essay
          )
        );
      } catch (error) {
        console.error(`Error processing file ${fileName}:`, error);
        
        // Update the essay with the error
        setEssays(prev => 
          prev.map(essay => 
            essay.id === fileId 
              ? { 
                  ...essay, 
                  text: "Error processing file. Please try again.",
                  status: 'error',
                  error: 'File processing failed'
                } 
              : essay
          )
        );
      }
    }

    // Clear the input
    event.target.value = '';
  };
  
  // Helper function to read text from a file
  const readTextFile = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          resolve(e.target.result as string);
        } else {
          reject(new Error('Failed to read file'));
        }
      };
      reader.onerror = () => reject(new Error('File reading error'));
      reader.readAsText(file);
    });
  };

  return (
    <Box component="main" sx={{ flexGrow: 1, py: 4 }}>
      <Box sx={{ px: { xs: 2, md: 3 } }}>
        <Stack spacing={3}>
          <Stack direction="row" alignItems="center" spacing={1}>
            <Files size={24} />
            <Typography variant="h4">Bulk Detailed Writing Check</Typography>
          </Stack>
          <Alert severity="info">
            Detailed Writing Check analyzes multiple essays with in-depth feedback. Download comprehensive PDF reports 
            with detailed grammar, vocabulary, and sentence structure analysis. Perfect for IELTS Task 1 and 2 essays.
          </Alert>
          
          <Stack direction="row" spacing={2} alignItems="center">
            <Typography variant="subtitle2">Model:</Typography>
            <ToggleButtonGroup
              value={model}
              exclusive
              onChange={handleModelChange}
              size="small"
            >
              <ToggleButton value="gpt-4o">GPT-4o</ToggleButton>
            </ToggleButtonGroup>
          </Stack>
          
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
                        <Button 
                          variant="contained" 
                          startIcon={<Plus />}
                          onClick={handleOpenAddEssayDialog}
                        >
                          Add Essay
                        </Button>
                        <Button 
                          component="label" 
                          variant="outlined" 
                          startIcon={<UploadSimple />}
                        >
                          Upload Files
                          <input
                            type="file"
                            hidden
                            multiple
                            accept=".txt,.docx,.md,.rtf,.pdf,.jpg,.jpeg,.png,.bmp,.tiff"
                            onChange={handleFileUpload}
                          />
                        </Button>
                        <Button 
                          variant="outlined" 
                          startIcon={<Download />}
                          onClick={handleOpenExportMenu}
                          disabled={!hasCompletedEssays}
                          aria-controls="export-menu"
                          aria-haspopup="true"
                        >
                          Export Results
                        </Button>
                        <Menu
                          id="export-menu"
                          anchorEl={exportMenuAnchorEl}
                          open={Boolean(exportMenuAnchorEl)}
                          onClose={handleCloseExportMenu}
                        >
                          <MenuItem onClick={exportAllPdfs}>
                            <ListItemIcon>
                              <FilePdf />
                            </ListItemIcon>
                            <ListItemText>Export all as PDF</ListItemText>
                          </MenuItem>
                          <MenuItem onClick={exportAsJson}>
                            <ListItemIcon>
                              <FileText />
                            </ListItemIcon>
                            <ListItemText>Export as JSON</ListItemText>
                          </MenuItem>
                        </Menu>
                        <Box sx={{ flexGrow: 1 }} />
                        <Button 
                          variant="contained" 
                          color="primary" 
                          startIcon={<ArrowsClockwise />}
                          disabled={isProcessing || essays.length === 0 || !essays.some(e => e.status === 'pending')}
                          onClick={processEssays}
                        >
                          {isProcessing ? (
                            <>
                              <CircularProgress size={24} color="inherit" sx={{ mr: 1 }} />
                              Processing...
                            </>
                          ) : 'Check Essays'}
                        </Button>
                      </Stack>
                    </Grid>

                    {/* Global Task Question Section */}
                    <Grid item xs={12}>
                      <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
                        <Typography variant="subtitle1" gutterBottom>
                          Global Task Question
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          Set a common task question for all essays. This will be applied to any essay without a specific task question.
                        </Typography>
                        <Grid container spacing={2} alignItems="flex-start">
                          <Grid item xs={12} md={9}>
                            <TextField
                              fullWidth
                              label="Task Question"
                              placeholder="Enter the IELTS task question that applies to all essays..."
                              value={globalTaskQuestion}
                              onChange={(e) => setGlobalTaskQuestion(e.target.value)}
                              multiline
                              rows={2}
                            />
                          </Grid>
                          <Grid item xs={12} md={3}>
                            <Button 
                              variant="contained" 
                              fullWidth 
                              onClick={applyGlobalTaskQuestion}
                              disabled={!globalTaskQuestion.trim()}
                              sx={{ height: '56px' }}
                            >
                              Apply to All Essays
                            </Button>
                          </Grid>
                        </Grid>
                      </Paper>
                    </Grid>

                    {error && (
                      <Grid item xs={12}>
                        <Alert severity="error" onClose={() => setError(null)}>
                          {error}
                        </Alert>
                      </Grid>
                    )}
                    
                    {/* Essays Table */}
                    <Grid item xs={12}>
                      <Typography variant="h6" sx={{ mb: 2 }}>Essays ({essays.length})</Typography>
                      {essays.length === 0 ? (
                        <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                          <Typography color="text.secondary">
                            No essays added yet. Add essays or upload files to get started.
                          </Typography>
                        </Paper>
                      ) : (
                        renderEssayList()
                      )}
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {selectedEssayId && (
              <Grid item xs={12}>
                {renderEssayDetail()}
              </Grid>
            )}
          </Grid>
        </Stack>
      </Box>

      {renderAddEssayDialog()}
      {renderTaskImageDialog()}
    </Box>
  );
} 
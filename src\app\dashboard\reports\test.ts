import { calculateOverallBand } from '@/lib/report-utils';

/**
 * Test the calculateOverallBand function
 */
function testCalculateOverallBand() {
  // Test with all scores present
  const test1 = calculateOverallBand(7.0, 6.5, 6.0, 7.5);
  console.assert(test1 === 6.5, `Expected 6.5, got ${test1}`);

  // Test with some scores missing
  const test2 = calculateOverallBand(7.0, null, 6.0, null);
  console.assert(test2 === 6.5, `Expected 6.5, got ${test2}`);

  // Test with all scores missing
  const test3 = calculateOverallBand(null, null, null, null);
  console.assert(test3 === null, `Expected null, got ${test3}`);

  // Test rounding to nearest 0.5
  const test4 = calculateOverallBand(6.1, 6.2, 6.3, 6.4);
  console.assert(test4 === 6.5, `Expected 6.5, got ${test4}`);

  console.log('All tests passed!');
}

// Run the tests
testCalculateOverallBand();

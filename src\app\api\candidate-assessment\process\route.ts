import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { processWritingEntry } from '@/lib/writing-utils';
import { processSpeakingEntry } from '@/lib/speaking-utils';
import { processReadingEntrySimple } from '@/lib/simple-reading-utils';
import { processListeningEntrySimple } from '@/lib/simple-listening-utils';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { entryId, componentType, model = 'gpt-4o', correctAnswersText } = body;

    console.log('Processing component:', { componentType, entryId, model });

    // Validate input
    if (!entryId || !componentType) {
      return NextResponse.json(
        { error: 'Entry ID and component type are required' },
        { status: 400 }
      );
    }

    let result;

    // Process different component types
    switch (componentType) {
      case 'listening':
        // Get the listening entry
        const listeningEntry = await prisma.listeningEntry.findUnique({
          where: { id: entryId }
        });

        if (!listeningEntry) {
          return NextResponse.json(
            { error: 'Listening entry not found' },
            { status: 404 }
          );
        }

        // Process the listening entry with simplified approach
        console.log('Processing listening entry:', {
          materialId: listeningEntry.materialId,
          materialTitle: listeningEntry.materialTitle
        });

        // Add correct answers to the entry's metadata if provided
        if (correctAnswersText) {
          // Create metadata object if it doesn't exist
          if (!listeningEntry.metadata) {
            listeningEntry.metadata = {};
          }
          // Add correct answers to metadata using type assertion
          (listeningEntry.metadata as any).globalCorrectAnswers = correctAnswersText;
          console.log('Using provided global correct answers for listening:', correctAnswersText);
        }
        // Check if we have correct answers in the metadata
        else if (listeningEntry.metadata && typeof listeningEntry.metadata === 'object' &&
                 listeningEntry.metadata !== null && 'globalCorrectAnswers' in (listeningEntry.metadata as any)) {
          console.log('Using metadata correct answers for listening:', (listeningEntry.metadata as any).globalCorrectAnswers);
        }

        result = await processListeningEntrySimple(listeningEntry);
        break;

      case 'reading':
        // Get the reading entry
        const readingEntry = await prisma.readingEntry.findUnique({
          where: { id: entryId }
        });

        if (!readingEntry) {
          return NextResponse.json(
            { error: 'Reading entry not found' },
            { status: 404 }
          );
        }

        // Process the reading entry with simplified approach
        console.log('Processing reading entry:', {
          materialId: readingEntry.materialId,
          materialTitle: readingEntry.materialTitle
        });

        // Add correct answers to the entry's metadata if provided
        if (correctAnswersText) {
          // Create metadata object if it doesn't exist
          if (!readingEntry.metadata) {
            readingEntry.metadata = {};
          }
          // Add correct answers to metadata using type assertion
          (readingEntry.metadata as any).globalCorrectAnswers = correctAnswersText;
          console.log('Using provided global correct answers for reading:', correctAnswersText);
        }
        // Check if we have correct answers in the metadata
        else if (readingEntry.metadata && typeof readingEntry.metadata === 'object' &&
                 readingEntry.metadata !== null && 'globalCorrectAnswers' in (readingEntry.metadata as any)) {
          console.log('Using metadata correct answers for reading:', (readingEntry.metadata as any).globalCorrectAnswers);
        }

        result = await processReadingEntrySimple(readingEntry);
        break;

      case 'writing_task1':
      case 'writing_task2':
        // Get the writing entry
        const writingEntry = await prisma.writingEntry.findUnique({
          where: { id: entryId },
          include: { material: true } // Keep this for writing entries
        });

        if (!writingEntry) {
          return NextResponse.json(
            { error: 'Writing entry not found' },
            { status: 404 }
          );
        }

        // Process the writing entry
        result = await processWritingEntry(writingEntry, model);
        break;

      case 'speaking':
        try {
          // Get the speaking entry
          const speakingEntry = await prisma.speakingEntry.findUnique({
            where: { id: entryId },
          });

          if (!speakingEntry) {
            return NextResponse.json(
              { error: 'Speaking entry not found' },
              { status: 404 }
            );
          }

          console.log('Found speaking entry:', {
            id: speakingEntry.id,
            studentId: speakingEntry.studentId,
            hasAudio: !!speakingEntry.audioUrl,
            hasTranscription: !!speakingEntry.transcription,
            partNumber: speakingEntry.partNumber
          });

          // Validate that we have either audio or transcription
          if (!speakingEntry.audioUrl && (!speakingEntry.transcription || speakingEntry.transcription.trim() === '')) {
            return NextResponse.json(
              { error: 'Speaking entry must have either audio or transcription' },
              { status: 400 }
            );
          }

          // Process the speaking entry
          result = await processSpeakingEntry(speakingEntry, model);
        } catch (speakingError) {
          console.error('Error processing speaking entry:', speakingError);
          return NextResponse.json(
            {
              error: 'Failed to process speaking entry',
              details: speakingError instanceof Error ? speakingError.message : 'Unknown error'
            },
            { status: 500 }
          );
        }
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid component type' },
          { status: 400 }
        );
    }

    // Ensure we're returning the band score and other important fields
    // Use type assertion to handle different result types
    const resultAny = result as any;

    const response = {
      ...result,
      band: resultAny.band || null,
      score: resultAny.score !== undefined ? resultAny.score : null,
      correctAnswers: resultAny.correctAnswers !== undefined ? resultAny.correctAnswers : null,
      totalQuestions: resultAny.totalQuestions !== undefined ? resultAny.totalQuestions : null,
      strengths: resultAny.strengths || [],
      weaknesses: resultAny.weaknesses || [],
      improvementSuggestions: resultAny.improvementSuggestions || []
    };

    console.log('Returning response with band score:', response.band);

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error processing component:', error);

    // Provide more detailed error information
    let errorMessage = 'Failed to process component';
    let errorDetails = {};

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = { stack: error.stack };
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: errorDetails,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

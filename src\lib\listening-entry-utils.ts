import { prisma } from '@/lib/db';
import { convertListeningRawScoreToBand, generateStandardizedFeedback } from './score-conversion';

/**
 * Get all listening entries for a student
 *
 * @param studentId - The student ID
 * @returns Array of listening entries
 */
export async function getStudentListeningEntries(studentId: string) {
  return prisma.listeningEntry.findMany({
    where: { studentId },
    orderBy: { createdAt: 'desc' }
  });
}

/**
 * Get a listening entry by ID
 *
 * @param id - The listening entry ID
 * @returns The listening entry or null if not found
 */
export async function getListeningEntryById(id: string) {
  return prisma.listeningEntry.findUnique({
    where: { id }
  });
}

/**
 * Create a new listening entry
 *
 * @param data - The listening entry data
 * @returns The created listening entry
 */
export async function createListeningEntry(data: {
  studentId: string;
  materialId: string;
  answers: any;
  imageUrl?: string;
  score?: number;
  band?: number;
  correctAnswers?: number;
  totalQuestions?: number;
  mistakes?: any;
  strengths?: any;
  weaknesses?: any;
  improvementSuggestions?: any;
  raw_score?: string;
}) {
  return prisma.listeningEntry.create({
    data
  });
}

/**
 * Create a new listening entry with simplified approach using raw score
 *
 * @param data - The listening entry data with raw score
 * @returns The created listening entry
 */
export async function createListeningEntryWithRawScore(data: {
  studentId: string;
  materialId?: string;
  materialTitle?: string;
  raw_score: string;
  imageUrl?: string;
}) {
  // Calculate band score from raw score
  const band = convertListeningRawScoreToBand(data.raw_score);

  // Generate standardized feedback
  const feedback = generateStandardizedFeedback(band, 'listening');

  // Extract score values
  let score = 0;
  let totalQuestions = 40;

  if (data.raw_score.includes('/')) {
    const [correct, total] = data.raw_score.split('/').map(s => parseInt(s.trim(), 10));
    score = correct;
    totalQuestions = total || 40;
  } else {
    score = parseInt(data.raw_score.trim(), 10);
  }

  // Create entry with calculated values
  return prisma.listeningEntry.create({
    data: {
      studentId: data.studentId,
      materialId: data.materialId,
      materialTitle: data.materialTitle || 'Global Listening Test',
      imageUrl: data.imageUrl,
      raw_score: data.raw_score,
      score,
      band,
      correctAnswers: score,
      totalQuestions,
      answers: [], // Empty array as we're not using answer checking
      strengths: feedback.strengths,
      weaknesses: feedback.weaknesses,
      improvementSuggestions: feedback.improvements
    }
  });
}

/**
 * Update a listening entry
 *
 * @param id - The listening entry ID
 * @param data - The updated listening entry data
 * @returns The updated listening entry
 */
export async function updateListeningEntry(
  id: string,
  data: {
    answers?: any;
    imageUrl?: string;
    score?: number;
    band?: number;
    correctAnswers?: number;
    totalQuestions?: number;
    mistakes?: any;
    strengths?: any;
    weaknesses?: any;
    improvementSuggestions?: any;
    raw_score?: string;
  }
) {
  return prisma.listeningEntry.update({
    where: { id },
    data
  });
}

/**
 * Update a listening entry with raw score
 *
 * @param id - The listening entry ID
 * @param raw_score - The raw score in format "30/40"
 * @returns The updated listening entry
 */
export async function updateListeningEntryWithRawScore(
  id: string,
  raw_score: string
) {
  // Calculate band score from raw score
  const band = convertListeningRawScoreToBand(raw_score);

  // Generate standardized feedback
  const feedback = generateStandardizedFeedback(band, 'listening');

  // Extract score values
  let score = 0;
  let totalQuestions = 40;

  if (raw_score.includes('/')) {
    const [correct, total] = raw_score.split('/').map(s => parseInt(s.trim(), 10));
    score = correct;
    totalQuestions = total || 40;
  } else {
    score = parseInt(raw_score.trim(), 10);
  }

  // Update entry with calculated values
  return prisma.listeningEntry.update({
    where: { id },
    data: {
      raw_score,
      score,
      band,
      correctAnswers: score,
      totalQuestions,
      strengths: feedback.strengths,
      weaknesses: feedback.weaknesses,
      improvementSuggestions: feedback.improvements
    }
  });
}

/**
 * Delete a listening entry
 *
 * @param id - The listening entry ID
 * @returns The deleted listening entry
 */
export async function deleteListeningEntry(id: string) {
  return prisma.listeningEntry.delete({
    where: { id },
  });
}

/**
 * Calculate band score based on raw score and total questions
 *
 * @param score - The raw score (number of correct answers)
 * @param totalQuestions - The total number of questions
 * @returns The band score (0-9)
 * @deprecated Use convertListeningRawScoreToBand from score-conversion.ts instead
 */
export function calculateBandScore(score: number, totalQuestions: number): number {
  const percentage = (score / totalQuestions) * 100;

  // IELTS Listening band score conversion (approximate)
  if (percentage >= 90) return 9.0;
  if (percentage >= 85) return 8.5;
  if (percentage >= 80) return 8.0;
  if (percentage >= 75) return 7.5;
  if (percentage >= 70) return 7.0;
  if (percentage >= 65) return 6.5;
  if (percentage >= 60) return 6.0;
  if (percentage >= 55) return 5.5;
  if (percentage >= 50) return 5.0;
  if (percentage >= 45) return 4.5;
  if (percentage >= 40) return 4.0;
  if (percentage >= 35) return 3.5;
  if (percentage >= 30) return 3.0;
  if (percentage >= 25) return 2.5;
  if (percentage >= 20) return 2.0;
  if (percentage >= 15) return 1.5;
  if (percentage >= 10) return 1.0;
  if (percentage >= 5) return 0.5;
  return 0.0;
}

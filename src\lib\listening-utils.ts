import { prisma } from './db';
import { compareAnswers } from './reading-utils';

// Get API key from environment variables
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

if (!OPENAI_API_KEY) {
  throw new Error('OPENAI_API_KEY environment variable is not set');
}

/**
 * Process a listening entry and generate feedback
 *
 * @param entry - The listening entry to process
 * @param model - The AI model to use for processing
 * @returns The processed listening entry
 */
export async function processListeningEntry(entry: any, model: string) {
  try {
    console.log('Processing listening entry:', entry.id);

    // Get the user's answers and correct answers
    const userAnswers = entry.answers || [];

    // Get correct answers from the entry directly (global correct answers) or material
    let correctAnswers: string[] = [];

    // First check if entry has correctAnswersText field (global correct answers)
    if (entry.correctAnswersText) {
      console.log('Global correct answers found:', entry.correctAnswersText);

      if (typeof entry.correctAnswersText === 'string') {
        // Handle case where answers are a comma-separated string
        correctAnswers = entry.correctAnswersText.split(',').map((a: string) => a.trim());
      } else if (Array.isArray(entry.correctAnswersText)) {
        correctAnswers = entry.correctAnswersText;
      }

      console.log('Processed global correct answers:', correctAnswers);
    }
    // Fallback to material answers if no global answers are provided
    else if (entry.material && entry.material.answers) {
      console.log('Falling back to listening material answers:', entry.material.answers);

      // If the material has answers, use those
      correctAnswers = Array.isArray(entry.material.answers)
        ? entry.material.answers
        : Object.values(entry.material.answers);

      console.log('Processed material correct answers:', correctAnswers);
    }

    console.log('User answers:', userAnswers);
    console.log('Correct answers:', correctAnswers);

    // Compare answers and get results
    const {
      score,
      band,
      correctAnswers: correctCount,
      totalQuestions,
      mistakes
    } = compareAnswers(userAnswers, correctAnswers);

    // Prepare data for OpenAI analysis
    const userAnswersStr = userAnswers.map((ans: string, i: number) => `${i + 1}. ${ans}`).join('\n');
    const correctAnswersStr = correctAnswers.map((ans: string, i: number) => `${i + 1}. ${ans}`).join('\n');
    const mistakesStr = mistakes.map((m: any) =>
      `Question ${m.questionNumber}: User answered "${m.userAnswer}" but correct answer is "${m.correctAnswer}"`
    ).join('\n');

    // Generate AI-powered analysis
    let aiAnalysis = null;

    try {
      console.log('Generating AI analysis for listening entry...');

      // Create prompt for OpenAI
      const prompt = `
      You are an expert IELTS Listening examiner providing detailed feedback.

      Student's Listening test results:
      - Score: ${score}/40
      - Band: ${band.toFixed(1)}
      - Correct answers: ${correctCount}/${totalQuestions}
      - Percentage correct: ${(correctCount / totalQuestions * 100).toFixed(1)}%

      User's answers:
      ${userAnswersStr}

      Correct answers:
      ${correctAnswersStr}

      Mistakes:
      ${mistakesStr}

      Based on this information, provide a detailed analysis of the student's listening performance.
      Include specific strengths, weaknesses, and improvement suggestions.
      Format your response as a JSON object with the following structure:
      {
        "analysis": "Detailed analysis of the student's performance",
        "strengths": ["Strength 1", "Strength 2", ...],
        "weaknesses": ["Weakness 1", "Weakness 2", ...],
        "improvementSuggestions": ["Suggestion 1", "Suggestion 2", ...],
        "criteriaScores": [
          {"name": "Section 1 (Social)", "score": "X/Y", "feedback": "Feedback for section 1"},
          {"name": "Section 2 (Training)", "score": "X/Y", "feedback": "Feedback for section 2"},
          {"name": "Section 3 (Academic)", "score": "X/Y", "feedback": "Feedback for section 3"},
          {"name": "Section 4 (Academic Lecture)", "score": "X/Y", "feedback": "Feedback for section 4"}
        ]
      }
      `;

      // Call OpenAI API
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENAI_API_KEY}`
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: 'system',
              content: 'You are an expert IELTS Listening examiner providing detailed feedback.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 2000,
          response_format: { type: "json_object" }
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Failed to generate AI analysis');
      }

      const data = await response.json();
      const content = data.choices[0].message.content;

      // Parse the AI response
      aiAnalysis = JSON.parse(content);
      console.log('AI analysis generated successfully');

    } catch (aiError) {
      console.error('Error generating AI analysis:', aiError);

      // Fallback to basic analysis if AI fails
      aiAnalysis = {
        analysis: "Analysis could not be generated with AI. Basic analysis provided instead.",
        strengths: [] as string[],
        weaknesses: [] as string[],
        improvementSuggestions: [] as string[],
        criteriaScores: [] as any[]
      };

      // Calculate percentage correct
      const percentCorrect = totalQuestions > 0 ? (correctCount / totalQuestions) * 100 : 0;

      if (percentCorrect >= 80) {
        aiAnalysis.strengths.push("Excellent listening comprehension");
        aiAnalysis.strengths.push("Strong ability to identify key information");
      } else if (percentCorrect >= 60) {
        aiAnalysis.strengths.push("Good understanding of main ideas");
        aiAnalysis.strengths.push("Accurate detail identification");
      } else {
        aiAnalysis.weaknesses.push("Difficulty understanding spoken English");
        aiAnalysis.weaknesses.push("Struggles with identifying key details");
      }

      // Add more specific feedback based on mistakes
      if (mistakes.length > 0) {
        aiAnalysis.weaknesses.push("Difficulty with specific question types");
      }

      aiAnalysis.improvementSuggestions = [
        "Practice listening to a variety of accents",
        "Improve note-taking techniques",
        "Focus on understanding numbers and specific details"
      ];
    }

    // Extract data from AI analysis
    const strengths = aiAnalysis.strengths || [];
    const weaknesses = aiAnalysis.weaknesses || [];
    const improvementSuggestions = aiAnalysis.improvementSuggestions || [];
    const criteriaScores = aiAnalysis.criteriaScores || [];

    // Update the entry in the database
    // Create update data without criteriaScores first
    const updateData = {
      score,
      band,
      correctAnswers: correctCount, // Store as integer, not array
      totalQuestions,
      mistakes: JSON.stringify(mistakes),
      strengths: JSON.stringify(strengths),
      weaknesses: JSON.stringify(weaknesses),
      improvementSuggestions: JSON.stringify(improvementSuggestions)
    };

    // Store criteriaScores in a variable for logging
    const criteriaScoresJson = JSON.stringify(criteriaScores);
    console.log('Generated criteriaScores:', criteriaScoresJson);

    // Update the entry without criteriaScores field
    const updatedEntry = await prisma.listeningEntry.update({
      where: { id: entry.id },
      data: updateData
    });

    return updatedEntry;
  } catch (error) {
    console.error('Error processing listening entry:', error);
    throw error;
  }
}
